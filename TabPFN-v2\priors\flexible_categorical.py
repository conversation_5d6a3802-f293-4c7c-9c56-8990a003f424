import time
import random

import torch
from torch import nn
import numpy as np
from .utils import get_batch_to_dataloader
from utils import normalize_data, nan_handling_missing_for_unknown_reason_value, nan_handling_missing_for_no_reason_value, nan_handling_missing_for_a_reason_value, to_ranking_low_mem, remove_outliers, normalize_by_used_features_f
from .utils import randomize_classes, CategoricalActivation
from .utils import uniform_int_sampler_f


time_it = False

class BalancedBinarize(nn.Module):
    '''平衡二值化处理,将张量x中的每个元素与该张量的中位数进行比较，如果元素大于中位数，则将其设置为1，否则设置为0。这种操作通常用于数据预处理，特别是在处理不平衡数据集时，可以帮助平衡正负样本的比例'''
    def __init__(self):
        super().__init__()

    def forward(self, x):
        return (x > torch.median(x)).float()

def class_sampler_f(min_, max_):
    def s():
        if random.random() > 0.5:
            return uniform_int_sampler_f(min_, max_)()
        return 2
    return s

class RegressionNormalized(nn.Module):
    '''用于对输入数据进行归一化处理。归一化处理是将数据缩放到一个特定的范围，例如[-1, 1]或标准正态分布'''
    def __init__(self):
        super().__init__()

    def forward(self, x):
        # x has shape (T,B)

        # TODO: Normalize to -1, 1 or gaussian normal
        maxima = torch.max(x, 0)[0]
        minima = torch.min(x, 0)[0]
        norm = (x - minima) / (maxima-minima)

        return norm

class MulticlassRank(nn.Module):
    '''多类排序任务'''
    def __init__(self, num_classes, ordered_p=0.5):
        super().__init__()
        self.num_classes = class_sampler_f(2, num_classes)()
        self.ordered_p = ordered_p

    def forward(self, x, max_retry=0):
        # x has shape (T,B,H)

        # CAUTION: This samples the same idx in sequence for each class boundary in a batch
        class_boundaries = torch.randint(0, x.shape[0], (self.num_classes - 1,))
        class_boundaries = x[class_boundaries].unsqueeze(1)

        d = (x > class_boundaries).sum(axis=0)
        
        # 平衡分布机制
        for _ in range(max_retry):
            # 检查分布是否均衡
            counts = torch.bincount(d.flatten().long(), minlength=self.num_classes)
            if (counts.float() / counts.sum()).min() > 0.2 / self.num_classes:
                break     
            # 如果不均衡，调整边界
            class_boundaries = torch.randint(0, x.shape[0], (self.num_classes - 1,))
            class_boundaries = x[class_boundaries].unsqueeze(1)
            d = (x > class_boundaries).sum(axis=0)

            
            # # 打印d的分布
            # print(f"d的类别分布(0-{self.num_classes-1}):")
            # for i in range(self.num_classes):
            #     count = (d == i).sum().item()
            #     print(f"类别{i}: {count}个样本 ({count/d.numel()*100:.1f}%)")

        randomized_classes = torch.rand((d.shape[1], )) > self.ordered_p
        d[:, randomized_classes] = randomize_classes(d[:, randomized_classes], self.num_classes)
        reverse_classes = torch.rand((d.shape[1],)) > 0.5
        d[:, reverse_classes] = self.num_classes - 1 - d[:, reverse_classes]
        return d

class MulticlassValue(nn.Module):
    '''多类任务，列表随机化与反转'''
    def __init__(self, num_classes, ordered_p=0.5):
        super().__init__()
        self.num_classes = class_sampler_f(2, num_classes)()
        self.classes = nn.Parameter(torch.randn(self.num_classes-1), requires_grad=False)
        self.ordered_p = ordered_p

    def forward(self, x):
        # x has shape (T,B,H)
        d = (x > (self.classes.unsqueeze(-1).unsqueeze(-1))).sum(axis=0)

        randomized_classes = torch.rand((d.shape[1],)) > self.ordered_p
        d[:, randomized_classes] = randomize_classes(d[:, randomized_classes], self.num_classes)
        reverse_classes = torch.rand((d.shape[1],)) > 0.5
        d[:, reverse_classes] = self.num_classes - 1 - d[:, reverse_classes]
        return d

class MulticlassMultiNode(nn.Module):
    def __init__(self, num_classes, ordered_p=0.5):
        super().__init__()
        self.num_classes = class_sampler_f(2, num_classes)()
        self.classes = nn.Parameter(torch.randn(num_classes-1), requires_grad=False)
        self.alt_multi_class = MulticlassValue(num_classes, ordered_p)

    def forward(self, x):
        # x has shape T, B, H
        if len(x.shape) == 2:
            return self.alt_multi_class(x)
        T = 3
        x[torch.isnan(x)] = 0.00001
        d = torch.multinomial(torch.pow(0.00001+torch.sigmoid(x[:, :, 0:self.num_classes]).reshape(-1, self.num_classes), T), 1, replacement=True).reshape(x.shape[0], x.shape[1])#.float()
        return d


class FlexibleCategorical(torch.nn.Module):
    def __init__(self, get_batch, hyperparameters, args):
        super(FlexibleCategorical, self).__init__()

        # self.h = {k: hyperparameters[k]() if callable(hyperparameters[k]) else hyperparameters[k] for k in
        #                         hyperparameters.keys()}
        self.h = {}
        skip_keys = hyperparameters['skip_func_names']
        for k in  hyperparameters.keys():
            if k in skip_keys:
                self.h[k] = hyperparameters[k]
            else:
                if callable(hyperparameters[k]):
                    try:
                        self.h[k] = hyperparameters[k]()
                    except:
                        self.h[k] = hyperparameters[k]
                else:
                    self.h[k] = hyperparameters[k]
        
        self.args = args
        self.args_passed = {**self.args}
        self.args_passed.update({'num_features': self.h['num_features_used']})
        self.get_batch = get_batch
        # print(f"==++== num_featrue_used: {self.h['num_features_used']}")

        if self.h['num_classes'] == 0:
            self.class_assigner = RegressionNormalized()
        else:
            if self.h['num_classes'] > 1 and not self.h['balanced']:
                if self.h['multiclass_type'] == 'rank':
                    self.class_assigner = MulticlassRank(self.h['num_classes']
                                                 , ordered_p=self.h['output_multiclass_ordered_p']
                                                 )
                elif self.h['multiclass_type'] == 'value':
                    self.class_assigner = MulticlassValue(self.h['num_classes']
                                                         , ordered_p=self.h['output_multiclass_ordered_p']
                                                         )
                elif self.h['multiclass_type'] == 'multi_node':
                    self.class_assigner = MulticlassMultiNode(self.h['num_classes'])
                else:
                    raise ValueError("Unknow Multiclass type")
            elif self.h['num_classes'] == 2 and self.h['balanced']:
                self.class_assigner = BalancedBinarize()
            elif self.h['num_classes'] > 2 and self.h['balanced']:
                raise NotImplementedError("Balanced multiclass training is not possible")

    def drop_for_reason(self, x, v):
        '''基于特征的缺失值注入'''
        # 创建20类无序分类采样器
        nan_prob_sampler = CategoricalActivation(ordered_p=0.0
                                                 , categorical_p=1.0
                                                 , keep_activation_size=False,
                                                 num_classes_sampler=lambda: 20)
        # 生成特征相关的缺失掩码（形状与x相同
        d = nan_prob_sampler(x)
        # TODO: Make a different ordering for each activation
        # 动态计算缺失阈值（结合多个随机因子）
        x[d < torch.rand((1,), device=x.device) * 20 * self.h['nan_prob_no_reason'] * random.random()] = v
        return x

    def drop_for_no_reason(self, x, v):
        # 生成全随机缺失掩码（形状与x相同）,动态计算缺失阈值（基于配置参数和随机因子）,应用随机缺失
        x[torch.rand(x.shape, device=self.args['device']) < random.random() * self.h['nan_prob_no_reason']] = v
        return x


    def drop_for_no_reason_perfeature(self, x):
        # 生成随机矩阵
        rand_mask = torch.rand_like(x)
        have_nan_p = np.random.uniform(self.h['prob_have_nan_value_min'], self.h['prob_have_nan_value_max'])  # 有异常值的比例
        have_nan_value_p = np.random.uniform(self.h['prob_have_nan_type_min'], self.h['prob_have_nan_type_max']) # 异常值是nan的比例
        
        # 计算各类型阈值
        total_noise = rand_mask < have_nan_p
        inf_threshold = have_nan_value_p + 0.5 * (1 - have_nan_value_p)
        
        # 创建各类型mask
        nan_mask = total_noise & (rand_mask < have_nan_value_p)
        inf_mask = total_noise & (rand_mask >= have_nan_value_p) & (rand_mask < inf_threshold)
        neg_inf_mask = total_noise & (rand_mask >= inf_threshold)
        
        # 应用替换
        x = torch.where(nan_mask, torch.tensor(float('nan'), device=x.device), x)
        x = torch.where(inf_mask, torch.tensor(float('inf'), device=x.device), x)
        x = torch.where(neg_inf_mask, torch.tensor(float('-inf'), device=x.device), x)
        return x

    def drop_for_reason_perfeature(self, x):
         # 创建20类无序分类采样器
        nan_prob_sampler = CategoricalActivation(ordered_p=0.0
                                                 , categorical_p=1.0
                                                 , keep_activation_size=False,
                                                 num_classes_sampler=lambda: 20)
        # 生成特征相关的缺失掩码（形状与x相同
        d = nan_prob_sampler(x)
        have_nan_p = np.random.uniform(self.h['prob_have_nan_value_min'], self.h['prob_have_nan_value_max'])  # 有异常值的比例
        have_nan_value_p = np.random.uniform(self.h['prob_have_nan_type_min'], self.h['prob_have_nan_type_max']) # 异常值是nan的比例
        
        rand_vals = torch.rand_like(x)
        # 创建各类型mask
        nan_mask = (d < torch.rand((1,), device=x.device) * 20 * have_nan_p * random.random()) & (rand_vals < have_nan_value_p)
        inf_mask = (d < torch.rand((1,), device=x.device) * 20 * have_nan_p * random.random()) & (rand_vals >= have_nan_value_p) & (rand_vals < have_nan_value_p + 0.5*(1-have_nan_value_p))
        neg_inf_mask = (d < torch.rand((1,), device=x.device) * 20 * have_nan_p * random.random()) & (rand_vals >= have_nan_value_p + 0.5*(1-have_nan_value_p))
        
        # 应用替换
        x = torch.where(nan_mask, torch.tensor(float('nan'), device=x.device), x)
        x = torch.where(inf_mask, torch.tensor(float('inf'), device=x.device), x)
        x = torch.where(neg_inf_mask, torch.tensor(float('-inf'), device=x.device), x)
        return x
    
    def validate_noise_distribution(self, x):
        # 计算总元素数
        total_elements = x.numel()
        
        # 计算各种特殊值的数量
        nan_count = torch.isnan(x).sum().item()
        inf_count = torch.isinf(x).sum().item()
        pos_inf_count = (x == float('inf')).sum().item()
        neg_inf_count = (x == float('-inf')).sum().item()
        
        # 计算实际比例
        nan_ratio = nan_count / total_elements
        inf_ratio = inf_count / total_elements
        pos_inf_ratio = pos_inf_count / total_elements
        neg_inf_ratio = neg_inf_count / total_elements
        

        print(f"验证结果:")
        print(f"NaN比例: 实际={nan_ratio:.4f}")
        print(f"Inf比例: 实际={inf_ratio:.4f}")
        print(f"  - 正Inf比例: {pos_inf_ratio:.4f}")
        print(f"  - 负Inf比例: {neg_inf_ratio:.4f}")
        print(torch.isnan(x).sum(0))
        print(torch.isinf(x).sum(0))

    
    def forward(self, batch_size):
        start = time.time()
        x, y, y_, dicts = self.get_batch(hyperparameters=self.h, **self.args_passed)
        if time_it:
            print('Flex Forward Block 1', round(time.time() - start, 3))

        start = time.time()

        if random.random() < self.h['prob_have_nan']:# 向该数据集中引入缺失值
            # 生成含缺失值的mask
            prop_nan_feature_noreason = np.random.uniform(self.h['prob_have_nan_feature_no_reason_min'], 
                                                          self.h['prob_have_nan_feature_no_reason_max'])
            prob_nan_feature_reason = np.random.uniform(self.h['prob_have_nan_feature_reason_min'],
                                                         self.h['prob_have_nan_feature_reason_max'])
            
            # 生成形状为(B, f)的随机矩阵
            B, f = x.shape[1], x.shape[2]

            # 生成每个样本的nan位置索引
            k1 = int(f * prop_nan_feature_noreason)
            k2 = int(f * prob_nan_feature_reason)
            
            # 为每个样本独立生成mask
            mask = torch.zeros_like(x[0], dtype=torch.long)  # (B, f)
            for b in range(B):
                # 随机打乱特征索引
                indices = torch.randperm(f, device=self.args['device'])
                
                # 分配类型1的nan位置
                mask1_indices = indices[:k1]
                mask[b, mask1_indices] = 1
                
                # 分配类型2的nan位置（从剩余位置中选择）
                remaining = indices[k1:]
                mask2_indices = remaining[:k2]
                mask[b, mask2_indices] = 2
            
            # 将mask扩展到与x相同的维度 (S, B, f)
            mask = mask.unsqueeze(0).expand(x.shape[0], -1, -1)
            x = torch.where(mask == 1, self.drop_for_no_reason_perfeature(x), x)
            x = torch.where(mask == 2, self.drop_for_reason_perfeature(x), x)
           
            # self.validate_noise_distribution(x)
    
        # Categorical features
        if 'categorical_feature_p' in self.h and random.random() < self.h['categorical_feature_p']:
            # 遍历每个特征列，随机生成不同数量的唯一类别，并使用MulticlassRank进行转换，模拟分类特征。这里使用了Gamma分布来生成类别数量，确保至少有2个类别，避免单一类别的情况
            # TODO 增加分类特征比例的分布控制的参数
            p = random.random()
            for col in range(x.shape[2]):
                num_unique_features = max(round(random.gammavariate(1,10)),2)
                m = MulticlassRank(num_unique_features, ordered_p=0.3)
                if random.random() < p:
                    x[:, :, col] = m(x[:, :, col])

        if time_it:
            print('Flex Forward Block 2', round(time.time() - start, 3))
            start = time.time()

        # 数据标准化
        if self.h['normalize_to_ranking']:
            x = to_ranking_low_mem(x)                   # 转换为排序特征
        else:
            x = remove_outliers(x)                      # 去除异常值
        x, y = normalize_data(x), normalize_data(y)     # 标准化到统一量纲

        if time_it:
            print('Flex Forward Block 3', round(time.time() - start, 3))
            start = time.time()

        # Cast to classification if enabled, 目标转换
        y = self.class_assigner(y, max_retry=self.h['y_balance_max_retry']).float()

        if time_it:
            print('Flex Forward Block 4', round(time.time() - start, 3))
            start = time.time()
        
        # 特征维度扩展
        # if self.h['normalize_by_used_features']:
        #     x = normalize_by_used_features_f(x, self.h['num_features_used'], self.args['num_features'], normalize_with_sqrt=self.h.get('normalize_with_sqrt',False))    ## 基于有效特征的标准化
        if time_it:
            print('Flex Forward Block 5', round(time.time() - start, 3)) 

        if False:
            start = time.time()
            # Append empty features if enabled, 填充无效特征列
            x = torch.cat(
                [x, torch.zeros((x.shape[0], x.shape[1], self.args['num_features'] - self.h['num_features_used']),
                                device=self.args['device'])], -1)
            if time_it:
                print('Flex Forward Block 6', round(time.time() - start, 3))

            if torch.isnan(y).sum() > 0:
                print('Nans in target!')

            # 如果normalize_positions为-1，则使用整个X，否则使用X的前normalize_positions部分
            if self.h['check_is_compatible']:
                for b in range(y.shape[1]):
                    # 计算data的均值和标准差
                    # 确保训练集和验证集包含相同类别
                    # 计算n_sigma倍的标准差
                    is_compatible, N = False, 0
                    # 计算上下限
                    while not is_compatible and N < 10:
                        targets_in_train = torch.unique(y[:self.args['single_eval_pos'], b], sorted=True)
                        # 创建一个掩码，用于标记data中不在上下限范围内的值
                        targets_in_eval = torch.unique(y[self.args['single_eval_pos']:, b], sorted=True)
                        is_compatible = len(targets_in_train) == len(targets_in_eval) and (
                                    targets_in_train == targets_in_eval).all() and len(targets_in_train) > 1

                        if not is_compatible:
                            randperm = torch.randperm(x.shape[0])
                            x[:, b], y[:, b] = x[randperm, b], y[randperm, b]
                        N = N + 1
                    if not is_compatible:
                        if not is_compatible:
                            # todo check that it really does this and how many together
                            y[:, b] = -100 # Relies on CE having `ignore_index` set to -100 (default)

            if self.h['normalize_labels']:
                # 标签标准化
                #assert self.h['output_multiclass_ordered_p'] == 0., "normalize_labels destroys ordering of labels anyways."
                for b in range(y.shape[1]):
                    valid_labels = y[:,b] != -100
                    if self.h.get('normalize_ignore_label_too', False):     # 是否将忽略的标签也参与标签序列化
                        valid_labels[:] = True
                    # 将标签转换为排序索引
                    y[valid_labels, b] = (y[valid_labels, b] > y[valid_labels, b].unique().unsqueeze(1)).sum(axis=0).unsqueeze(0).float()

                    if y[valid_labels, b].numel() != 0 and self.h.get('rotate_normalized_labels', True):
                        # 随机循环偏移标签
                        num_classes_float = (y[valid_labels, b].max() + 1).cpu()
                        num_classes = num_classes_float.int().item()
                        assert num_classes == num_classes_float.item()
                        random_shift = torch.randint(0, num_classes, (1,), device=self.args['device'])
                        y[valid_labels, b] = (y[valid_labels, b] + random_shift) % num_classes

        return x, y, y, dicts  # x.shape = (T,B,H)

import torch.cuda as cutorch

@torch.no_grad()
def get_batch(batch_size, seq_len, num_features, get_batch, device, hyperparameters=None, batch_size_per_gp_sample=None, **kwargs):
    device = 'cpu'
    batch_size_per_gp_sample = batch_size_per_gp_sample or (min(32, batch_size))
    num_models = batch_size // batch_size_per_gp_sample
    assert num_models > 0, f'Batch size ({batch_size}) is too small for batch_size_per_gp_sample ({batch_size_per_gp_sample})'
    assert num_models * batch_size_per_gp_sample == batch_size, f'Batch size ({batch_size}) not divisible by batch_size_per_gp_sample ({batch_size_per_gp_sample})'

    # Sample one seq_len for entire batch
    seq_len = hyperparameters['seq_len_used']() if callable(hyperparameters['seq_len_used']) else seq_len

    args = {'device': device, 'seq_len': seq_len, 'num_features': num_features, 'batch_size': batch_size_per_gp_sample, **kwargs}

    models = [FlexibleCategorical(get_batch, hyperparameters, args).to(device) for _ in range(num_models)]

    sample = [model(batch_size=batch_size_per_gp_sample) for model in models]

    x, y, y_, dicts = zip(*sample)
    x, y, y_ = torch.cat(x, 1).detach(), torch.cat(y, 1).detach(), torch.cat(y_, 1).detach()

    return x, y, y_, dicts

# num_features_used = num_features_used_sampler()
# prior_outputscale = prior_outputscale_sampler()
# prior_lengthscale = prior_lengthscale_sampler()
#
# x, sample = normalize_data(x), normalize_data(sample)
#
# if is_binary_classification:
#     sample = (sample > torch.median(sample, dim=0)[0]).float()
#
# if normalize_by_used_features:
#     x = normalize_by_used_features_f(x, num_features_used, num_features)
#
# # # if is_binary_classification and order_y:
# # #     x, sample = order_by_y(x, sample)
# #
# # Append empty features if enabled
# x = torch.cat([x, torch.zeros((x.shape[0], x.shape[1], num_features - num_features_used), device=device)], -1)

DataLoader = get_batch_to_dataloader(get_batch)