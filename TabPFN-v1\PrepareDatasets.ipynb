{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "import openml\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "from datasets import load_openml_list, test_dids_classification, valid_large_classification, open_cc_dids, open_cc_valid_dids\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Prepare test datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["renamer = {'name': 'Name', 'NumberOfFeatures': '# Features', 'NumberOfSymbolicFeatures': '# Categorical Features', 'NumberOfInstances': '# Instances', 'NumberOfMissingValues': '# NaNs', 'NumberOfClasses': '# Classes', 'MinorityClassSize': 'Minority Class Size'}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["openml.study.list_suites()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["suite = openml.study.get_suite(suite_id=99)\n", "tasks = openml.tasks.list_tasks(output_format=\"dataframe\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using ``@`` in `pd.DataFrame.query <\n", "# https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.query.html>`_\n", "# accesses variables outside of the current dataframe.\n", "tasks = tasks.query(\"tid in @suite.tasks\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tids = list(tasks[np.logical_and(np.logical_and((tasks.NumberOfInstances <= 2000), (tasks.NumberOfFeatures <= 100))\n", "                                 , (tasks.NumberOfClasses <= 10))].tid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(tids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tids = list(tasks[tasks.NumberOfInstances <= 2000].tid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_cc_dids = [openml.tasks.get_task(task_id).get_dataset().id for task_id in tids]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["open_ml_datasets, open_ml_datasets_df = load_openml_list(test_dids_classification, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 100000, num_feats=100, return_capped=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["open_ml_datasets_df = open_ml_datasets_df[open_ml_datasets_df.NumberOfInstances > 10000]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_table = open_ml_datasets_df\n", "print_table = print_table[['name', 'NumberOfFeatures', 'NumberOfSymbolicFeatures', 'NumberOfInstances', 'NumberOfClasses', 'NumberOfMissingValues', 'MinorityClassSize']].copy()\n", "print_table['id'] = print_table.index\n", "print_table[['NumberOfFeatures', 'NumberOfSymbolicFeatures', 'NumberOfInstances', 'NumberOfClasses', 'NumberOfMissingValues', 'MinorityClassSize']] = print_table[['NumberOfFeatures', 'NumberOfSymbolicFeatures', 'NumberOfInstances', 'NumberOfClasses', 'NumberOfMissingValues', 'MinorityClassSize']].astype(int)\n", "print_table = print_table.rename(columns=renamer)\n", "print(print_table.to_latex(index=False))"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Prepare Validation datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["open_cc_datasets, open_cc_datasets_df = load_openml_list(open_cc_dids, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 2000, num_feats=100, return_capped=True)\n", "\n", "def extend_datasets(datasets, filtering = False):\n", "    extended_datasets = {}\n", "    i = 0\n", "    for d in tqdm(datasets):\n", "        if ((not 'NumberOfFeatures' in datasets[d])\n", "                or (not 'NumberOfClasses' in datasets[d])\n", "                or (not 'NumberOfInstances' in datasets[d])\n", "                # or datasets[d]['NumberOfFeatures'] >= num_feats\n", "                or datasets[d]['NumberOfClasses'] <= 0):\n", "            print(datasets[d])\n", "            continue\n", "        ds = openml.datasets.get_dataset(d, download_data=False)\n", "        if filtering and (datasets[d]['NumberOfInstances'] < 150\n", "                          or datasets[d]['NumberOfInstances'] > 2000\n", "                         or datasets[d]['NumberOfFeatures'] > 100\n", "                         or datasets[d]['NumberOfClasses'] > 10):\n", "            continue\n", "        extended_datasets[d] = datasets[d]\n", "        extended_datasets[d].update(ds.qualities)\n", "    \n", "    return extended_datasets\n", "\n", "# All datasets\n", "openml_list = openml.datasets.list_datasets()\n", "openml_list = pd.DataFrame.from_dict(openml_list, orient=\"index\")\n", "\n", "# Select only classification\n", "openml_list = openml_list[~openml_list['MajorityClassSize'].isna()]\n", "\n", "# Remove duplicated datasets\n", "duplicated = openml_list.duplicated(subset=['MajorityClassSize', 'MaxNominalAttDistinctValues', 'MinorityClassSize',\n", "       'NumberOfClasses', 'NumberOfFeatures', 'NumberOfInstances',\n", "       'NumberOfInstancesWithMissingValues', 'NumberOfMissingValues',\n", "       'NumberOfNumericFeatures', 'NumberOfSymbolicFeatures'], keep='first')\n", "openml_list = openml_list[~duplicated]\n", "\n", "duplicated = openml_list.duplicated(subset=['name'], keep='first')\n", "openml_list = openml_list[~duplicated]\n", "\n", "# Filter out datasets that don't have meta information or Don't fulfill other criteria\n", "openml_list = openml_list.to_dict(orient='index')\n", "openml_list = pd.DataFrame.from_dict(extend_datasets(openml_list, filtering=True), orient=\"index\")\n", "\n", "# Filter out datasets in Open CC\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: x in test_datasets_multiclass_df.name.values)]\n", "openml_list['CFI'] = openml_list.apply(lambda x: str(x.NumberOfClasses) + '_' + str(x.NumberOfFeatures) + '_' + str(x.NumberOfInstances), axis = 1)\n", "test_datasets_multiclass_df['CFI'] = test_datasets_multiclass_df.apply(lambda x: str(x.NumberOfClasses) + '_' + str(x.NumberOfFeatures) + '_' + str(x.NumberOfInstances), axis = 1)\n", "openml_list = openml_list[~openml_list.CFI.apply(lambda x: x in test_datasets_multiclass_df.CFI.values)]\n", "\n", "# Remove time series and artificial data\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: 'autoUniv' in x)]\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: 'fri_' in x)]\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: 'FOREX' in x)]\n", "\n", "# Remove datasets that overlapped with Open CC closely by name\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: 'ilpd' in x)]\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: 'car' in x)]\n", "openml_list = openml_list[~openml_list.name.apply(lambda x: 'pc1' in x)]\n", "\n", "# Remove datasets that didn't load\n", "openml_list = openml_list[~openml_list.did.apply(lambda x: x in {1065, 40589, 41496, 770, 43097, 43148, 43255, 43595, 43786, 41701})]\n", "\n", "# Remove class skew\n", "openml_list = openml_list[(openml_list.MinorityClassSize / openml_list.MajorityClassSize) > 0.05]\n", "openml_list = openml_list[openml_list.AutoCorrelation != 1]\n", "\n", "# Remove too easy\n", "openml_list = openml_list[openml_list.CfsSubsetEval_DecisionStumpAUC != 1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_table = openml_list\n", "print_table = print_table[['name', 'NumberOfFeatures', 'NumberOfSymbolicFeatures', 'NumberOfInstances', 'NumberOfClasses', 'NumberOfMissingValues', 'MinorityClassSize']].copy()\n", "print_table['id'] = print_table.index\n", "print_table[['NumberOfFeatures', 'NumberOfSymbolicFeatures', 'NumberOfInstances', 'NumberOfClasses', 'NumberOfMissingValues', 'MinorityClassSize']] = print_table[['NumberOfFeatures', 'NumberOfSymbolicFeatures', 'NumberOfInstances', 'NumberOfClasses', 'NumberOfMissingValues', 'MinorityClassSize']].astype(int)\n", "print_table = print_table.rename(columns=renamer)\n", "print(print_table.to_latex(index=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 4}