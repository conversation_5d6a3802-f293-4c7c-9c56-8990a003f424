import os
import networkx as nx
import torch
import torch.nn as nn
import numpy as np
from typing import *
import types
from .edge_func_tree import <PERSON><PERSON>, Categorical, DecesionTree, GaussianNoise, Conv1D, MLP_2layers, DecisionTreeRegression

from .utils import gamma_sampler_f, beta_sampler_f, normal_sampler_f, uniform_sampler_f, randint_sampler, get_threshold_func_beta
import matplotlib.pyplot as plt
import time
# from utils import default_device
import random
from .utils import get_batch_to_dataloader
from torch.distributions import <PERSON><PERSON><PERSON>
from utils import high_precision_trace


default_device = 'cuda:0' if torch.cuda.is_available() else 'cpu:0'

def load_DAG(path: str, file_name: str) -> nx.Graph:
    if '.gml' not in file_name:
        file_name += '.gml'
    path = os.path.join(path, file_name)
    dag = nx.read_gml(path)
    return dag

def draw_DAG(dag_plt: nx.Graph, 
             X_node: List = None, 
             Y_node: List = None, 
             XY_node: List = None,
             edge_labels: dict = None,
             pos_circle=True) -> None:
    node_colors = []
    if X_node == None:
        X_node = []
    if Y_node == None:
        Y_node = []
    if XY_node == None:
        XY_node = []
    for node in dag_plt.nodes():
        node = int(node)
        # X，Y同时存在于一个node
        if node in XY_node:
            node_colors.append('yellow')
        elif node in X_node:
            node_colors.append('skyblue')
        elif node in Y_node:
            node_colors.append('red')
        else:
            node_colors.append('gainsboro')
    
    if pos_circle:
        # 圆形布局（适合小规模图）
        pos = nx.circular_layout(dag_plt)
    else:
        # 计算层级
        levels = {n: 0 for n in dag_plt.nodes()}
        for node in nx.topological_sort(dag_plt):
            for successor in dag_plt.successors(node):
                levels[successor] = max(levels[successor], levels[node] + 1)
        
        # 将层级信息添加到节点属性中
        nx.set_node_attributes(dag_plt, levels, name='subset')
        
        # 使用正确的subset_key参数
        pos = nx.multipartite_layout(dag_plt, subset_key='subset', align='horizontal', scale=2)
        
        # 调整坐标方向
        pos = {n: (x, -y) for n, (x, y) in pos.items()}
    
    plt.figure()
    if node_colors != []:
        nx.draw(dag_plt, with_labels=True, pos=pos, node_color=node_colors)
    else:
        nx.draw(dag_plt, with_labels=True, pos=pos, node_color='gainsboro')
    if edge_labels != None:
        nx.draw_networkx_edge_labels(
            dag_plt, 
            pos,
            edge_labels=edge_labels,
            font_color='darkred',
            font_size=10
        )
    return
    

def causes_sampler_f(num_causes):
    # 原V1 MLP的实现
    means = np.random.normal(0, 1, (num_causes))
    std = np.abs(np.random.normal(0, 1, (num_causes)) * means)
    return means, std
 
def norm_to_0_1(x: torch.Tensor):
    eps = .00001
    maxima = torch.max(x, 0)[0]
    minima = torch.min(x, 0)[0]
    normed_x = (x - minima) / (maxima - minima + eps)

    def denorm(normed_x):
        return normed_x * (maxima - minima + eps) + minima

    normed_x = torch.clamp(normed_x , min=0.0, max=1.0)
    
    return normed_x, denorm
 
def input_warpping(x: torch.Tensor, hyperparameters: dict):
    input_warping_type = hyperparameters.get('input_warping_type', 'kumar')
    input_warping_norm = hyperparameters.get('input_warping_norm', True)
    use_icdf = hyperparameters.get('input_warping_use_icdf', False)
    
    if input_warping_norm:
        x, denorm = norm_to_0_1(x)
    if input_warping_type == 'kumar':
        # if 'input_warping_c_std' in hyperparameters:
        #     assert 'input_warping_c0_std' not in hyperparameters and 'input_warping_c1_std' not in hyperparameters
        #     hyperparameters['input_warping_c0_std'] = hyperparameters['input_warping_c_std']
        #     hyperparameters['input_warping_c1_std'] = hyperparameters['input_warping_c_std']
        inside = 0
        while not inside:
            c1 = (torch.randn(*x.shape[1:], device=x.device) * hyperparameters.get('input_warping_c1_std', 0.9759720822120248)).exp()
            c0 = (torch.randn(*x.shape[1:], device=x.device) * hyperparameters.get('input_warping_c0_std', 0.8002534583197192)).exp()
            if not hyperparameters.get('input_warping_in_range', False):
                inside = True
            elif (c1 < 10).all() and (c1 > 0).all() and (c0 < 10).all() and (c0 > 0).all():
                inside = True
            else:
                inside -= 1
                if inside < -100:
                    print('It seems that the input warping is not working.')
        if c1_v := hyperparameters.get('fix_input_warping_c1', False):
            c1[:] = c1_v
        if c0_v := hyperparameters.get('fix_input_warping_c0', False):
            c0[:] = c0_v
        k = Kumaraswamy(concentration1=c1, concentration0=c0)
        x_transformed = k.icdf(x) if use_icdf else k.cdf(x)
    elif input_warping_type is None or input_warping_type == 'none':
        x_transformed = x
    else:
        raise ValueError(f"Unknown input_warping_type: {input_warping_type}")
    
    if input_warping_norm:
        x_transformed = denorm(x_transformed)
    return x_transformed


def get_DAG_flexible(mode: str = 'multi', 
                     sampler_num_dag: Union[types.FunctionType, int, None] = None,
                     sampler_num_node: Union[types.FunctionType, int, None] = None,
                     sampler_prob: Union[types.FunctionType, float, None] = None,
                     sampler_num_dag_node: Union[types.FunctionType, str] = 'uniform'
                     ) -> List:
    
    # 多项式分布（通过修改概率，允许DAG间节点数更大的差异性）
    def num_dag_node_multinomial(num_dag: int, 
                                 num_node: int, 
                                 sampler_num_dag_node: Union[types.FunctionType, str]):
        if isinstance(sampler_num_dag_node, str):
            if sampler_num_dag_node == 'uniform':
                prob_num_dag_node = np.ones(num_dag)/num_dag    # 对节点属于各个DAG的概率均匀采样
        else:
             # 对节点属于各个DAG的概率进行采样, shape为 (num_dag, )，确保概率为正数
            prob_num_dag_node = np.abs(sampler_num_dag_node())
            
            # 使概率和为 1
            prob_num_dag_node = prob_num_dag_node / np.sum(prob_num_dag_node)
            
        # 生成总和为 num_node 的 num_dag个非负整数
        num_dag_node = np.random.multinomial(num_node, prob_num_dag_node)
        return num_dag_node
    
    # 定义re-direction概率
    if isinstance(sampler_prob, float):
        p = sampler_prob
    else:
        p = sampler_prob()
    p = abs(p) % 1   # 确保 p 在 [0, 1] 范围内
    
    # 单根节点, 单个 DAG
    if mode == 'single':
        assert sampler_num_node!= None and sampler_prob != None, 'sampler_num_node and sampler_prob must be provided'
        
        # 定义节点总数
        if isinstance(sampler_num_node, int):
            num_node = sampler_num_node
        else:
            # 总节点数必须为正整数
            num_node = int(np.ceil(abs(sampler_num_node())).item()) 
        
        # 总节点数至少为 2
        num_node = max(num_node, 2)                                
        
        # 生成图：通过re-direction sampling生成DAG, reverse()获得数据生成阶段的正向DAG
        dag = nx.gnr_graph(num_node, p)
        return [dag]
    
    # 多根节点, 多个独立 DAG
    elif mode =='multi':
        assert sampler_num_dag != None and sampler_num_node != None and sampler_prob != None, 'sampler_num_dag, sampler_num_node and sampler_prob must be provided'

        # 定义DAG概率
        if isinstance(sampler_num_dag, int):
            num_dag = sampler_num_dag
        else:
            # DAG总数必须为正整数
            num_dag = int(np.floor(abs(sampler_num_dag())).item())
        
         # DAG总数至少为 2
        num_dag = max(num_dag, 2)                                 

        # 定义节点总数
        if isinstance(sampler_num_node, int):
            num_node = sampler_num_node
        else:
            # 总节点数必须为正整数
            num_node = int(np.ceil(abs(sampler_num_node())).item()) 

        # 总节点数至少为 2*num_dag + 2, 即保证每个DAG至少有概率有两个节点
        num_node = max(num_node, 2 * num_dag)    

        # 定义各个DAG的节点数
        if num_node / num_dag == 2:
            # 假如每个DAG至多有两个节点, 则直接返回
            num_dag_node = np.ones(num_dag, dtype=int) * 2
        else:
            num_single_root = 1
            # 确保每个DAG至少有两个节点, 若有节点数小于2的DAG, 则重新生成
            while num_single_root:  
                num_dag_node = num_dag_node_multinomial(num_dag, num_node, sampler_num_dag_node)
                num_single_root = np.sum(num_dag_node < 2)  # 统计节点数小于 2 的 DAG 数量
       
        # 生成图：通过re-direction sampling生成DAG, reverse()获得数据生成阶段的正向DAG
        dags = []
        for n in num_dag_node:
            dags.append(nx.gnr_graph(n, p))
        
        return dags
    
def get_DAG(sampler_num_dag: Union[types.FunctionType, int, None] = None,
            sampler_num_node: Union[types.FunctionType, int, None] = None,
            sampler_prob: Union[types.FunctionType, float, None] = None,
            generate_gnc_dag: bool = False
            ) -> List:

    assert sampler_num_dag != None and sampler_num_node != None, 'sampler_num_dag and sampler_num_node must be provided'
    
    # 定义DAG个数
    if isinstance(sampler_num_dag, int):
        num_dag = sampler_num_dag
    else:
        # DAG总数必须为正整数
        num_dag = int(np.floor(abs(sampler_num_dag())).item())
    
    if not generate_gnc_dag:
        # 定义re-direction概率
        assert sampler_prob != None, 'sampler_prob must be provided'

        if isinstance(sampler_prob, float):
            p = sampler_prob
        else:
            p = sampler_prob()
        p = abs(p) % 1   # 确保 p 在 [0, 1] 范围内
            
        # 生成图：通过re-direction sampling生成DAG, reverse()获得数据生成阶段的正向DAG
        dags = []
        for _ in range(num_dag):
            # 定义节点总数
            if isinstance(sampler_num_node, int):
                num_node = sampler_num_node
            else:
                # 总节点数必须为正整数
                num_node = int(np.ceil(abs(sampler_num_node())).item())
            dags.append(nx.gnr_graph(num_node, p))
    else:
        # 生成图：生成正向 GNC DAG图
        dags = []
        for _ in range(num_dag):
            # 定义节点总数
            if isinstance(sampler_num_node, int):
                num_node = sampler_num_node
            else:
                # 总节点数必须为正整数
                num_node = int(np.ceil(abs(sampler_num_node())).item())
            dags.append(nx.gnc_graph(num_node))
    
    
    return dags

def root_data_generation(num_root: int,
                         seq_len: int, 
                         num_features_used: int, 
                         hyperparameters: dict,
                         device: str) -> torch.Tensor:
    sampler_prototypes_mix = hyperparameters['sampler_prototypes_mix']  # func
    prototypes_fraction = hyperparameters['prototypes_fraction']    # float
    prototypes_p = hyperparameters['prototypes_p']  # np.ndarray

    if hyperparameters['num_features_used_predefined'] > 0:
        num_features_used = hyperparameters['num_features_used_predefined']
    elif hyperparameters['num_features_used_predefined'] < 0:
        num_features_used = int(num_features_used * hyperparameters['edge_output_dim_scale'])
    type = ""
    # 是否通过prototypes的混合来增加 non-independence
    if sampler_prototypes_mix() < 0.5:  
        type = "normal"
        size = (num_root, seq_len, num_features_used)   # 对应的是get_batch中的num_features
        
        # 生成根数据
        if hyperparameters['scm_init_data_sampling_method'] == 'normal':
            # root_data = torch.normal(0, hyperparameters['init_data_normal_std'], size=size, device=device) # (num_root, seq_len, num_features_used)
            mean, std = causes_sampler_f(num_features_used)
            mean = torch.tensor(mean, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, seq_len, 1))
            std = torch.tensor(std, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, seq_len, 1))
            root_data = torch.normal(mean, std.abs()).float()
            
        elif hyperparameters['scm_init_data_sampling_method'] == 'uniform':
            # alpha = hyperparameters['init_data_uniform_scale']
            # root_data = 2 * alpha *(torch.rand(size=size, device=device) - 0.5)   # 映射到 [-alpha, alpha]区间
            root_data = torch.rand((num_root, seq_len, num_features_used), device=device)
            
        elif hyperparameters['scm_init_data_sampling_method'] == 'mixed':
            zipf_p, multi_p, normal_p = random.random() * 0.66, random.random() * 0.66, random.random() * 0.66
            def sample_cause(n):
                if random.random() > normal_p:
                    mean, std = causes_sampler_f(num_features_used)
                    mean = torch.tensor(mean, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, seq_len, 1))
                    std = torch.tensor(std, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, seq_len, 1))
                    return torch.normal(mean[:, :, n], std[:, :, n].abs()).float()
                else:
                    data = []
                    for _ in range(num_root):
                        while True:
                            x = torch.multinomial(torch.rand((random.randint(2, 10))), seq_len, replacement=True).to(device).float()
                            if len(torch.unique(x)) > 1:
                                break
                        x = (x - torch.mean(x)) / torch.std(x)
                        data.append(x)
                    data = torch.stack(data, dim=0).float()
                    return data
                # elif random.random() > multi_p:
                #     data = []
                #     for _ in range(num_root):
                #         x = torch.multinomial(torch.rand((random.randint(2, 10))), seq_len, replacement=True).to(device).float()
                #         x = (x - torch.mean(x)) / torch.std(x)
                #         data.append(x)
                #     data = torch.stack(data, dim=0).float()
                #     return data
                # else:
                #     x = torch.minimum(torch.tensor(np.random.zipf(2.0 + random.random() * 2, size=(num_root, seq_len)),
                #                         device=device).float(), torch.tensor(10.0, device=device))
                #     return x - torch.mean(x)
            root_data = torch.cat([sample_cause(n).unsqueeze(-1) for n in range(num_features_used)], -1)
        
        elif hyperparameters['scm_init_data_sampling_method'] == 'mixed_v2':
            if np.random.rand() >= 0.5:
                # root_data = torch.normal(0, hyperparameters['init_data_normal_std'], size=size, device=device)  # (num_root, seq_len, num_features_used)
                mean, std = causes_sampler_f(num_features_used)
                mean = torch.tensor(mean, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, seq_len, 1))
                std = torch.tensor(std, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, seq_len, 1))
                root_data = torch.normal(mean, std.abs()).float()
            else:
                alpha = hyperparameters['init_data_uniform_scale']
                root_data = 2 * alpha *(torch.rand(size=size, device=device) - 0.5)   # 映射到 [-alpha, alpha]区间
                # root_data = torch.rand((num_root, seq_len, num_features_used), device=device)
                 
    else:
        type = "prototypes"
        assert 0 < prototypes_fraction < 1, "prototypes_fraction must be in (0, 1)."
        num_prototypes = int(seq_len * prototypes_fraction)
        size = (num_root, num_prototypes, num_features_used)  
        
        # 生成 prototypes
        if hyperparameters['scm_init_data_sampling_method'] == 'normal':
            # prototypes = torch.normal(0, hyperparameters['init_data_normal_std'], size=size, device=device) # (num_root, num_prototypes, num_features_used)
            mean, std = causes_sampler_f(num_features_used)
            mean = torch.tensor(mean, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, num_prototypes, 1))
            std = torch.tensor(std, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, num_prototypes, 1))
            prototypes = torch.normal(mean, std.abs()).float()

        elif hyperparameters['scm_init_data_sampling_method'] == 'uniform':
            # alpha = hyperparameters['init_data_uniform_scale']
            # prototypes = 2 * alpha *(torch.rand(size=size, device=device) - 0.5)   # 映射到 [-alpha, alpha]区间
            prototypes = torch.rand((num_root, num_prototypes, num_features_used), device=device)
            
        elif hyperparameters['scm_init_data_sampling_method'] == 'mixed':
            zipf_p, multi_p, normal_p = random.random() * 0.66, random.random() * 0.66, random.random() * 0.66
            def sample_cause(n):
                if random.random() > normal_p:
                    mean, std = causes_sampler_f(num_features_used)
                    mean = torch.tensor(mean, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, num_prototypes, 1))
                    std = torch.tensor(std, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, num_prototypes, 1))
                    return torch.normal(mean[:, :, n], std[:, :, n].abs()).float()
                # elif random.random() > multi_p:
                #     data = []
                #     for _ in range(num_root):
                #         x = torch.multinomial(torch.rand((random.randint(2, 10))), num_prototypes, replacement=True).to(device).float()
                #         x = (x - torch.mean(x)) / torch.std(x)
                #         data.append(x)
                #     data = torch.stack(data, dim=0).float()
                #     return data
                # else:
                #     x = torch.minimum(torch.tensor(np.random.zipf(2.0 + random.random() * 2, size=(num_root, num_prototypes)),
                #                         device=device).float(), torch.tensor(10.0, device=device))
                #     return x - torch.mean(x)
                else:
                    data = []
                    for _ in range(num_root):
                        while True:
                            x = torch.multinomial(torch.rand((random.randint(2, 10))), num_prototypes, replacement=True).to(device).float()
                            if len(torch.unique(x)) > 1:
                                break
                        x = (x - torch.mean(x)) / torch.std(x)
                        data.append(x)
                    data = torch.stack(data, dim=0).float()
                    return data
            prototypes = torch.cat([sample_cause(n).unsqueeze(-1) for n in range(num_features_used)], -1)
            # (num_root, num_prototypes, num_features_used)
            
        elif hyperparameters['scm_init_data_sampling_method'] == 'mixed_v2':
            if np.random.rand() >= 0.5:
                # prototypes = torch.normal(0, hyperparameters['init_data_normal_std'], size=size, device=device)  # (num_root, num_prototypes, num_features_used)
                mean, std = causes_sampler_f(num_features_used)
                mean = torch.tensor(mean, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, num_prototypes, 1))
                std = torch.tensor(std, device=device).unsqueeze(0).unsqueeze(0).tile((num_root, num_prototypes, 1))
                prototypes = torch.normal(mean, std.abs()).float()
            else:
                alpha_scale = hyperparameters['init_data_uniform_scale']
                prototypes = 2 * alpha_scale *(torch.rand(size=size, device=device) - 0.5)   # 映射到 [-alpha, alpha]区间
                # prototypes = torch.rand((num_root, num_prototypes, num_features_used), device=device)
        
        if hyperparameters['use_dirichlet']:  
            alpha = torch.distributions.dirichlet.Dirichlet(
                torch.ones(num_prototypes, device=device) * prototypes_p
                ).sample([num_root, seq_len]).float()   # (num_root, seq_len, num_prototypes)
        else:
            with torch.no_grad():
                softmax_temp = max(abs(hyperparameters['root_data_temp']), 1e-4)
                alpha = torch.randn(num_root, seq_len, num_prototypes, device=device)
                alpha = torch.nn.functional.softmax(alpha / softmax_temp, dim=-1)

        root_data = torch.matmul(alpha, prototypes) # (num_root, seq_len, num_features_used)
    
    root_data.requires_grad = False
    
    
    return root_data


def get_batch(batch_size, seq_len, num_features, hyperparameters, device=default_device, num_outputs=1, sampling = 'normal', epoch=None, **kwargs):
    
    
    def SCM_compute(seq_len: int, num_features_used: int, hyperparameters: dict, device: str):
        EDGE_LIST = {
                'mlp': MLP,
                'cate': Categorical,
                'tree': DecesionTree, 
                'conv1d': Conv1D,
                'mlp_2layers': MLP_2layers,
                "reg_tree": DecisionTreeRegression
            }
        edge_use_full_data = [
            'reg_tree'
        ]
        
        dict_save = {}
        # 生成DAG
        dags = get_DAG(hyperparameters['sampler_num_dag'],
                       hyperparameters['sampler_num_node'],
                       hyperparameters['sampler_prob'],
                       hyperparameters['get_gnc_DAG'])
        
        X = []
        X_all = []
        X_root = []
        X_leaf = []
        X_other = []
        dict_save['dag'] = {}
        dict_save['info'] = {
            'edge_type' : {},
            'edge_datas' : {},
            'node_sampled': {}
        }
        dict_save['info']['node_sampled'] = {}
        node_XY_sampled = []
        node_XY_sampled_root = []
        node_XY_sampled_other = []
        node_XY_sampled_leaf = []
        
        if callable(hyperparameters['tree_input_scale']):
            tree_input_scale = hyperparameters['tree_input_scale']()
        else:
            tree_input_scale = hyperparameters['tree_input_scale']
        
        tree_input_num = int(num_features_used * tree_input_scale)
        tree_input_num = max(tree_input_num, hyperparameters['tree_input_min_num'])
        tree_input_num = min(tree_input_num, num_features_used)
        
        for idx_dag, dag in enumerate(dags):
            # 存储dag图数据
            dict_save['dag'][idx_dag] = dag
            dict_save['info']['node_sampled'][idx_dag] = {
                'x': None,
                'y': None,
                'xy': None
            }
            
            # 生成根数据
            num_roots = len([node for node in dag.nodes if dag.in_degree(node) == 0])
            root_data = root_data_generation(num_roots, seq_len, num_features_used, hyperparameters, device)  # (num_root, seq_len, num_features_used)
            
            # 定义节点值存储（使用字典加速查询）
            node_values = {}
            node_values_bak = {}
            num_visited_toors = 0
            edge_type = {}
            edge_datas = {}
            
            # 获取拓扑排序（保证父节点先于子节点处理）
            t1 = time.time()
            topo_order = list(nx.topological_sort(dag))
            t2 = time.time()
            
            # 存储节点与输出存储序号的对应关系，用于剔除数据使用，输入tree的，当且仅当父节点未非根节点，则进行降维处理
            X_other_preTree = {}
            X_other_preTree_scaled = []
            
            # 按拓扑顺序处理节点
            for node in topo_order:
                if dag.in_degree(node) == 0:  # 根节点
                    node_values[node] = root_data[num_visited_toors, :, :]  # 假设根数据已按节点存储
                    node_values_bak[node] = node_values[node]
                    num_visited_toors += 1
                    X_root.append(node_values[node])
                    node_XY_sampled_root.append(np.array(
                        [str(idx_dag) +'_'+ str(node) + f"_{i}" for i in range(node_values[node].shape[-1])]
                    ))
                else:
                    parent_values = []
                    parents = []
                    parent_values_mapped = []
                    
                    # 批量收集所有父节点值
                    for idx, p in enumerate(dag.predecessors(node)):
                        val = node_values[p]
                        parents.append(p)
                        pre_in_d = dag.in_degree(p)

                        while True:
                            # 确定边函数
                            edge_info = np.random.choice([
                                'mlp',
                                'cate',
                                'tree',
                                'conv1d',
                                'mlp_2layers',
                                'reg_tree'
                            ], p = hyperparameters['sampler_edge_funcs_probability'])
                            if pre_in_d > 0 or edge_info not in edge_use_full_data:
                                break
                        edge = EDGE_LIST[edge_info]
                        if edge_info == 'tree':
                            # normalization
                            val = (val - torch.mean(val, dim=0))/torch.std(val, dim=0)
                        
                            if tree_input_num != num_features_used and pre_in_d != 0 and p not in X_other_preTree_scaled:
                                perm = torch.randperm(val.shape[1])
                                val = val[:, perm[:tree_input_num]]
                                X_other[X_other_preTree[p]] = X_other[X_other_preTree[p]][:, perm[:tree_input_num]]
                                node_values[p] = X_other[X_other_preTree[p]]
                                X_other_preTree_scaled.append(p)
                                node_XY_sampled_other[X_other_preTree[p]] = node_XY_sampled_other[X_other_preTree[p]][perm[:tree_input_num]]
                        elif edge_info in edge_use_full_data:
                            val = node_values_bak[p]
                            if edge_info == 'reg_tree':
                                val = (val - torch.mean(val, dim=0))/torch.std(val, dim=0)
                            
                        values, info = edge(val, hyperparameters)
                        parent_values_mapped.append(values)
                        edge_type[(str(parents[idx]), str(node))] = edge_info
                        edge_datas[(str(parents[idx]), str(node))] = info



                    # 聚合父节点数据
                    if hyperparameters['parents_aggregation_method']:
                        agg_method = hyperparameters['parents_aggregation_method']
                        
                        if agg_method == 'mean':
                            parent_values_agg = torch.mean(torch.stack(parent_values_mapped), dim=0)
                        elif agg_method == 'mlp':
                            parent_values_mapped = torch.cat(parent_values_mapped, dim=-1)
                            parent_values_agg,_ = MLP(parent_values_mapped, hyperparameters)
                        elif agg_method =='mixed':
                            agg_method = random.choice(['mean', 'mlp'])
                            if agg_method == 'mean':
                                parent_values_agg = torch.mean(torch.stack(parent_values_mapped), dim=0)
                            elif agg_method == 'mlp':
                                parent_values_mapped = torch.cat(parent_values_mapped, dim=-1)
                                parent_values_agg,_ = MLP(parent_values_mapped, hyperparameters)
                    
                    # 计算当前节点值
                    node_values[node] = parent_values_agg
                    if hyperparameters['edge_fixed_gaussian_noise'] == True:
                        node_values[node] += GaussianNoise(node_values[node], hyperparameters)# 定义节点值存储（使用字典加速查询）
                    node_values_bak[node] = node_values[node]
                    
                    X_other_preTree[node] = len(X_other)
                    X_other.append(node_values[node])
                    # node_XY_sampled_other.append(np.array(
                    #     [str(idx_dag) +'_'+ str(node)] * node_values[node].shape[-1]
                    # ))
                    node_XY_sampled_other.append(np.array(
                        [str(idx_dag) +'_'+ str(node) + '_' + str(i) for i in range(node_values[node].shape[-1])]
                    ))

                X_all.append(node_values[node])
                node_XY_sampled.append(np.array(
                    [str(idx_dag) +'_'+ str(node) + '_' + str(i) for i in range(node_values[node].shape[-1])]
                ))
            
            # 储存边和节点信息
            dict_save['info']['edge_type'][idx_dag] = edge_type
            dict_save['info']['edge_datas'][idx_dag] = edge_datas
            node_XY_sampled_leaf.append(node_XY_sampled_other.pop())
            X_leaf.append(X_other.pop())
        
        node_XY_sampled = np.concatenate(node_XY_sampled)
        node_XY_sampled_leaf = np.concatenate(node_XY_sampled_leaf)
        node_XY_sampled_root = np.concatenate(node_XY_sampled_root)
        if len(node_XY_sampled_other) > 0:
            node_XY_sampled_other = np.concatenate(node_XY_sampled_other)
        
        if hyperparameters['using_diffrence_sample_prob']:
            root_num = max(1, int(hyperparameters['sample_root_prob_x'] * num_features_used))
            leaf_num = max(1, int(hyperparameters['sample_leaf_prob_x'] * num_features_used))
        else:
            root_num = 0
            leaf_num = 0
        other_num = num_features_used - root_num - leaf_num
        root_x_num = len(X_root) * X_root[0].shape[1]
        leaf_x_num = len(X_leaf) * X_leaf[0].shape[1]
        # other_x_num = len(X_other) * X_other[0].shape[1] if len(X_other) > 0 else 0
        other_x_num = sum([x.shape[1] for x in X_other]) if len(X_other) > 0 else 0

        if hyperparameters['auto_adjust_root_num'] and root_num > root_x_num:
            tmp_n = root_num - root_x_num
            root_num = root_x_num
            leaf_num_a = int(hyperparameters['sample_leaf_prob_x'] * tmp_n + 0.5)
            leaf_num += leaf_num_a
            other_num += tmp_n - leaf_num_a
            
        if hyperparameters['using_diffrence_sample_prob'] and len(X_other) > 0 and num_features_used >= 5 and root_x_num >= root_num and leaf_x_num > leaf_num and other_x_num >= other_num:
            assert root_num + leaf_num <= num_features_used, f"root_num({root_num}) + leaf_num({leaf_num}) should be less than num_features_used({num_features_used})， sample_root_prob_x: {hyperparameters['sample_root_prob_x']}, sample_leaf_prob_x: {hyperparameters['sample_leaf_prob_x']}"
            # 拼接节点数据
            X_root = torch.cat(X_root, dim=1)
            X_leaf = torch.cat(X_leaf, dim=1)
            X_other = torch.cat(X_other, dim=1)
            node_x = []
            # 随机采样特征
            perm_root = torch.randperm(X_root.shape[1])
            perm_leaf = torch.randperm(X_leaf.shape[1])
            perm_other = torch.randperm(X_other.shape[1])

            # 保存被选中的索引
            selected_root = perm_root[:root_num]
            selected_leaf = perm_leaf[:leaf_num]
            selected_other = perm_other[:other_num]
            
            # 添加选中的数据
            X.append(X_root[:, selected_root])
            X.append(X_leaf[:, selected_leaf])
            X.append(X_other[:, selected_other])
            
            # 从原始数据中剔除已选数据
            if(X_root.shape[1] > root_num):
                X_root = X_root[:, torch.tensor([i for i in range(X_root.shape[1]) if i not in selected_root])]
            else:
                X_root = []
            if(X_leaf.shape[1] > leaf_num):
                X_leaf = X_leaf[:, torch.tensor([i for i in range(X_leaf.shape[1]) if i not in selected_leaf])]
            else:
                X_leaf = []
            if(X_other.shape[1] > other_num):
                X_other = X_other[:, torch.tensor([i for i in range(X_other.shape[1]) if i not in selected_other])]
            else:
                X_other = []

            # 确保维度一样
            if root_num == 1:
                node_x.append(np.expand_dims(node_XY_sampled_root[selected_root], axis=-1))
            else:
                node_x.append(node_XY_sampled_root[selected_root])
            
            if leaf_num == 1:
                node_x.append(np.expand_dims(node_XY_sampled_leaf[selected_leaf], axis=-1))
            else:
                node_x.append(node_XY_sampled_leaf[selected_leaf])
            
            if other_num == 1:
                node_x.append(np.expand_dims(node_XY_sampled_other[selected_other], axis=-1))
            else:
                node_x.append(node_XY_sampled_other[selected_other])
            
            node_XY_sampled_root = node_XY_sampled_root[[i for i in range(node_XY_sampled_root.shape[0]) if i not in selected_root]]
            node_XY_sampled_leaf = node_XY_sampled_leaf[[i for i in range(node_XY_sampled_leaf.shape[0]) if i not in selected_leaf]] 
            node_XY_sampled_other = node_XY_sampled_other[[i for i in range(node_XY_sampled_other.shape[0]) if i not in selected_other]]
            
            # 特征打乱
            X = torch.concat(X, dim=1)
            node_x = np.concatenate(node_x)
            perm = torch.randperm(X.shape[1])
            X = X[:, perm]
            node_x = node_x[perm.numpy()]
            
            if random.random() > hyperparameters['is_causal_p']:
                # TODO 避免取到跟X相同的元素
                perm2 = torch.randint(0, X_leaf.shape[1], (1,))
                Y = X_leaf[:, [perm2]]     # [seq_len, 1]
                node_y = node_XY_sampled_leaf[[perm2.item()]]
            else:
                # TODO 避免取到跟X相同的元素
                Xs = []
                if len(X_root) > 0:
                    Xs.append(X_root)
                if len(X_other) > 0:
                    Xs.append(X_other)
                if len(X_leaf) > 0:
                    Xs.append(X_leaf)
                X_remain = torch.cat(Xs, dim=1)
                perm2 = torch.randint(0, X_remain.shape[1], (1,))
                Y = X_remain[:, perm2]     # [seq_len, 1]
                
                node_xy_sample_remain = np.concatenate([
                    node_XY_sampled_root, 
                    node_XY_sampled_other, 
                    node_XY_sampled_leaf
                ])
                node_y = node_xy_sample_remain[[perm2.item()]]
            # print(f"root_num: {root_num}, leaf_num: {leaf_num}, other_num: {other_num}")
        else:
            # num_output = len(X_all)
            # num_feature = X_all[0].shape[1]
            
            if random.random() > hyperparameters['is_causal_p']:
                # TODO 避免取到跟X相同的元素
                X_leaf = torch.cat(X_leaf, dim=1)
                perm2 = torch.randint(0, X_leaf.shape[1], (1,))
                Y = X_leaf[:, [perm2]]     # [seq_len, 1]
                # 剔除已被选择的数据
                X_leaf = torch.cat([X_leaf[:,:perm2], X_leaf[:,perm2+1:]], dim=1)
                
                node_y = node_XY_sampled_leaf[[perm2.item()]]
                node_XY_sampled_leaf = np.delete(node_XY_sampled_leaf, perm2.item(), axis=0)
                # 重新组建X数据
                X_array = [torch.cat(X_root, dim = 1), X_leaf]
                if len(X_other) > 0:
                    X_array.append(torch.cat(X_other, dim = 1))
                X = torch.cat(X_array, dim=1)
                perm = torch.randperm(X.shape[1])
            else:
                X = torch.concat(X_all, dim=1)
                perm = torch.randperm(X.shape[1])
                Y = X[:, [perm[num_features_used]]]     # [seq_len, 1]
                node_y = node_XY_sampled[perm[num_features_used].item()]
                if isinstance(node_y, str):
                    node_y = np.array([node_y])
                
            X = X[:, perm[:num_features_used]]    # [seq_len, num_features_used]
            node_x = node_XY_sampled[perm[:num_features_used].numpy()]
            
        # 获取各个位置对应的DAG图编号
        prefixes_x = np.char.lstrip(np.char.partition(node_x, '_')[:, 0], '_').astype(int)
        prefixes_y = np.char.lstrip(np.char.partition(node_y, '_')[:, 0], '_').astype(int)
        
        for idx_dag in range(len(dags)):
            # 如果idx_dag编号的DAG在prefixes中出现，则记录该DAG对应的被采样的node编号
            if idx_dag in prefixes_x:
                dict_save['info']['node_sampled'][idx_dag]['x'] = []
                for val in np.char.split(node_x[prefixes_x == idx_dag], sep='_'):
                    dict_save['info']['node_sampled'][idx_dag]['x'].append([int(val[1]), int(val[2])])
            if idx_dag in prefixes_y:
                dict_save['info']['node_sampled'][idx_dag]['y'] = []
                for val in np.char.split(node_y[prefixes_y == idx_dag], sep='_'):
                    dict_save['info']['node_sampled'][idx_dag]['y'].append([int(val[1]), int(val[2])])
            # 寻找同时有x和y的node
            # if idx_dag in prefixes_x and idx_dag in prefixes_y:
            #     dag_node_xy = list(
            #         set(dict_save['info']['node_sampled'][idx_dag]['x']) 
            #         & 
            #         set(dict_save['info']['node_sampled'][idx_dag]['y'])
            #     )
            #     dict_save['info']['node_sampled'][idx_dag]['xy'] = dag_node_xy if dag_node_xy != [] else None
                
            # X = torch.concat(X, dim=1)
            # perm = torch.randperm(X.shape[1])

            # Y = X[:, [perm[num_features_used]]]     # [seq_len, 1]
            # X = X[:, perm[:num_features_used]]    # [seq_len, num_features_used]
        # input warping
        if 'input_warpings_ratio' in hyperparameters and random.random() < hyperparameters['input_warpings_ratio']:
            input_warping_groups = hyperparameters.get('input_warping_groups', 'x')

            if 'x' in input_warping_groups:
                X = input_warpping(X, hyperparameters)
                
                
        # root_flags = [root_flags[i] for i in perm.tolist()]  # 新增
        # root_count = sum(root_flags[:num_features_used])  # 统计实际使用的根特征数
        # global root_cout_all, num_features_used_all
        # root_cout_all += root_count
        # num_features_used_all += num_features_used
        # print(f"Root features: {root_count}/{num_features_used} ({root_count/num_features_used:.1%}), Average: {root_cout_all/num_features_used_all:.1%}")  # 新增统计输出
        
        return X, Y, dict_save
    
    # prof = torch.profiler.profile(
    #          activities=[
    #              torch.profiler.ProfilerActivity.CPU,
    #              torch.profiler.ProfilerActivity.CUDA
    #          ],
    #          schedule=torch.profiler.schedule(wait=1, warmup=1, active=3,repeat=1),
    #          on_trace_ready=torch.profiler.tensorboard_trace_handler('./logs'),
    #          record_shapes=True,
    #          profile_memory=True,
    #          with_stack=True,
    #          use_cuda=True
    #      )
    # prof.start()
    
    get_model = lambda: SCM_compute(seq_len, num_features, hyperparameters, device)
    sample = []

    # for i in range(batch_size):
    #     sample.append(get_model())
        # if i == 5:
        #     t1 = time.time()
        
        # prof.step()
    # prof.stop()
    # print(prof.key_averages().table(
    #     sort_by="cuda_time_total",   # 按 GPU 总耗时排序‌:ml-citation{ref="1,6" data="citationList"}
    #     row_limit=200
    # ))
    # cost = time.time()-t1
    # print(f"total: {cost * 1000}ms, aver: {cost*1000/batch_size}ms")
    sample = [get_model() for _ in range(batch_size)]
    x, y, dicts = zip(*sample)
    x = torch.stack(x, dim=0).detach()
    y = torch.stack(y, dim=0)
    x = x.transpose(1,0)
    y = y.squeeze(0)
    
    
    
    return x, y, y, dicts

DataLoader = get_batch_to_dataloader(get_batch)


if __name__ == '__main__':
    
    # mlp_sampler_modulo = normal_sampler_f(1, 2)
    # mlp_sampler_power = uniform_sampler_f(2.5, 2.5)
    # categorical_sampler_K = gamma_sampler_f(3, 2)
    # sampler_edge_noise_std = normal_sampler_f(1, 1)
    # seq_len = 1234
    # prototypes_fraction = .4
    # num_proto = int(seq_len * prototypes_fraction)
    # s = lambda: random.random()
    
    # h = {'num_features_used': 32, 
    #     #  'K': 5,
    #      'mlp_sampler_modulo': mlp_sampler_modulo,
    #      'mlp_sampler_power': mlp_sampler_power,
    #      'categorical_sampler_K': categorical_sampler_K,
    #      'sampler_edge_noise_std': sampler_edge_noise_std,
    #      'tree_num_sample_fit': 100,
    #      'tree_max_num_classes': 10,
    #      'tree_max_depth': 5,
    #      'edge_fixed_gaussian_noise': True,
    #      'sampler_prototypes_mix': lambda: random.random(),
    #      'prototypes_fraction': prototypes_fraction,   # float
    #      'prototypes_p': torch.ones(num_proto, device=default_device)/num_proto,
    #      'DAG_mode': 'multi',
    #      'scm_init_data_sampling_method': 'uniform',
    #      'init_data_uniform_scale': 1.5,
    #      'init_data_normal_std': 1,
    #      'sampler_edge_funcs_probability': np.ones(3)/3,
    #     #  'sampler_edge_funcs_probability': np.array([0.4, 0.4, 0.2]),
    #     #  'sampler_num_dag': randint_sampler(1, 4),
    #     #  'sampler_num_node': log_uniform_sampling(1.25, 2.75),
    #      'sampler_num_dag': 3,
    #      'sampler_num_node': 9,
    #      'sampler_prob': beta_sampler_f(1.5, 3),
    #      'sampler_num_dag_node': 'uniform',
    #      'parents_aggregation_method': 'mixed',
    #      'tree_leaf_prob': 0.1,
    #      'tree_threshold_func': get_threshold_func_beta}
    
    # t = []
    
    
    # prof = torch.profiler.profile(
    #         activities=[
    #             torch.profiler.ProfilerActivity.CPU,
    #             torch.profiler.ProfilerActivity.CUDA
    #         ],
    #         schedule=torch.profiler.schedule(wait=1, warmup=1, active=3,repeat=1),
    #         on_trace_ready=torch.profiler.tensorboard_trace_handler('./logs'),
    #         record_shapes=True,
    #         profile_memory=True,
    #         with_stack=True,
    #         use_cuda=True
    #     )
    # prof.start()
    
    # from tqdm import tqdm
    # for _ in tqdm(range(20)):
    #     t1 = time.time()
    #     x, y, y = get_batch(16, seq_len, h['num_features_used'], h, device=default_device)
    #     t.append(time.time() - t1)
    #     # print(time.time() - t1)
    #     prof.step()
    
    # print(np.mean(t))
    # prof.stop()
    # prof.stop()
    # TODO: 继承刚哥的决策树代码，后处理

    # 打印按 GPU 时间排序的关键算子耗时表
    # print(prof.key_averages().table(
    #     sort_by="cuda_time_total",   # 按 GPU 总耗时排序‌:ml-citation{ref="1,6" data="citationList"}
    #     row_limit=20
    # ))

    # %%
    import numpy as np
    import matplotlib.pyplot as plt
    
    gamma_sampler_f = lambda a, b, size=1 : lambda : np.random.gamma(a, b,size)
    f = gamma_sampler_f(3, 2)
    v = []
    
    for i in range(1000):
        v.append(f())
    v = np.concatenate(v)
       
    plt.hist(v, bins=20)
    
    
# %%
