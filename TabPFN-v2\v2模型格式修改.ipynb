{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4f69931b-e89b-4830-97ee-db9f9f2452c1", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": null, "id": "36d161a7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f79bfbde-7ea6-4260-a925-03eb0329e907", "metadata": {}, "outputs": [], "source": ["model_m = torch.load(open(model_ours,'rb'))\n", "model_d = torch.load(open(model_default,'rb'))\n", "my_stat = model_m[0]"]}, {"cell_type": "code", "execution_count": 7, "id": "d03e1b84-f3f6-4446-aa31-74d0ba6ca873", "metadata": {}, "outputs": [], "source": ["new_stat = {}\n", "for k, v in my_stat.items():\n", "    new_stat[k.replace('module.','')] = v"]}, {"cell_type": "code", "execution_count": null, "id": "6f854501-4312-48c3-b240-6b057c3369f3", "metadata": {}, "outputs": [], "source": ["for k, v in model_d['state_dict'].items():\n", "    model_d['state_dict'][k]=new_stat[k]"]}, {"cell_type": "code", "execution_count": null, "id": "b055366c-3572-48fb-8ea8-cea0d846e1c2", "metadata": {}, "outputs": [], "source": ["torch.save(model_d, open(model_save,'wb'))"]}], "metadata": {"kernelspec": {"display_name": "PFN", "language": "python", "name": "pfn"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}