# test_edge_func_tree.py
import torch
import pytest
import random
from priors.edge_func_tree import MLP
from priors.edge_func_tree import G<PERSON>sianN<PERSON>, MLP, Categorical, DecisionTreeClassifier_numba
from priors.edge_func_tree import decision_tree_numba_predict, DecesionTree, multi_tree
from priors.edge_func_tree import DecisionTreeClassifier, DecisionTreeRegression

from numba.extending import register_jitable
import numpy as np
import time

# ================================== GaussianNoise 测试 ================================
def test_gaussian_noise_shape():
    """测试高斯噪声的输出形状是否与输入一致"""
    hyperparameters = {'noise_std': 0.1}
    x = torch.randn(10, 5)  # seq_len=10, num_features=5
    noise = GaussianNoise(x, hyperparameters)
    assert noise.shape == x.shape, f"Expected shape {x.shape}, but got {noise.shape}"

def test_gaussian_noise_device():
    """测试输出张量是否与输入在同一设备上"""
    hyperparameters = {'noise_std': 0.1}
    for device in [torch.device('cpu'), torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')]:
        x = torch.randn(10, 5).to(device)
        noise = GaussianNoise(x, hyperparameters)
        assert noise.device == x.device, f"Expected device {x.device}, but got {noise.device}"

def test_gaussian_noise_std_zero():
    """测试噪声标准差为0时输出全零"""
    hyperparameters = {'noise_std': 0.0}
    x = torch.randn(10, 5)
    noise = GaussianNoise(x, hyperparameters)
    assert torch.allclose(noise, torch.zeros_like(x)), f"Expected all zeros, but got {noise}"

def test_gaussian_noise_std_negative():
    """测试负标准差会被自动取绝对值"""
    hyperparameters = {'noise_std': -0.5}
    x = torch.randn(10, 5)
    torch.manual_seed(0)
    noise1 = GaussianNoise(x, hyperparameters)
    hyperparameters['noise_std'] = 0.5
    torch.manual_seed(0)
    noise2 = GaussianNoise(x, hyperparameters)
    # 检查两次噪声的统计特性是否相似
    assert torch.allclose(noise2, noise1), f"Expected similar noise, but got {noise1} and {noise2}"

def test_gaussian_noise_distribution():
    """测试噪声是否符合正态分布"""
    hyperparameters = {'noise_std': 0.5}
    x = torch.randn(10000, 5)  # 大样本用于统计测试
    noise = GaussianNoise(x, hyperparameters)
    # 检查均值和标准差
    assert torch.mean(noise).item() == pytest.approx(0.0, abs=0.05), f"Expected mean 0, but got {torch.mean(noise).item()}"
    assert torch.std(noise).item() == pytest.approx(0.5, rel=0.1), f"Expected std 0.5, but got {torch.std(noise).item()}"

@pytest.mark.parametrize("std", [0.1, 0.5, 1.0, 2.0])
def test_gaussian_noise_multiple_stds(std):
    """参数化测试不同标准差下的噪声"""
    hyperparameters = {'noise_std': std}
    x = torch.randn(1000, 5)
    noise = GaussianNoise(x, hyperparameters)
    assert torch.std(noise).item() == pytest.approx(std, rel=0.1), f"Expected std {std}, but got {torch.std(noise).item()}"

def test_gaussian_noise_deterministic_seed():
    """测试在固定随机种子下噪声是否确定"""
    hyperparameters = {'noise_std': 0.3}
    x = torch.randn(10, 5)
    
    torch.manual_seed(42)
    noise1 = GaussianNoise(x, hyperparameters)
    
    torch.manual_seed(42)
    noise2 = GaussianNoise(x, hyperparameters)
    
    assert torch.allclose(noise1, noise2), f"Expected deterministic noise, but got {noise1} and {noise2}"


# ================================== MLP 测试 =====================================
def test_mlp_output_shape():
    """测试MLP输出形状是否正确"""
    hyperparameters = {
        'num_features_used': 10,
        'mlp_sampler_power': lambda: 1,
        'mlp_sampler_modulo': lambda: 1,
        'edge_output_dim_scale': 2,
        "using_v1_activations": False,
        "rank_operation": True,
        "rank_sampler_num_classes": 10,
        "rank_balanced": True,
        "rank_sampler_temp": 1.0
    }
    x = torch.randn(5, 20)  # seq_len=5, num_input=20
    output,_  = MLP(x, hyperparameters)
    assert output.shape == (5, 20), f"Expected output shape (5, 20), but got {output.shape}" # num_features_used * scale = 10*2=20
    
    # 测试不同输入维度
    x = torch.randn(10, 15)
    output,_  = MLP(x, hyperparameters)
    assert output.shape == (10, 20), f"Expected output shape (10, 20), but got {output.shape}"
    
    # 测试不同输入维度
    x = torch.randn(15, 35)
    output,_  = MLP(x, hyperparameters)
    assert output.shape == (15, 20), f"Expected output shape (15, 20), but got {output.shape}"

# def test_mlp_device_consistency():
#     """测试MLP在不同设备上输出是否一致, GPU与CPU随机算法存在差异，无法对比
#        即使设置了相同的随机种子，nn.init.xavier_uniform_ 在 CPU 和 GPU 之间的结果可能会有所不同，主要是由于 torch.rand 的实现细节导致的微小数值差异。如果应用对数值精确一致性要求很高，建议在 CPU 进行初始化后再转移到 GPU
#     """
#     hyperparameters = {
#         'num_features_used': 8,
#         'mlp_sampler_power': lambda: 1,
#         'mlp_sampler_modulo': lambda: 1,
#         'edge_output_dim_scale': 3
#     }
#     x_cpu = torch.randn(3, 16)
#     x_gpu = x_cpu.to('cuda') if torch.cuda.is_available() else x_cpu
    
#     torch.manual_seed(0)
#     random.seed(0)
#     if torch.cuda.is_available():
#         torch.cuda.manual_seed_all(0)
#     output_cpu = MLP(x_cpu, hyperparameters)
#     torch.manual_seed(0)
#     random.seed(0)
#     if torch.cuda.is_available():
#         torch.cuda.manual_seed_all(0)
#     output_gpu = MLP(x_gpu, hyperparameters)
    
#     if torch.cuda.is_available():
#         assert torch.allclose(output_cpu, output_gpu.cpu(), atol=1e-6), f"Expected output to be the same on CPU and GPU, but got {output_cpu[:,:4]} and {output_gpu.cpu()[:,:4]}"
#     else:
#         assert torch.allclose(output_cpu, output_gpu, atol=1e-6), f"Expected output to be the same on CPU and GPU, but got {output_cpu} and {output_gpu}"

def test_mlp_activation_functions():
    """测试不同激活函数下的输出范围"""
    hyperparameters = {
        'num_features_used': 5,
        'mlp_sampler_power': lambda: 1,
        'mlp_sampler_modulo': lambda: 1,
        'edge_output_dim_scale': 4,
        "using_v1_activations": False,
        "rank_operation": True,
        "rank_sampler_num_classes": 10,
        "rank_balanced": True,
        "rank_sampler_temp": 1.0
    }
    x = torch.randn(2, 10)
    
    # 测试abs激活
    output_relu,_  = MLP(x, hyperparameters, act_func_name='abs')
    assert (output_relu >= 0).all(), f"Expected all positive values, but got {output_relu}"
    
    # 测试Sigmoid激活
    output_sigmoid,_  = MLP(x, hyperparameters, act_func_name='sigmoid')
    assert (output_sigmoid >= 0).all() and (output_sigmoid <= 1).all(), f"Expected values between 0 and 1, but got {output_sigmoid}"

def test_mlp_gradient_flow():
    """测试梯度是否被正确禁用"""
    hyperparameters = {
        'num_features_used': 6,
        'mlp_sampler_power': lambda: 1,
        'mlp_sampler_modulo': lambda: 1,
        'edge_output_dim_scale': 2,
        "using_v1_activations": False,
        "rank_operation": True,
        "rank_sampler_num_classes": 10,
        "rank_balanced": True,
        "rank_sampler_temp": 1.0
    }
    x = torch.randn(4, 12, requires_grad=True)
    output,_  = MLP(x, hyperparameters)
    
    # 尝试计算梯度应该报错
    with pytest.raises(RuntimeError):
        output.sum().backward()

def test_mlp_extreme_inputs():
    """测试极端输入情况（全零/大数值输入）"""
    hyperparameters = {
        'num_features_used': 3,
        'mlp_sampler_power': lambda: 1,
        'mlp_sampler_modulo': lambda: 1,
        'edge_output_dim_scale': 5,
        "using_v1_activations": False,
        "rank_operation": True,
        "rank_sampler_num_classes": 10,
        "rank_balanced": True,
        "rank_sampler_temp": 1.0
    }
    
    # 全零输入
    x_zeros = torch.zeros(3, 9)
    output_zeros,_  = MLP(x_zeros, hyperparameters)
    assert output_zeros.shape == (3, 15), f"Expected output shape (3, 15)"  # 3 * 5 = 15
    assert not torch.isnan(output_zeros).any(), f"Expected no NaN values in output, but got {output_zeros}"
    
    # 大数值输入
    x_large = torch.randn(2, 9) * 1e6
    output_large,_  = MLP(x_large, hyperparameters)
    assert output_large.shape == (2, 15), f"Expected output shape (2, 15)"  # 3 * 5 = 15
    assert not torch.isnan(output_large).any(), f"Expected no NaN values in output, but got {output_large}"

@pytest.mark.parametrize("seq_len,num_input", [(1, 5), (10, 1), (100, 50)])
def test_mlp_various_shapes(seq_len, num_input):
    """测试不同输入形状下的输出"""
    hyperparameters = {
        'num_features_used': 4,
        'mlp_sampler_power': lambda: 1,
        'mlp_sampler_modulo': lambda: 1,
        'edge_output_dim_scale': 3,
        "using_v1_activations": False,
        "rank_operation": True,
        "rank_sampler_num_classes": 10,
        "rank_balanced": True,
        "rank_sampler_temp": 1.0
    }
    x = torch.randn(seq_len, num_input)
    output,_ = MLP(x, hyperparameters)
    expected_features = hyperparameters['num_features_used'] * hyperparameters['edge_output_dim_scale']
    assert output.shape == (seq_len, expected_features), f"Expected output shape ({seq_len}, {expected_features}), but got {output.shape}"



# ================================= Categorical 测试 ====================================
def test_categorical_basic():
    """测试基本功能"""
    x = torch.tensor([[0.1, 0.2], [0.3, 0.4]])
    hyperparameters = {
        'categorical_sampler_K': lambda: 3,
        'num_features_used': 2,
        'edge_output_dim_scale': 2
    }
    output, _ = Categorical(x, hyperparameters)
    assert output.shape == (2, 4), f"Expected output shape (2, 4), but got {output.shape}"  # 2 features * scale=2

def test_categorical_min_K():
    """测试K的最小值为2"""
    x = torch.tensor([[0.1], [0.2]])
    hyperparameters = {
        'categorical_sampler_K': lambda: 1,  # 小于2的值
        'num_features_used': 1,
        'edge_output_dim_scale': 1
    }
    torch.manual_seed(0)
    output, _ = Categorical(x, hyperparameters)
    
    hyperparameters2 = {
        'categorical_sampler_K': lambda: 2,  # 小于2的值
        'num_features_used': 1,
        'edge_output_dim_scale': 1
    }
    torch.manual_seed(0)
    output2, _ = Categorical(x, hyperparameters2)
    
    hyperparameters3 = {
        'categorical_sampler_K': lambda: 3,  # 小于2的值
        'num_features_used': 1,
        'edge_output_dim_scale': 1
    }
    torch.manual_seed(0)
    output3, _ = Categorical(x, hyperparameters3)
    
    assert output.shape == (2, 1), f"Expected output shape (2, 1), but got {output.shape}"
    assert torch.allclose(output, output2), f"Expected output to be the same for K=1 and K=2, but got {output} and {output2}"
    assert not torch.allclose(output2, output3), f"Expected output to be different for different seeds, but got {output2} and {output3}"

def test_categorical_different_scales():
    """测试不同scale值"""
    x = torch.tensor([[0.1, 0.2, 0.3]])
    hyperparameters = {
        'categorical_sampler_K': lambda: 4,
        'num_features_used': 3,
        'edge_output_dim_scale': 3
    }
    torch.manual_seed(0)
    output, _ = Categorical(x, hyperparameters)
    assert output.shape == (1, 9), f"Expected output shape (1, 9), but got {output.shape}"  # 3 features * scale=3
    
    x = torch.tensor([[0.1, 0.2, 0.3, 0.5, 0.6]])
    hyperparameters = {
        'categorical_sampler_K': lambda: 4,
        'num_features_used': 3,
        'edge_output_dim_scale': 3
    }
    torch.manual_seed(0)
    output, _ = Categorical(x, hyperparameters)
    assert output.shape == (1, 9), f"Expected output shape (1, 9), but got {output.shape}"  # 3 features * scale=3
    
    hyperparameters = {
        'categorical_sampler_K': lambda: 4,
        'num_features_used': 3,
        'edge_output_dim_scale': 6
    }
    torch.manual_seed(0)
    output, _ = Categorical(x, hyperparameters)
    assert output.shape == (1, 18), f"Expected output shape (1, 18), but got {output.shape}"  # 3 features * scale=3

def test_categorical_large_K():
    """测试较大的K值"""
    x = torch.randn(10, 5)  # 随机数据
    hyperparameters = {
        'categorical_sampler_K': lambda: 10,
        'num_features_used': 5,
        'edge_output_dim_scale': 2
    }
    output, _ = Categorical(x, hyperparameters)
    assert output.shape == (10, 10),f" Expected output shape (10, 10), but got {output.shape}"  # 5 features * scale=2

# def test_categorical_device():
#     """测试不同设备(cpu/gpu)
#        CPU 和 GPU 可能使用不同的 PRNG（伪随机数生成器）。
#        CPU 默认使用的是 MT19937（Mersenne Twister），而 GPU 可能使用 Philox 或 MTGP32。
#        GPU 是并行计算的，而 CPU 是顺序执行的，即使种子相同，GPU 上的操作顺序可能与 CPU 不同，进而影响 torch.rand 的结果"""
#     devices = ['cpu']
#     if torch.cuda.is_available():
#         devices.append('cuda')
#     else:
#         devices.append('cpu')
    
#     outputs = []
#     for device in devices:
#         x = torch.tensor([[0.1, 0.2], [0.3, 0.4]], device=device)
#         hyperparameters = {
#             'categorical_sampler_K': lambda: 3,
#             'num_features_used': 2,
#             'edge_output_dim_scale': 2
#         }
#         torch.manual_seed(0)
#         random.seed(0)
#         if torch.cuda.is_available():
#             torch.cuda.manual_seed_all(0)
#         output = Categorical(x, hyperparameters)
#         assert output.device == x.device, f"Expected output device to be {x.device}, but got {output.device}"
#         assert output.shape == (2, 4), f"Expected output shape (2, 4), but got {output.shape}"
#         outputs.append(output.cpu())
#     assert torch.allclose(outputs[0], outputs[1]), f"Expected output to be the same on different devices, but got {outputs[0]} and {outputs[1]}"

def test_categorical_grad():
    """测试梯度计算"""
    x = torch.tensor([[0.1, 0.2]], requires_grad=True)
    hyperparameters = {
        'categorical_sampler_K': lambda: 2,
        'num_features_used': 2,
        'edge_output_dim_scale': 1
    }
    output, _ = Categorical(x, hyperparameters)
    # 确保输出不保留梯度
    assert output.requires_grad == False, f"Expected output to not require gradients, but got {output.requires_grad}"

def test_categorical_randomness():
    """测试随机性-相同输入不同输出"""
    x = torch.tensor([[0.5, 0.5]])
    hyperparameters = {
        'categorical_sampler_K': lambda: 3,
        'num_features_used': 2,
        'edge_output_dim_scale': 1
    }
    output1, _ = Categorical(x, hyperparameters)
    output2, _ = Categorical(x, hyperparameters)
    # 由于随机初始化，输出应该不同
    assert not torch.allclose(output1, output2), f"Expected output to be different for different runs, but got {output1} and {output2}"

@pytest.mark.parametrize("seq_len,num_features", [(1,1), (5,3), (10,10)])
def test_categorical_shapes(seq_len, num_features):
    """参数化测试不同形状输入"""
    x = torch.rand(seq_len, num_features)
    hyperparameters = {
        'categorical_sampler_K': lambda: 3,
        'num_features_used': num_features,
        'edge_output_dim_scale': 2
    }
    output, _ = Categorical(x, hyperparameters)
    assert output.shape == (seq_len, num_features * 2), f"Expected output shape ({seq_len}, {num_features * 2}), but got {output.shape}"

# =================================== DecisionTreeClassifier_numba 测试 =========================================

from graphviz import Digraph 
def visualize_decision_tree(node_array, filename='decision_tree'):
    """
    使用graphviz可视化决策树结构
    
    参数:
        node_array (np.ndarray): 决策树节点数组
        filename (str): 输出文件名（不带扩展名）
        feature_names (list): 可选的特征名称列表
    """
    from graphviz import Digraph
    
    dot = Digraph()
    
    # [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
    def add_nodes(node_idx):
        node = node_array[node_idx]
        node_id = str(node_idx)
        
        if node[0] == 1:  # 叶子节点
            dot.node(node_id, f"N: {node_id}\n{int(node[3])}", 
                    shape='ellipse', style='filled', fillcolor='lightblue')
        else:  # 非叶子节点 [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
            dot.node(node_id, 
                    f"N: {node_id}\nI: {node[1]}\nT: {node[2]:.3f}",
                    shape='ellipse', style='filled', fillcolor='lightyellow')
            
            # 递归添加左子节点
            if node[4] != -1:
                add_nodes(int(node[4]))
                dot.edge(node_id, str(int(node[4])), label="l")
            
            # 递归添加右子节点
            if node[5] != -1:
                add_nodes(int(node[5]))
                dot.edge(node_id, str(int(node[5])), label="g")
    
    # 从根节点开始构建树
    add_nodes(0)
    
    # 渲染并保存为png
    dot.render(filename, view=False, format='png', cleanup=True)

@register_jitable
def numba_get_threshold_func_beta(a, b):
    return np.random.beta(0.95, 8) * (b - a) + a

@register_jitable
def numba_get_threshold_func_uniform(a, b):
    return np.random.random() * (b - a) + a

def test_DecisionTreeClassifier_uniform():
    def get_threshold_func(a,b):
        return random.random() * (b - a) + a
    def get_threshold_func_beta(a,b):
        return np.random.beta(0.95, 8) * (b - a) + a
    
    torch.manual_seed(0)
    x = torch.rand(5, 10)*2-1
    base_seed = 0
    tree, output = DecisionTreeClassifier(x.numpy(), 5, 10, 0.2, base_seed, numba_get_threshold_func_uniform)
    visualize_decision_tree(tree, filename='decision_tree')
    assert output.shape == (5,), f"Expected output shape (5,), but got {output.shape}"
    assert (output == [7,8,7,7,8]).all(), f"Expected output to be [7,8,7,7,8], but got {output}"

def test_DecisionTreeClassifier_beta():
    def get_threshold_func(a,b):
        return random.random() * (b - a) + a
    def get_threshold_func_beta(a,b):
        return np.random.beta(0.95, 8) * (b - a) + a
    
    torch.manual_seed(0)
    x = torch.rand(5, 10)*2-1
    base_seed = 2
    tree, output = DecisionTreeClassifier(x.numpy(), 5, 10, 0.2, base_seed, numba_get_threshold_func_beta)
    visualize_decision_tree(tree, filename='decision_tree')
    assert output.shape == (5,), f"Expected output shape (5,), but got {output.shape}"
    assert (output == [1,7,1,1,1]).all(), f"Expected output to be [7,8,7,7,8], but got {output}"

def test_multi_tree_value():
    torch.manual_seed(0)
    x = torch.rand(10, 16)
    results,_ = multi_tree(x.numpy(), x.shape[1], 5, 10, 0.2, 0, 0)
    
    results = results.astype(np.int32)
    res = np.array(([6, 7, 1, 2, 6, 4, 7, 4, 1, 1, 6, 2, 4, 2, 4, 0],
                    [6, 7, 7, 2, 3, 1, 7, 4, 0, 4, 7, 2, 4, 2, 4, 8],
                    [1, 3, 7, 2, 2, 4, 7, 4, 6, 1, 7, 2, 4, 2, 5, 3],
                    [1, 7, 1, 2, 9, 4, 7, 8, 0, 8, 6, 2, 4, 2, 5, 5],
                    [8, 3, 1, 2, 8, 1, 7, 3, 0, 5, 7, 6, 4, 2, 5, 8],
                    [6, 7, 1, 2, 8, 4, 7, 8, 1, 1, 6, 2, 9, 2, 4, 5],
                    [7, 7, 1, 2, 9, 4, 7, 9, 0, 9, 7, 2, 4, 2, 4, 8],
                    [6, 7, 1, 2, 8, 4, 7, 4, 6, 4, 6, 2, 4, 2, 1, 0],
                    [8, 3, 1, 2, 2, 4, 7, 8, 6, 5, 6, 2, 4, 2, 5, 5],
                    [8, 7, 7, 7, 2, 4, 7, 8, 6, 5, 6, 2, 4, 2, 5, 5])).astype(np.int32)
    assert (results == res).all(), f"Expected output to be {res}, but got {results}"

def test_multi_tree_speed():
    # return 
    def get_threshold_func(a,b):
        return random.random() * (b - a) + a
    def get_threshold_func_beta(a,b):
        return np.random.beta(0.95, 8) * (b - a) + a
    
    # 测试新版决策树
    x = torch.rand(1000, 32)
    random.seed(0)
    t_old = []
    t_new = []
    for i in range(100):
        t1 = time.time()
        nodes_infos = []
        for _ in range(32):
            tree = DecisionTreeClassifier_numba(5, 10, 0.2, 10, get_threshold_func)
            tree.fit(x.shape[1])
            nodes_infos.append(tree._convert_tree_to_array())
        output = decision_tree_numba_predict(x.cpu().numpy().astype(np.float32), nodes_infos)
        t2 = time.time()
        
        t_old.append(t2 - t1)
    random.seed(0)
    for i in range(100):
        t1 = time.time()
        results,_ = multi_tree(x.numpy(), x.shape[1], 5, 10, 0.2, 0, 0)
        results = torch.as_tensor(results.T).float()
        # multi_tree2(x.numpy(), 5, 10, 0.2)
        t2 = time.time()
        t_new.append(t2 - t1)
        
    old_mean = np.mean(t_old[10:]) * 1000
    new_mean = np.mean(t_new[10:]) * 1000
    # print(f"t_old: {t_old}")
    # print(f"t_new: {t_new}")
    print(f"old function mean: {old_mean}ms, new function mean: {new_mean}ms")
    # print(f"old function mean: {old_mean}ms, new function mean: {new_mean}ms")

    assert old_mean > 5 * new_mean, f"Expected new function to be faster than old function, but got {old_mean}ms vs {new_mean}ms"

# =================================== decision tree regression 测试 =========================================

def visualize_decision_tree_reg(node_array, filename='decision_tree_reg'):
    """
    使用graphviz可视化决策树结构
    
    参数:
        node_array (np.ndarray): 决策树节点数组
        filename (str): 输出文件名（不带扩展名）
        feature_names (list): 可选的特征名称列表
    """
    from graphviz import Digraph
    
    dot = Digraph()
    
    # [is_leaf, feature_idx, threshold, value_index, left_child_idx, right_child_idx]
    def add_nodes(node_idx):
        node = node_array[node_idx]
        node_id = str(node_idx)
        
        if node[0] == 1:  # 叶子节点
            dot.node(node_id, f"N: {node_id}\nLI: {int(node[3])}", 
                    shape='ellipse', style='filled', fillcolor='lightblue')
        else:  # 非叶子节点 [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
            dot.node(node_id, 
                    f"N: {node_id}\nI: {node[1]}\nT: {node[2]:.6f}",
                    shape='ellipse', style='filled', fillcolor='lightyellow')
            
            # 递归添加左子节点
            if node[4] != -1:
                add_nodes(int(node[4]))
                dot.edge(node_id, str(int(node[4])), label="l")
            
            # 递归添加右子节点
            if node[5] != -1:
                add_nodes(int(node[5]))
                dot.edge(node_id, str(int(node[5])), label="g")

    # 从根节点开始构建树
    add_nodes(0)
    
    # 渲染并保存为png
    dot.render(filename, view=False, format='png', cleanup=True)


def test_DecisionTreeRegression():
    import csv
    params = {
            "reg_tree_max_depth": 5,
            "reg_tree_leaf_prob": 0.2,
            "reg_tree_input_weight": 0.5,
            "reg_tree_threshold_mode": 'beta_3_3'
        }
    torch.manual_seed(0)
    random.seed(0)
    np.random.seed(0)
    
    x = torch.rand(1000, 32)
    output, infos = DecisionTreeRegression(x, params, 3)
    
    visualize_decision_tree_reg(infos['tree'], "100_reg_tree")
    
    x_list = x.tolist()
    output_list = output.tolist()
    with open("100_input.csv", "w", newline="") as file:
        writer = csv.writer(file)
        for row in x_list:
            writer.writerow(row)
    with open("100_output.csv", "w", newline="") as file:
        writer = csv.writer(file)
        for row in output_list:
            writer.writerow(row)
    
    leaf_values = infos["leaf_values"].tolist()
    with open("100_leaf_value.csv", "w", newline="") as file:
        writer = csv.writer(file)
        for row in leaf_values:
            writer.writerow(row)
    
    leaf2 = np.array([0.49431344866752625,0.49899712204933167,0.5076663494110107,0.5062621831893921,0.4977220296859741,0.4986078441143036,0.5010040998458862,0.5233030915260315,0.499066025018692,0.4912773370742798,0.5182586908340454,0.5096402168273926,0.5009644627571106,0.4953318238258362,0.49218833446502686,0.5041902661323547,0.48650920391082764,0.516887366771698,0.48326876759529114,0.4912951588630676,0.5143734216690063,0.4986788332462311,0.5040839910507202,0.4904017448425293,0.5102344751358032,0.6713993549346924,0.5011253356933594,0.519572913646698,0.4979170858860016,0.5023125410079956,0.5039207935333252,0.511781632900238])
    leaf8 = np.array([0.5299916863441467,0.48532453179359436,0.8702129125595093,0.5587318539619446,0.45447808504104614,0.4726712703704834,0.6928169131278992,0.5626171827316284,0.4904986619949341,0.5306463241577148,0.44882944226264954,0.47092100977897644,0.4271237850189209,0.5521360039710999,0.5188300013542175,0.5276954770088196,0.43364417552948,0.533487856388092,0.06894940882921219,0.5130360722541809,0.511273980140686,0.4242423474788666,0.4303155243396759,0.517764687538147,0.4274629056453705,0.20366017520427704,0.4060169756412506,0.49085795879364014,0.42972004413604736,0.504474401473999,0.5650097131729126,0.4188518524169922])
    leaf13 = np.array([0.5015267729759216,0.5467475652694702,0.8392137885093689,0.591485321521759,0.4829740822315216,0.5925813913345337,0.49236682057380676,0.46998387575149536,0.45657140016555786,0.4470615088939667,0.4986817240715027,0.44668132066726685,0.5592280030250549,0.5639569163322449,0.45762673020362854,0.614639163017273,0.42021796107292175,0.43447914719581604,0.5610607862472534,0.48599791526794434,0.5860064625740051,0.4553075134754181,0.5397908687591553,0.4293658137321472,0.4298466145992279,0.1961994469165802,0.5103037357330322,0.4827269911766052,0.36964669823646545,0.5459292531013489,0.5650010108947754,0.4334908425807953])
    assert np.allclose(leaf_values[2], leaf2), f"expect: {leaf2[:5]}, but got: {leaf_values[2][:5]}"
    assert np.allclose(leaf_values[8], leaf8), f"expect: {leaf8[:5]}, but got: {leaf_values[8][:5]}"
    assert np.allclose(leaf_values[13], leaf13), f"expect: {leaf13[:5]}, but got: {leaf_values[13][:5]}"
    
    output11 = torch.tensor([0.45217156410217285,0.5968266725540161,0.5482386350631714,0.609498143196106,0.4139041304588318,0.6211967468261719,0.32588431239128113,0.5681152939796448,0.3303850293159485,0.2490042746067047,0.3083654046058655,0.7021876573562622,0.635741114616394,0.7322198152542114,0.6963744759559631,0.2788337767124176,0.322646826505661,0.4680480360984802,0.32927632331848145,0.6692466735839844,0.3181847631931305,0.37736403942108154,0.2605191469192505,0.3532644808292389,0.7107366323471069,0.7903896570205688,0.6795220971107483,0.7028182744979858,0.7212551832199097,0.4371405243873596,0.6119608879089355,0.7286233901977539])
    output30 = torch.tensor([0.3363276422023773,0.5464597344398499,0.9127424359321594,0.28346434235572815,0.22864100337028503,0.5183668732643127,0.7846640348434448,0.37918615341186523,0.5502836108207703,0.691069483757019,0.5514938235282898,0.5395631790161133,0.7129933834075928,0.688936173915863,0.36880823969841003,0.5354568958282471,0.5945401787757874,0.7372213006019592,0.03574148193001747,0.6418943405151367,0.43305402994155884,0.21731586754322052,0.5206512808799744,0.37277892231941223,0.4991595149040222,0.18951836228370667,0.26487624645233154,0.4742162227630615,0.38087859749794006,0.6484533548355103,0.5540593862533569,0.6545655727386475])
    assert torch.allclose(output[11, :], output11), f"expect: {output11[:5]}, but got: {output[11, :5]}"
    assert torch.allclose(output[30, :], output30), f"expect: {output30[:5]}, but got: {output[30, :5]}"


if __name__ == "__main__":
    pytest.main(["-v", "test_edge_func_tree.py"])