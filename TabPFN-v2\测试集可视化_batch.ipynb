{"cells": [{"cell_type": "code", "execution_count": 15, "id": "1a07549a-b5e7-4ac3-8c56-8d38d1fab8f0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline\n", "import seaborn as sns\n", "import numpy as np\n", "import matplotlib.font_manager as fm\n", "\n", "# 定义字体文件的路径，这里假设字体文件名为 myfont.ttf，且与代码文件在同一目录下\n", "font_path = 'TabPFN-tabpfn_v1/tabpfn/simhei.ttf'\n", "# 加载字体文件\n", "font_prop = fm.FontProperties(fname=font_path)"]}, {"cell_type": "code", "execution_count": 16, "id": "d0c41207", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "end_batch=376\n", "base_dir = '/home/<USER>/workspace/TabPFN/TabPFN-tabpfn_v1/tabpfn/0208/'\n", "\n", "# 读取第一个文件作为 data_AUC_PFN 和 data_AUC_xgboost 的初始值\n", "epoch_number = str(0).zfill(3)\n", "data_AUC_PFN = pd.read_csv(f'/{base_dir}/v1_kaggle_classifier_v1_large_epoch_{epoch_number}/all_rst.csv').rename(columns={'AUC_PFN': f'PFN_{epoch_number}'})\n", "data_AUC_xgboost = pd.read_csv(f'/{base_dir}/v1_kaggle_classifier_v1_large_epoch_{epoch_number}/all_rst.csv').rename(columns={'AUC_xgboost': f'xgboost_{epoch_number}'})\n", "\n", "# 定义需要保留的公共列\n", "common_columns = ['dataset name', 'num_data_train', 'num_data_test', 'num_feat', 'num_class', 'domain']\n", "\n", "# 只保留需要的列\n", "data_AUC_PFN = data_AUC_PFN[common_columns + [f'PFN_{epoch_number}']]\n", "data_AUC_xgboost = data_AUC_xgboost[common_columns + [f'xgboost_{epoch_number}']]\n", "\n", "# 原始单次执行代码转换为循环结构\n", "for epoch in range(5, end_batch, 5):  # 从 5 开始，因为第一个文件已经读取\n", "    epoch_number = str(epoch).zfill(3)\n", "    # 读取当前 epoch 的文件\n", "    current_data = pd.read_csv(f'/{base_dir}/v1_kaggle_classifier_v1_large_epoch_{epoch_number}/all_rst.csv')\n", "\n", "    # 只选择需要的列\n", "    current_AUC_PFN = current_data[['dataset name', 'AUC_PFN']].rename(columns={'AUC_PFN': f'PFN_{epoch_number}'})\n", "    current_AUC_xgboost = current_data[['dataset name', 'AUC_xgboost']].rename(columns={'AUC_xgboost': f'xgboost_{epoch_number}'})\n", "\n", "    # 合并 AUC_PFN 数据，指定合并键为 'dataset name'\n", "    data_AUC_PFN = data_AUC_PFN.merge(\n", "        current_AUC_PFN,\n", "        on='dataset name'\n", "    )\n", "\n", "    # 合并 AUC_xgboost 数据，指定合并键为 'dataset name'\n", "    data_AUC_xgboost = data_AUC_xgboost.merge(\n", "        current_AUC_xgboost,\n", "        on='dataset name'\n", "    )\n", "\n", "# 删除包含缺失值的行\n", "data_AUC_PFN.dropna(inplace=True)\n", "data_AUC_xgboost.dropna(inplace=True)\n", "\n", "# 去除 data_AUC_PFN 中除 AUC_PFN 相关列外的重复列\n", "cols_to_keep_pfn = []\n", "seen = set()\n", "for col in data_AUC_PFN.columns:\n", "    if 'AUC_PFN' in col or col not in seen:\n", "        cols_to_keep_pfn.append(col)\n", "        seen.add(col)\n", "data_AUC_PFN = data_AUC_PFN[cols_to_keep_pfn]\n", "\n", "# 去除 data_AUC_xgboost 中除 AUC_xgboost 相关列外的重复列\n", "cols_to_keep_xgboost = []\n", "seen = set()\n", "for col in data_AUC_xgboost.columns:\n", "    if 'AUC_xgboost' in col or col not in seen:\n", "        cols_to_keep_xgboost.append(col)\n", "        seen.add(col)\n", "data_AUC_xgboost = data_AUC_xgboost[cols_to_keep_xgboost]\n"]}, {"cell_type": "code", "execution_count": null, "id": "6042b3f3", "metadata": {}, "outputs": [], "source": ["# 显示 data_AUC_PFN 的最后几行\n", "data_AUC_PFN.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1fece888-5dd5-4cb2-a1cb-3a70b7093516", "metadata": {}, "outputs": [], "source": ["data_AUC_xgboost.head()"]}, {"cell_type": "code", "execution_count": null, "id": "96480cef", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "base_dir = '/home/<USER>/workspace/TabPFN/TabPFN-tabpfn_v1/tabpfn'\n", "\n", "# 假设 data_AUC_PFN 和 data_AUC_xgboost 已按之前代码生成好\n", "# 这里假设 data_AUC_PFN 有 'dataset name' 和以 PFN_ 开头的各列，data_AUC_xgboost 类似\n", "\n", "# 自动生成 epoch_numbers\n", "epoch_numbers = [str(epoch).zfill(3) for epoch in range(0, end_batch, 5)]\n", "\n", "# 假设 x 轴数据是从文件中读取的一些索引值\n", "x = np.arange(len(epoch_numbers))\n", "\n", "# 准备三组数据的均值和标准差列表\n", "y_means = []\n", "y_stds = []\n", "\n", "# 从 data_AUC_PFN 中提取各 epoch 的均值数据作为第一组 y 轴数据示例\n", "y1_mean = [data_AUC_PFN[f'PFN_{epoch}'].mean() for epoch in epoch_numbers]\n", "y1_std = [data_AUC_PFN[f'PFN_{epoch}'].std() for epoch in epoch_numbers]  # 真实标准差\n", "y_means.append(y1_mean)\n", "y_stds.append(y1_std)\n", "\n", "# 从 data_AUC_xgboost 中提取各 epoch 的均值数据作为第二组 y 轴数据示例\n", "y2_mean = [data_AUC_xgboost[f'xgboost_{epoch}'].mean() for epoch in epoch_numbers]\n", "y2_std = [data_AUC_xgboost[f'xgboost_{epoch}'].std() for epoch in epoch_numbers]  # 真实标准差\n", "y_means.append(y2_mean)\n", "y_stds.append(y2_std)\n", "\n", "# 绘图\n", "plt.figure(figsize=(24, 12))\n", "\n", "# 自动扩充绘图语句\n", "for i in range(len(y_means)):\n", "    if i == 0:\n", "        plt.errorbar(x*50, y_means[i], yerr=y_stds[i], fmt='o-', label=f'PFN', capsize=5, zorder=len(y_means)-i)\n", "    else:\n", "        plt.errorbar(x*50, y_means[i], yerr=y_stds[i], fmt='o-', label=f'xgboost', capsize=5, zorder=len(y_means)-i)\n", "\n", "plt.xlabel('XAxis')\n", "plt.ylabel('YAxis')\n", "plt.title('<PERSON><PERSON><PERSON>ar Plot')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a7d92c56-0463-4966-9aaa-a0a07065db48", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "base_dir = '/home/<USER>/workspace/TabPFN/TabPFN-tabpfn_v1/tabpfn'\n", "\n", "# 假设 data_AUC_PFN 和 data_AUC_xgboost 已按之前代码生成好\n", "# 这里假设 data_AUC_PFN 有 'dataset name' 和以 PFN_ 开头的各列，data_AUC_xgboost 类似\n", "\n", "# 自动生成 epoch_numbers\n", "epoch_numbers = [str(epoch).zfill(3) for epoch in range(0, end_batch, 5)]\n", "\n", "# 假设 x 轴数据是从文件中读取的一些索引值\n", "x = np.arange(len(epoch_numbers))\n", "\n", "# 准备三组数据的均值和标准差列表\n", "y_means = []\n", "y_stds = []\n", "\n", "# 在计算均值和标准差前添加过滤条件\n", "data_AUC_PFN_filtered = data_AUC_PFN[data_AUC_PFN['num_data_train'] > 5000]\n", "data_AUC_xgboost_filtered = data_AUC_xgboost[data_AUC_xgboost['num_data_train'] > 5000]\n", "\n", "\n", "# 从 data_AUC_PFN 中提取各 epoch 的均值数据作为第一组 y 轴数据示例\n", "y1_mean = [data_AUC_PFN_filtered[f'PFN_{epoch}'].mean() for epoch in epoch_numbers]\n", "y1_std = [data_AUC_PFN_filtered[f'PFN_{epoch}'].std() for epoch in epoch_numbers]\n", "\n", "y_means.append(y1_mean)\n", "y_stds.append(y1_std)\n", "\n", "# 从 data_AUC_xgboost 中提取各 epoch 的均值数据作为第二组 y 轴数据示例\n", "y2_mean = [data_AUC_xgboost_filtered[f'xgboost_{epoch}'].mean() for epoch in epoch_numbers]\n", "y2_std = [data_AUC_xgboost_filtered[f'xgboost_{epoch}'].std() for epoch in epoch_numbers]\n", "\n", "y_means.append(y2_mean)\n", "y_stds.append(y2_std)\n", "\n", "# 绘图\n", "plt.figure(figsize=(24, 12))\n", "\n", "# 自动扩充绘图语句\n", "for i in range(len(y_means)):\n", "    if i == 0:\n", "        plt.errorbar(x*50, y_means[i], yerr=y_stds[i], fmt='o-', label=f'PFN', capsize=5, zorder=len(y_means)-i)\n", "    else:\n", "        plt.errorbar(x*50, y_means[i], yerr=y_stds[i], fmt='o-', label=f'xgboost', capsize=5, zorder=len(y_means)-i)\n", "\n", "plt.xlabel('XAxis')\n", "plt.ylabel('YAxis')\n", "plt.title('Errorbar Plot (num_data_train > 5000)')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8ce923c6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "base_dir = '/home/<USER>/workspace/TabPFN/TabPFN-tabpfn_v1/tabpfn'\n", "\n", "# 假设 data_AUC_PFN 和 data_AUC_xgboost 已按之前代码生成好\n", "# 这里假设 data_AUC_PFN 有 'dataset name' 和以 PFN_ 开头的各列，data_AUC_xgboost 类似\n", "\n", "# 自动生成 epoch_numbers\n", "epoch_numbers = [str(epoch).zfill(3) for epoch in range(0, end_batch, 5)]\n", "\n", "# 假设 x 轴数据是从文件中读取的一些索引值\n", "x = np.arange(len(epoch_numbers))\n", "\n", "# 准备三组数据的均值和标准差列表\n", "y_means = []\n", "y_stds = []\n", "\n", "# 在计算均值和标准差前添加过滤条件\n", "data_AUC_PFN_filtered = data_AUC_PFN[data_AUC_PFN['num_data_train'] < 5000]\n", "data_AUC_xgboost_filtered = data_AUC_xgboost[data_AUC_xgboost['num_data_train'] < 5000]\n", "\n", "\n", "# 从 data_AUC_PFN 中提取各 epoch 的均值数据作为第一组 y 轴数据示例\n", "y1_mean = [data_AUC_PFN_filtered[f'PFN_{epoch}'].mean() for epoch in epoch_numbers]\n", "y1_std = [data_AUC_PFN_filtered[f'PFN_{epoch}'].std() for epoch in epoch_numbers]\n", "\n", "y_means.append(y1_mean)\n", "y_stds.append(y1_std)\n", "\n", "# 从 data_AUC_xgboost 中提取各 epoch 的均值数据作为第二组 y 轴数据示例\n", "y2_mean = [data_AUC_xgboost_filtered[f'xgboost_{epoch}'].mean() for epoch in epoch_numbers]\n", "y2_std = [data_AUC_xgboost_filtered[f'xgboost_{epoch}'].std() for epoch in epoch_numbers]\n", "\n", "y_means.append(y2_mean)\n", "y_stds.append(y2_std)\n", "\n", "# 绘图\n", "plt.figure(figsize=(24, 12))\n", "\n", "# 自动扩充绘图语句\n", "for i in range(len(y_means)):\n", "    if i == 0:\n", "        plt.errorbar(x*50, y_means[i], yerr=y_stds[i], fmt='o-', label=f'PFN', capsize=5, zorder=len(y_means)-i)\n", "    else:\n", "        plt.errorbar(x*50, y_means[i], yerr=y_stds[i], fmt='o-', label=f'xgboost', capsize=5, zorder=len(y_means)-i)\n", "\n", "plt.xlabel('XAxis')\n", "plt.ylabel('YAxis')\n", "plt.title('Errorbar Plot (num_data_train < 5000)')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a4848c38", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "270da715-33e9-4b4f-a435-bea353d2ef78", "metadata": {}, "outputs": [], "source": ["cmap = plt.get_cmap('inferno')\n", "\n", "# 创建散点图，使用 data_all.num 作为颜色映射的依据\n", "sc = plt.scatter(data_all.AUC_PFN_v1_large, data_all.AUC_xgboost, c=data_all.num_data, cmap=cmap, label='xgboost', s=3, alpha=1, marker='o')\n", "plt.plot([0,1],[0,1], '--', linewidth=0.5)\n", "# 添加颜色条，用于显示颜色和数值的对应关系\n", "plt.colorbar(sc)\n", "plt.xlim(0.49,1)\n", "plt.ylim(0.49,1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "aaf1025a-60a8-40d4-9435-fab4ed69bc9b", "metadata": {}, "outputs": [], "source": ["cmap = plt.get_cmap('inferno')\n", "\n", "# 创建散点图，使用 data_all.num 作为颜色映射的依据\n", "sc = plt.scatter(data_all.AUC_xgboost, data_all.AUC_PFN_v1_large, c=data_all.num_data, cmap=cmap, label='xgboost', s=3, alpha=1, marker='o')\n", "plt.plot([0,1],[0,1], '--', linewidth=0.5)\n", "# 添加颜色条，用于显示颜色和数值的对应关系\n", "plt.colorbar(sc)\n", "plt.xlim(0.49,1)\n", "plt.ylim(0.49,1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b43dab5f-bd55-4d70-a86c-218293e62eb9", "metadata": {}, "outputs": [], "source": ["cmap = plt.get_cmap('inferno')\n", "\n", "# 创建散点图，使用 data_all.num 作为颜色映射的依据\n", "sc = plt.scatter(data_all.AUC_PFN_v1_default, data_all.AUC_PFN_v2_default, c=data_all.num_data, cmap=cmap, label='xgboost', s=3, alpha=1, marker='o')\n", "plt.plot([0,1],[0,1], '--', linewidth=0.5)\n", "# 添加颜色条，用于显示颜色和数值的对应关系\n", "plt.colorbar(sc)\n", "plt.xlim(0.49,1)\n", "plt.ylim(0.49,1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "id": "bbf4eb7f-f6b1-4410-8011-ddfe094626c3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset name</th>\n", "      <th>num_data</th>\n", "      <th>num_feat</th>\n", "      <th>num_class</th>\n", "      <th>AUC_xgboost</th>\n", "      <th>AUC_PFN_v1_default</th>\n", "      <th>AUC_PFN_v1_reproduce</th>\n", "      <th>AUC_PFN_v1_large</th>\n", "      <th>AUC_PFN_v2_default</th>\n", "      <th>num_data_s</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>kr-vs-kp</td>\n", "      <td>3196</td>\n", "      <td>36</td>\n", "      <td>2</td>\n", "      <td>0.999481</td>\n", "      <td>0.999792</td>\n", "      <td>0.999626</td>\n", "      <td>0.999863</td>\n", "      <td>0.999747</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>balance-scale</td>\n", "      <td>625</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>0.906282</td>\n", "      <td>0.999349</td>\n", "      <td>0.998967</td>\n", "      <td>0.999128</td>\n", "      <td>0.998817</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>mfeat-fourier</td>\n", "      <td>2000</td>\n", "      <td>76</td>\n", "      <td>2</td>\n", "      <td>0.975102</td>\n", "      <td>0.976953</td>\n", "      <td>0.976701</td>\n", "      <td>0.979673</td>\n", "      <td>0.989360</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>breast-w</td>\n", "      <td>699</td>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td>0.987993</td>\n", "      <td>0.992055</td>\n", "      <td>0.992127</td>\n", "      <td>0.992594</td>\n", "      <td>0.992630</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>mfeat-ka<PERSON><PERSON><PERSON></td>\n", "      <td>2000</td>\n", "      <td>64</td>\n", "      <td>2</td>\n", "      <td>0.997512</td>\n", "      <td>0.998601</td>\n", "      <td>0.998685</td>\n", "      <td>0.999400</td>\n", "      <td>0.999607</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     dataset name  num_data  num_feat  num_class  AUC_xgboost  \\\n", "0        kr-vs-kp      3196        36          2     0.999481   \n", "1   balance-scale       625         4          2     0.906282   \n", "2   mfeat-fourier      2000        76          2     0.975102   \n", "3        breast-w       699         9          2     0.987993   \n", "4  mfeat-karhunen      2000        64          2     0.997512   \n", "\n", "   AUC_PFN_v1_default  AUC_PFN_v1_reproduce  AUC_PFN_v1_large  \\\n", "0            0.999792              0.999626          0.999863   \n", "1            0.999349              0.998967          0.999128   \n", "2            0.976953              0.976701          0.979673   \n", "3            0.992055              0.992127          0.992594   \n", "4            0.998601              0.998685          0.999400   \n", "\n", "   AUC_PFN_v2_default  num_data_s  \n", "0            0.999747       False  \n", "1            0.998817       False  \n", "2            0.989360       False  \n", "3            0.992630       False  \n", "4            0.999607       False  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data_all.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aa423643-1c7a-4aa2-b722-c075cc8504a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ebbb06fc-6efb-4154-ab53-2cac7b059e9d", "metadata": {}, "source": ["# Kaggle"]}, {"cell_type": "code", "execution_count": 17, "id": "f07bad12-92ec-4eda-926b-a0afe6abee83", "metadata": {}, "outputs": [], "source": ["base_dir = '/home/<USER>/workspace/TabPFN/output_cmp'\n", "data_v1_default = pd.read_csv(f'/{base_dir}/output_classifier_v1_default_kaggle_0225/all_rst.csv').rename(columns={'AUC_PFN':'AUC_PFN_v1_default'})\n", "data_v1_reproduce = pd.read_csv(f'{base_dir}/output_classifier_v1_reproduce_kaggle_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v1_reproduce'})\n", "data_v2_default = pd.read_csv(f'{base_dir}/output_classifier_v2_default_kaggle_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v2_default'})\n", "data_v1_large = pd.read_csv(f'{base_dir}/output_classifier_v1_large_kaggle_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v1_large'})"]}, {"cell_type": "code", "execution_count": 18, "id": "21f2d6bd-b6e7-4d09-bb62-b747303586e4", "metadata": {}, "outputs": [], "source": ["data_all = data_v1_default.merge(data_v1_reproduce).merge(data_v1_large).merge(data_v2_default)\n", "# data_all = data_all[data_all.AUC_PFN_v1_large != -1]\n", "data_all.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 19, "id": "6faba17d-297c-42e9-896b-e64af91b7d2e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(271, 11)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["data_all.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5e4f2c52-9bc3-47c5-a9c5-77ed2012f146", "metadata": {}, "outputs": [], "source": ["data_all"]}, {"cell_type": "code", "execution_count": null, "id": "120f0eb3-f33c-4b40-bb6b-929e2a9a4021", "metadata": {}, "outputs": [], "source": ["plt.hist(data_all['num_data_train'], bins=50)\n", "plt.title('data num')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0e9f1479-61ea-40a7-adf0-b9f1b23a4e58", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_AUC_PFN\n", "auc_cols = df.filter(regex='^AUC').columns\n", "cutoff = 5000\n", "df['num_data_s'] = df['num_data_train']>cutoff\n", "# df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(x='num_data_s', y='AUC', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "plt.xlabel('num_data')\n", "plt.ylabel('AUC')\n", "plt.title(f'num_data>{cutoff}')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "0b42b71e-2c43-493d-8d45-9f71218f74d9", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_all\n", "auc_cols = df.filter(regex='^AUC').columns\n", "# cutoff = 100\n", "# df['num_data_s'] = df['num_data_train']>cutoff\n", "df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "# df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "df_melt = df.melt(value_vars=auc_cols, var_name='epoch', value_name='AUC')\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(24, 6))\n", "# sns.boxplot(x='num_data_s', y='AUC', hue='model', data=df_melt)\n", "sns.boxplot(y='AUC', hue='epoch', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "plt.xlabel('num_data')\n", "plt.ylabel('AUC')\n", "plt.title(f'Domain')\n", "plt.title(f'Domain')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d0bb02f1-c710-4cda-aaf7-f1b707fe98c8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PFN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}