"""
scm_data_provider.py - SCM数据生成和获取模块


主要功能：
1. 数据生成
2. 数据获取：提供原始训练集、原始测试集、原始干预/扰动数据集
3. 配置管理：支持SNR配置、函数配置等各种参数设置
4. 数据处理：提供数据转换、分割等基础功能

核心功能：
- generate_scm_datasets(): 主要的数据生成函数
- get_dataset_splits(): 获取训练/测试/干预数据集分割
- DataProvider: 数据提供器类，统一管理数据生成和获取

支持的配置：
- SNR配置：信噪比设置
- 函数配置：节点函数类型配置
- DAG配置：图结构配置
- 干预配置：干预节点和方法配置
"""

import os
import numpy as np
import pandas as pd
import torch
import multiprocessing
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Any

from scm_data_generator import generate_datasets
from utils_scm import draw_graph


@dataclass
class FunctionConfig:
    """
    单个函数配置类
    
    支持的函数类型:
    - 'linear': 线性函数 y = a1*x1 + a2*x2 + ... + b
    - 'polynomial': 多项式函数
    - 'polynomial_even': 偶次多项式函数
    - 'exponential': 指数函数
    - 'logarithmic': 对数函数
    - 'sigmoid_scaled': 缩放的Sigmoid函数
    - 'tanh_scaled': 缩放的Tanh函数
    - 'sine': 正弦函数
    - 'cosine': 余弦函数
    - 'relu_quadratic': ReLU二次函数
    - 'gaussian_rbf': 高斯径向基函数
    - 'gaussian_process': 高斯过程函数（需要GPy）
    - 'fourier_series': 傅里叶级数函数
    - 'piecewise_linear': 分段线性函数
    - 'random_neural_network': 随机神经网络函数

    噪声配置优先级:
    1. target_snr: 基于信噪比动态计算噪声标准差
    2. noise_std: 固定噪声标准差
    3. 如果都未指定，使用全局SNR配置
    """
    function_type: str = 'linear'  # 默认使用线性函数，最简单且稳定
    hidden_dim: int = 2  # 神经网络隐藏层维度（仅对neural_network类型有效）
    depth: int = 3  # 神经网络深度（仅对neural_network类型有效）
    activation: str = 'tanh'  # 激活函数（仅对neural_network类型有效）
    target_snr: Optional[float] = None  # 目标信噪比
    noise_std: Optional[float] = None  # 固定噪声标准差（与target_snr互斥）
    noise_mean: Optional[float] = None  # 噪声均值配置
    noise_mean_mode: str = 'fixed'  # 噪声均值模式：'fixed' 或 'signal_mean'

    # 函数特定参数（根据function_type使用）
    coefficients: Optional[List[float]] = None  # 线性函数系数（linear类型）
    bias: Optional[float] = None  # 偏置项
    degree: Optional[int] = None  # 多项式次数（polynomial类型）
    scale: Optional[float] = None  # 缩放因子
    frequency: Optional[float] = None  # 频率参数（sine/cosine类型）
    n_components: Optional[int] = None  # 组件数量（fourier_series/piecewise_linear类型）
    centers: Optional[List[List[float]]] = None  # 中心点（gaussian_rbf类型）

    # g函数配置（后非线性变换）
    g_function_type: str = 'identity'  # 简单g函数类型，支持: identity, sigmoid, tanh, softplus, elu+1, scaled_tanh
    g_function_config: Optional[Dict] = None  # 完整g函数配置，支持所有function_generator.py中的函数类型


@dataclass
class NodeFunctionConfigs:
    """节点函数配置类 - 支持不同节点类型使用不同函数"""
    target_config: FunctionConfig = None  # target节点（parent）的函数配置
    target_child_config: FunctionConfig = None  # target_child节点的函数配置
    other_config: FunctionConfig = None  # Other_type节点的函数配置

    def __post_init__(self):
        # 如果没有指定，使用默认配置
        if self.target_config is None:
            self.target_config = FunctionConfig()
        if self.target_child_config is None:
            self.target_child_config = FunctionConfig()
        if self.other_config is None:
            self.other_config = FunctionConfig()


@dataclass
class SNRConfig:
    """SNR配置类"""
    parent_snr_values: List[float] = None
    child_snr_multipliers: List[float] = None
    other_snr_multipliers: List[float] = None

    def __post_init__(self):
        if self.parent_snr_values is None:
            self.parent_snr_values = [2.0]
        if self.child_snr_multipliers is None:
            self.child_snr_multipliers = [1.0]
        if self.other_snr_multipliers is None:
            self.other_snr_multipliers = [1.0]


@dataclass
class DataGenerationConfig:
    """数据生成配置类"""
    n_datasets: int = 2
    intervention_node_type: str = 'all_non_family'
    intervention_value_method: str = 'sample'
    custom_dag_type: str = 'random_SF'
    custom_dag_size: str = 'small'
    avg_in_degree: Tuple[float, float] = (2.2, 2.7)
    device: str = 'cuda'
    
    # SNR和函数配置
    snr_config: SNRConfig = None
    node_function_configs: NodeFunctionConfigs = None

    def __post_init__(self):
        if self.snr_config is None:
            self.snr_config = SNRConfig()
        if self.node_function_configs is None:
            self.node_function_configs = NodeFunctionConfigs()


class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def convert_tensors_to_numpy(*tensors):
        """将tensor转换为numpy数组"""
        return [tensor.clone().cpu().numpy() if torch.is_tensor(tensor) else tensor for tensor in tensors]
    
    @staticmethod
    def split_train_test(x, y, split_ratio=0.7):
        """分割训练测试数据"""
        eval_position = int(x.shape[0] * split_ratio)
        return (x[:eval_position], y[:eval_position], 
                x[eval_position:], y[eval_position:])


class DataProvider:
    """数据提供器类，统一管理数据生成和获取"""

    def __init__(self, h_config: Dict, data_config: DataGenerationConfig):
        self.h_config = h_config
        self.data_config = data_config
        self.datasets_by_config = {}  # 按配置存储数据集
        self.custom_functions_configs = None

    def generate_datasets(self) -> Dict[str, List]:
        """
        生成数据集，支持多个SNR配置

        返回:
            按配置分组的数据集字典，格式为：
            {
                'config_key': [
                    [dataset_name, x_original, y_original, x_intervened, y_intervened, scm, data_info, config_info],
                    ...
                ]
            }
        """
        print(f"开始生成数据集...")

        # 生成自定义函数配置
        self.custom_functions_configs = self._generate_custom_functions_configs()

        print(f"生成了 {len(self.custom_functions_configs)} 个配置:")
        for config_key in self.custom_functions_configs.keys():
            print(f"  - {config_key}")

        # 为每个配置生成数据集
        for config_key, config_info in self.custom_functions_configs.items():
            print(f"\n处理配置: {config_info['name']}")
            custom_functions = config_info['config']

            print("自定义函数配置:")
            for node, node_config in custom_functions.items():
                print(f"  节点 {node}: {node_config}")

            # 生成数据集
            datasets = generate_datasets(
                num_dataset=self.data_config.n_datasets,
                h_config=self.h_config,
                perturbation_type='counterfactual',
                perturbation_node_type=self.data_config.intervention_node_type,
                perturbation_value_method=self.data_config.intervention_value_method,
                custom_dag_type=self.data_config.custom_dag_type,
                custom_functions=custom_functions,
                custom_dag_size=self.data_config.custom_dag_size,
                node_unobserved=False,
                seed=42,
                allow_skip=True,
                avg_in_degree=self.data_config.avg_in_degree
            )

            # 为每个数据集添加配置信息
            enhanced_datasets = []
            for dataset in datasets:
                # 原始返回格式: [dataset_name, x_original, y_original, x_intervened, y_intervened, scm, data_info]
                enhanced_dataset = dataset + [config_info]  # 添加配置信息
                enhanced_datasets.append(enhanced_dataset)

            self.datasets_by_config[config_key] = enhanced_datasets
            print(f"配置 {config_key} 成功生成 {len(enhanced_datasets)} 个数据集")

        print(f"\n总共生成 {sum(len(datasets) for datasets in self.datasets_by_config.values())} 个数据集")
        return self.datasets_by_config
    
    def get_dataset_splits(self, config_key: str = None, dataset_idx: int = 0, train_test_split_ratio: float = 0.7) -> Dict:
        """
        获取指定配置和数据集的训练/测试/干预数据分割

        参数:
            config_key: 配置键，如果为None则使用第一个配置
            dataset_idx: 数据集索引
            train_test_split_ratio: 训练测试分割比例

        返回:
            包含各种数据分割的字典
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        # 如果未指定配置，使用第一个配置
        if config_key is None:
            config_key = list(self.datasets_by_config.keys())[0]

        if config_key not in self.datasets_by_config:
            raise ValueError(f"配置 {config_key} 不存在，可用配置: {list(self.datasets_by_config.keys())}")

        datasets = self.datasets_by_config[config_key]
        if dataset_idx >= len(datasets):
            raise ValueError(f"数据集索引 {dataset_idx} 超出范围，配置 {config_key} 共有 {len(datasets)} 个数据集")

        dataset = datasets[dataset_idx]
        # 新格式: [dataset_name, x_original, y_original, x_intervened, y_intervened, scm, data_info, config_info]
        dataset_name = dataset[0]
        xs_original, ys_original = DataProcessor.convert_tensors_to_numpy(dataset[1], dataset[2])
        xs_intervention, ys_intervention = DataProcessor.convert_tensors_to_numpy(dataset[3], dataset[4])
        scm = dataset[5]
        data_info = dataset[6]
        config_info = dataset[7]

        # 分割训练测试数据
        train_xs, train_ys, test_xs_original, test_ys_original = DataProcessor.split_train_test(
            xs_original, ys_original, train_test_split_ratio
        )
        _, _, test_xs_intervention, test_ys_intervention = DataProcessor.split_train_test(
            xs_intervention, ys_intervention, train_test_split_ratio
        )

        # 获取特征名
        feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(xs_original.shape[1])]
        
        return {
            'dataset_name': dataset_name,
            'dataset_idx': dataset_idx,
            'config_key': config_key,
            'scm': scm,
            'feature_names': feature_names,
            'data_info': data_info,
            'config_info': config_info,

            # 原始完整数据
            'xs_original_full': xs_original,
            'ys_original_full': ys_original,
            'xs_intervention_full': xs_intervention,
            'ys_intervention_full': ys_intervention,

            # 训练数据
            'train_xs': train_xs,
            'train_ys': train_ys,

            # 原始测试数据
            'test_xs_original': test_xs_original,
            'test_ys_original': test_ys_original,

            # 干预测试数据
            'test_xs_intervention': test_xs_intervention,
            'test_ys_intervention': test_ys_intervention,

            # 数据形状信息
            'data_shapes': {
                'n_samples_original': xs_original.shape[0],
                'n_samples_intervention': xs_intervention.shape[0],
                'n_features': xs_original.shape[1],
                'train_samples': train_xs.shape[0],
                'test_samples': test_xs_original.shape[0]
            }
        }

    def get_all_datasets_info(self) -> List[Dict]:
        """
        获取所有数据集的基本信息

        返回:
            包含所有数据集信息的列表
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        datasets_info = []
        for config_key, datasets in self.datasets_by_config.items():
            for idx, dataset in enumerate(datasets):
                dataset_name = dataset[0]
                xs_original, ys_original = DataProcessor.convert_tensors_to_numpy(dataset[1], dataset[2])
                xs_intervention, ys_intervention = DataProcessor.convert_tensors_to_numpy(dataset[3], dataset[4])
                scm = dataset[5]
                data_info = dataset[6]
                config_info = dataset[7]

                # 获取特征名
                feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(xs_original.shape[1])]

                # 提取SNR信息
                snr_mean = self._extract_snr_mean(scm)

                info = {
                    'config_key': config_key,
                    'config_name': config_info['name'],
                    'dataset_idx': idx,
                    'dataset_name': dataset_name,
                    'n_samples_original': xs_original.shape[0],
                    'n_samples_intervention': xs_intervention.shape[0],
                    'n_features': xs_original.shape[1],
                    'feature_names': feature_names,
                    'snr_mean': snr_mean,
                    'dag_nodes': len(scm.dag.nodes()) if hasattr(scm, 'dag') else None,
                    'dag_edges': len(scm.dag.edges()) if hasattr(scm, 'dag') else None,
                    'data_info': data_info,
                    'config_info': config_info
                }
                datasets_info.append(info)

        return datasets_info

    def save_dataset_to_csv(self, config_key: str = None, dataset_idx: int = 0, output_dir: str = 'data_output'):
        """
        将指定数据集保存为CSV文件

        参数:
            config_key: 配置键，如果为None则使用第一个配置
            dataset_idx: 数据集索引
            output_dir: 输出目录
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 获取数据分割
        data_splits = self.get_dataset_splits(config_key, dataset_idx)
        dataset_name = data_splits['dataset_name']
        config_name = data_splits['config_info']['name']

        # 保存训练数据
        train_data = np.column_stack([data_splits['train_xs'], data_splits['train_ys']])
        train_columns = data_splits['feature_names'] + ['target']
        train_df = pd.DataFrame(train_data, columns=train_columns)
        train_path = os.path.join(output_dir, f'{dataset_name}_train.csv')
        train_df.to_csv(train_path, index=False)

        # 保存原始测试数据
        test_original_data = np.column_stack([data_splits['test_xs_original'], data_splits['test_ys_original']])
        test_original_df = pd.DataFrame(test_original_data, columns=train_columns)
        test_original_path = os.path.join(output_dir, f'{dataset_name}_test_original.csv')
        test_original_df.to_csv(test_original_path, index=False)

        # 保存干预测试数据
        test_intervention_data = np.column_stack([data_splits['test_xs_intervention'], data_splits['test_ys_intervention']])
        test_intervention_df = pd.DataFrame(test_intervention_data, columns=train_columns)
        test_intervention_path = os.path.join(output_dir, f'{dataset_name}_test_intervention.csv')
        test_intervention_df.to_csv(test_intervention_path, index=False)

        print(f"数据集 {dataset_name} 已保存到:")
        print(f"  训练数据: {train_path}")
        print(f"  原始测试数据: {test_original_path}")
        print(f"  干预测试数据: {test_intervention_path}")

        return {
            'train_path': train_path,
            'test_original_path': test_original_path,
            'test_intervention_path': test_intervention_path
        }

    def draw_dag(self, config_key: str = None, dataset_idx: int = 0, output_dir: str = 'dag_output'):
        """
        绘制指定数据集的DAG图

        参数:
            config_key: 配置键，如果为None则使用第一个配置
            dataset_idx: 数据集索引
            output_dir: 输出目录
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        # 如果未指定配置，使用第一个配置
        if config_key is None:
            config_key = list(self.datasets_by_config.keys())[0]

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        datasets = self.datasets_by_config[config_key]
        dataset = datasets[dataset_idx]
        dataset_name = dataset[0]
        scm = dataset[5]

        # 绘制DAG图
        dag_filename = f'dag_{dataset_name}.png'
        dag_path = os.path.join(output_dir, dag_filename)

        try:
            intervention_or_perturb_nodes = getattr(scm, 'intervention_nodes', None)
            if intervention_or_perturb_nodes is None:
                intervention_or_perturb_nodes = getattr(scm, 'perturbation_nodes', None)

            draw_graph(
                scm.dag,
                dag_path,
                target_node=getattr(scm, 'selected_target', None),
                intervention_nodes=intervention_or_perturb_nodes,
                unobserved_nodes=getattr(scm, 'unobserved_nodes', None),
                selected_features=getattr(scm, 'selected_features', None),
                assignment=getattr(scm, 'assignment', None),
                scm=scm,
                model_results=None
            )
            print(f"DAG图已保存到: {dag_path}")
            return dag_path
        except Exception as e:
            print(f"绘制DAG图时出错: {str(e)}")
            return None

    def _extract_snr_mean(self, scm) -> float:
        """提取SNR均值"""
        snr_mean = np.nan
        if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
            actual_snr = scm.snr_validation_results.get('actual_snr', {})
            if actual_snr:
                valid_snr_values = [v for v in actual_snr.values() if v is not None and not np.isnan(v)]
                if valid_snr_values:
                    snr_mean = np.mean(valid_snr_values)
        return snr_mean

    def _generate_custom_functions_configs(self) -> Dict[str, Dict]:
        """生成自定义函数配置"""
        custom_functions_configs = {}

        # 使用配置类生成SNR配置
        for i in self.data_config.snr_config.parent_snr_values:
            for j_mult in self.data_config.snr_config.child_snr_multipliers:
                for k_mult in self.data_config.snr_config.other_snr_multipliers:
                    j = i * j_mult
                    k = max(i, j) * k_mult

                    key = f'parent_snr_{i}_child_snr_{j}_other_snr_{k}'
                    parent_snr = float(i)
                    child_snr = float(j)
                    other_snr = float(k)

                    # 为每个节点类型生成不同的函数配置
                    def create_node_config(node_config, snr_value):
                        """创建单个节点的配置字典"""
                        config = {
                            'type': node_config.function_type
                        }

                        # 神经网络相关参数（仅对neural_network类型有效）
                        if node_config.function_type == 'random_neural_network':
                            config['hidden_dim'] = node_config.hidden_dim
                            config['depth'] = node_config.depth
                            config['activation'] = node_config.activation

                        # 函数特定参数
                        if node_config.coefficients is not None:
                            config['coefficients'] = node_config.coefficients
                        if node_config.bias is not None:
                            config['bias'] = node_config.bias
                        if node_config.degree is not None:
                            config['degree'] = node_config.degree
                        if node_config.scale is not None:
                            config['scale'] = node_config.scale
                        if node_config.frequency is not None:
                            config['frequency'] = node_config.frequency
                        if node_config.n_components is not None:
                            config['n_components'] = node_config.n_components
                        if node_config.centers is not None:
                            config['centers'] = node_config.centers

                        # SNR和noise_std互斥配置
                        if node_config.target_snr is not None:
                            # 如果节点配置中指定了target_snr，使用节点配置的值
                            config['target_snr'] = node_config.target_snr
                        elif node_config.noise_std is not None:
                            # 如果节点配置中指定了noise_std，使用固定噪声标准差
                            config['noise_std'] = node_config.noise_std
                        else:
                            # 否则使用SNR配置生成的值
                            config['target_snr'] = snr_value

                        # 噪声均值配置
                        if node_config.noise_mean is not None:
                            config['noise_mean'] = node_config.noise_mean

                        # 噪声均值模式配置
                        if node_config.noise_mean_mode != 'fixed':
                            config['noise_mean_mode'] = node_config.noise_mean_mode

                        # g函数配置
                        if node_config.g_function_config is not None:
                            # 如果有完整的g函数配置，使用完整配置
                            config['g_function_config'] = node_config.g_function_config
                        elif node_config.g_function_type != 'identity':
                            # 否则使用简单的g函数类型
                            config['g_function_type'] = node_config.g_function_type

                        return config

                    target_function_config = create_node_config(self.data_config.node_function_configs.target_config, parent_snr)
                    target_child_function_config = create_node_config(self.data_config.node_function_configs.target_child_config, child_snr)
                    other_function_config = create_node_config(self.data_config.node_function_configs.other_config, other_snr)

                    custom_functions_configs[key] = {
                        'name': key,
                        'config': {
                            'target': target_function_config,
                            'target_child': target_child_function_config,
                            'Other_type': other_function_config
                        }
                    }

        return custom_functions_configs


def generate_scm_datasets(h_config: Dict = None, data_config: DataGenerationConfig = None) -> DataProvider:
    """
    便捷函数：生成SCM数据集

    参数:
        h_config: 基础配置字典，如果为None则使用默认配置
        data_config: 数据生成配置，如果为None则使用默认配置

    返回:
        DataProvider实例，已生成数据集
    """
    # 使用默认配置（如果未提供）
    if h_config is None:
        h_config = get_default_h_config()

    if data_config is None:
        data_config = DataGenerationConfig()

    # 创建数据提供器并生成数据集
    provider = DataProvider(h_config, data_config)
    provider.generate_datasets()

    return provider


def get_default_h_config() -> Dict:
    """获取默认的h_config配置"""
    return {
        'device': 'cpu',
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'sample_std': False,
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'train_test_split_ratio': 0.7,
        'task': 'regression',
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'use_monte_carlo_precompute': False
    }


def main():
    """主函数：演示如何使用数据提供器"""
    print("SCM数据生成和获取演示")
    print("=" * 50)

    # 配置SNR
    snr_config = SNRConfig(
        parent_snr_values=[2.0, 4.0],  # 父节点SNR值
        child_snr_multipliers=[0.1, 1.0],  # 子节点SNR倍数
        other_snr_multipliers=[0.1, 1.0]   # 其他节点SNR倍数
    )

    # 配置节点函数
    node_function_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        ),
        target_child_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        ),
        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        )
    )

    # 配置数据生成参数
    data_config = DataGenerationConfig(
        n_datasets=2,
        intervention_node_type='all_non_family',
        intervention_value_method='sample',
        custom_dag_type='random_SF',
        custom_dag_size='small',
        avg_in_degree=(1.2, 1.7),
        device='cuda' if torch.cuda.is_available() else 'cpu',
        snr_config=snr_config,
        node_function_configs=node_function_configs
    )

    # 生成数据集
    print("1. 生成数据集...")
    provider = generate_scm_datasets(data_config=data_config)

    # 获取所有数据集信息
    print("\n2. 获取数据集信息...")
    datasets_info = provider.get_all_datasets_info()
    for info in datasets_info:
        print(f"  配置 {info['config_key']}: 数据集 {info['dataset_idx']} ({info['dataset_name']})")
        print(f"    样本数: 原始={info['n_samples_original']}, 干预={info['n_samples_intervention']}")
        print(f"    特征数: {info['n_features']}")
        print(f"    SNR均值: {info['snr_mean']:.3f}" if not np.isnan(info['snr_mean']) else "    SNR均值: N/A")
        print(f"    DAG: {info['dag_nodes']} 节点, {info['dag_edges']} 边")

    # 获取第一个配置的第一个数据集的分割
    print("\n3. 获取数据集分割...")
    first_config_key = list(provider.datasets_by_config.keys())[0]
    data_splits = provider.get_dataset_splits(config_key=first_config_key, dataset_idx=0)
    print(f"  配置: {data_splits['config_key']}")
    print(f"  数据集: {data_splits['dataset_name']}")
    print(f"  特征名: {data_splits['feature_names']}")
    print(f"  训练集: {data_splits['train_xs'].shape}")
    print(f"  原始测试集: {data_splits['test_xs_original'].shape}")
    print(f"  干预测试集: {data_splits['test_xs_intervention'].shape}")

    # 保存数据到CSV
    print("\n4. 保存数据到CSV...")
    provider.save_dataset_to_csv(config_key=first_config_key, dataset_idx=0)

    # 绘制DAG图
    print("\n5. 绘制DAG图...")
    provider.draw_dag(config_key=first_config_key, dataset_idx=0)

    print("\n演示完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
