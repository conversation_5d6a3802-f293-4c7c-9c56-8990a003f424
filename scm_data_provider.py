"""
scm_data_provider.py - SCM数据生成和获取模块


主要功能：
1. 数据生成
2. 数据获取：提供原始训练集、原始测试集、原始干预/扰动数据集
3. 配置管理：支持SNR配置、函数配置等各种参数设置
4. 数据处理：提供数据转换、分割等基础功能

核心功能：
- DataProvider: 数据提供器类，统一管理数据生成和获取
- get_dataset_splits(): 获取训练/测试/干预数据集分割
- generate_datasets(): 生成多个SNR配置的数据集

支持的配置：
- SNR配置：信噪比设置
- 函数配置：节点函数类型配置
- DAG配置：图结构配置
- 干预配置：干预节点和方法配置
"""

import os
import numpy as np
import pandas as pd
import torch
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional

from scm_data_generator import generate_datasets
from utils_scm import draw_graph


@dataclass
class FunctionConfig:
    """
    单个函数配置类
    
    支持的函数类型:
    - 'linear': 线性函数 y = a1*x1 + a2*x2 + ... + b
    - 'polynomial': 多项式函数
    - 'polynomial_even': 偶次多项式函数
    - 'exponential': 指数函数
    - 'logarithmic': 对数函数
    - 'sigmoid_scaled': 缩放的Sigmoid函数
    - 'tanh_scaled': 缩放的Tanh函数
    - 'sine': 正弦函数
    - 'cosine': 余弦函数
    - 'relu_quadratic': ReLU二次函数
    - 'gaussian_rbf': 高斯径向基函数
    - 'gaussian_process': 高斯过程函数（需要GPy）
    - 'fourier_series': 傅里叶级数函数
    - 'piecewise_linear': 分段线性函数
    - 'random_neural_network': 随机神经网络函数

    噪声配置优先级:
    1. target_snr: 基于信噪比动态计算噪声标准差
    2. noise_std: 固定噪声标准差
    3. 如果都未指定，使用全局SNR配置
    """
    function_type: str = 'linear'  # 默认使用线性函数，最简单且稳定
    hidden_dim: int = 2  # 神经网络隐藏层维度（仅对neural_network类型有效）
    depth: int = 3  # 神经网络深度（仅对neural_network类型有效）
    activation: str = 'tanh'  # 激活函数（仅对neural_network类型有效）
    target_snr: Optional[float] = None  # 目标信噪比
    noise_std: Optional[float] = None  # 固定噪声标准差（与target_snr互斥）
    noise_mean: Optional[float] = None  # 噪声均值配置
    noise_mean_mode: str = 'fixed'  # 噪声均值模式：'fixed' 或 'signal_mean'

    # 函数特定参数（根据function_type使用）
    coefficients: Optional[List[float]] = None  # 线性函数系数（linear类型）
    bias: Optional[float] = None  # 偏置项
    degree: Optional[int] = None  # 多项式次数（polynomial类型）
    scale: Optional[float] = None  # 缩放因子
    frequency: Optional[float] = None  # 频率参数（sine/cosine类型）
    n_components: Optional[int] = None  # 组件数量（fourier_series/piecewise_linear类型）
    centers: Optional[List[List[float]]] = None  # 中心点（gaussian_rbf类型）

    # g函数配置（后非线性变换）
    g_function_type: str = 'identity'  # 简单g函数类型，支持: identity, sigmoid, tanh, softplus, elu+1, scaled_tanh
    g_function_config: Optional[Dict] = None  # 完整g函数配置，支持所有function_generator.py中的函数类型


@dataclass
class NodeFunctionConfigs:
    """节点函数配置类 - 支持不同节点类型使用不同函数"""
    target_config: FunctionConfig = None  # target节点（parent）的函数配置
    target_child_config: FunctionConfig = None  # target_child节点的函数配置
    other_config: FunctionConfig = None  # Other_type节点的函数配置

    def __post_init__(self):
        # 如果没有指定，使用默认配置
        if self.target_config is None:
            self.target_config = FunctionConfig()
        if self.target_child_config is None:
            self.target_child_config = FunctionConfig()
        if self.other_config is None:
            self.other_config = FunctionConfig()


@dataclass
class SNRConfig:
    """SNR配置类"""
    parent_snr_values: List[float] = None
    child_snr_multipliers: List[float] = None
    other_snr_multipliers: List[float] = None

    def __post_init__(self):
        if self.parent_snr_values is None:
            self.parent_snr_values = [2.0]
        if self.child_snr_multipliers is None:
            self.child_snr_multipliers = [1.0]
        if self.other_snr_multipliers is None:
            self.other_snr_multipliers = [1.0]


@dataclass
class DataGenerationConfig:
    """数据生成配置类"""
    n_datasets: int = 2

    # 数据集类型配置
    dataset_type: str = 'perturbation'  # 'standard', 'intervention', 'perturbation'
    intervention_node_type: str = 'all_non_family'
    intervention_value_method: str = 'sample'
    perturbation_node_type: str = 'all_non_family'  # 扰动节点类型
    perturbation_value_method: str = 'sample'  # 扰动值方法

    # DAG配置
    custom_dag_type: str = 'random_SF'
    custom_dag_size: str = 'small'
    avg_in_degree: Tuple[float, float] = (2.2, 2.7)
    device: str = 'cuda'

    # SNR和函数配置
    snr_config: SNRConfig = None
    node_function_configs: NodeFunctionConfigs = None

    def __post_init__(self):
        if self.snr_config is None:
            self.snr_config = SNRConfig()
        if self.node_function_configs is None:
            self.node_function_configs = NodeFunctionConfigs()


class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def convert_tensors_to_numpy(*tensors):
        """将tensor转换为numpy数组"""
        return [tensor.clone().cpu().numpy() if torch.is_tensor(tensor) else tensor for tensor in tensors]
    
    @staticmethod
    def split_train_test(x, y, split_ratio=0.7):
        """分割训练测试数据"""
        eval_position = int(x.shape[0] * split_ratio)
        return (x[:eval_position], y[:eval_position], 
                x[eval_position:], y[eval_position:])


class DataProvider:
    """数据提供器类，统一管理数据生成和获取"""

    def __init__(self, h_config: Dict, data_config: DataGenerationConfig):
        self.h_config = h_config
        self.data_config = data_config
        self.datasets_by_config = {}  # 按配置存储数据集
        self.custom_functions_configs = None

    def generate_datasets(self) -> Dict[str, List]:
        """
        生成数据集，支持多个SNR配置

        返回:
            按配置分组的数据集字典，格式为：
            {
                'config_key': [
                    [dataset_name, x_original, y_original, x_intervened, y_intervened, scm, data_info, config_info],
                    ...
                ]
            }
        """
        print(f"开始生成数据集...")

        # 生成自定义函数配置
        self.custom_functions_configs = self._generate_custom_functions_configs()

        print(f"生成了 {len(self.custom_functions_configs)} 个配置:")
        for config_key in self.custom_functions_configs.keys():
            print(f"  - {config_key}")

        # 为每个配置生成数据集
        for config_key, config_info in self.custom_functions_configs.items():
            print(f"\n处理配置: {config_info['name']}")
            custom_functions = config_info['config']

            print("自定义函数配置:")
            for node, node_config in custom_functions.items():
                print(f"  节点 {node}: {node_config}")

            # 根据配置生成不同类型的数据集
            datasets = self._generate_datasets_by_type(custom_functions)

            # 为每个数据集添加配置信息
            enhanced_datasets = []
            for dataset in datasets:
                # 解析数据集并添加配置信息
                parsed_dataset = self._parse_dataset_by_type(dataset)
                parsed_dataset['config_info'] = config_info
                enhanced_datasets.append(parsed_dataset)

            self.datasets_by_config[config_key] = enhanced_datasets
            print(f"配置 {config_key} 成功生成 {len(enhanced_datasets)} 个数据集")

        print(f"\n总共生成 {sum(len(datasets) for datasets in self.datasets_by_config.values())} 个数据集")
        return self.datasets_by_config

    def _generate_datasets_by_type(self, custom_functions: Dict) -> List:
        """根据配置类型生成数据集"""
        if self.data_config.dataset_type == 'standard':
            # 生成标准数据集
            return generate_datasets(
                num_dataset=self.data_config.n_datasets,
                h_config=self.h_config,
                custom_dag_type=self.data_config.custom_dag_type,
                custom_functions=custom_functions,
                custom_dag_size=self.data_config.custom_dag_size,
                node_unobserved=False,
                seed=42,
                allow_skip=True,
                avg_in_degree=self.data_config.avg_in_degree
            )
        elif self.data_config.dataset_type == 'intervention':
            # 生成干预数据集
            return generate_datasets(
                num_dataset=self.data_config.n_datasets,
                h_config=self.h_config,
                intervention_type='counterfactual',
                intervention_node_type=self.data_config.intervention_node_type,
                intervention_value_method=self.data_config.intervention_value_method,
                custom_dag_type=self.data_config.custom_dag_type,
                custom_functions=custom_functions,
                custom_dag_size=self.data_config.custom_dag_size,
                node_unobserved=False,
                seed=42,
                allow_skip=True,
                avg_in_degree=self.data_config.avg_in_degree
            )
        elif self.data_config.dataset_type == 'perturbation':
            # 生成扰动数据集
            return generate_datasets(
                num_dataset=self.data_config.n_datasets,
                h_config=self.h_config,
                perturbation_type='counterfactual',
                perturbation_node_type=self.data_config.perturbation_node_type,
                perturbation_value_method=self.data_config.perturbation_value_method,
                custom_dag_type=self.data_config.custom_dag_type,
                custom_functions=custom_functions,
                custom_dag_size=self.data_config.custom_dag_size,
                node_unobserved=False,
                seed=42,
                allow_skip=True,
                avg_in_degree=self.data_config.avg_in_degree
            )
        else:
            raise ValueError(f"不支持的数据集类型: {self.data_config.dataset_type}")

    def _parse_dataset_by_type(self, dataset: List) -> Dict:
        """根据数据集类型解析数据集格式"""
        if self.data_config.dataset_type == 'standard':
            # 标准数据集格式: [dataset_name, x, y, scm, data_info]
            return {
                'dataset_name': dataset[0],
                'x': dataset[1],
                'y': dataset[2],
                'scm': dataset[3],
                'data_info': dataset[4],
                'dataset_type': 'standard'
            }
        elif self.data_config.dataset_type in ['intervention', 'perturbation']:
            # 干预/扰动数据集格式: [dataset_name, x_original, y_original, x_modified, y_modified, scm, data_info]
            return {
                'dataset_name': dataset[0],
                'x_original': dataset[1],
                'y_original': dataset[2],
                'x_modified': dataset[3],  # 干预或扰动后的数据
                'y_modified': dataset[4],
                'scm': dataset[5],
                'data_info': dataset[6],
                'dataset_type': self.data_config.dataset_type
            }
        else:
            raise ValueError(f"不支持的数据集类型: {self.data_config.dataset_type}")
    
    def get_dataset_splits(self, config_key: str = None, dataset_idx: int = 0, train_test_split_ratio: float = 0.7) -> Dict:
        """
        获取指定配置和数据集的训练/测试/干预数据分割

        参数:
            config_key: 配置键，如果为None则使用第一个配置
            dataset_idx: 数据集索引
            train_test_split_ratio: 训练测试分割比例

        返回:
            包含各种数据分割的字典
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        # 如果未指定配置，使用第一个配置
        if config_key is None:
            config_key = list(self.datasets_by_config.keys())[0]

        if config_key not in self.datasets_by_config:
            raise ValueError(f"配置 {config_key} 不存在，可用配置: {list(self.datasets_by_config.keys())}")

        datasets = self.datasets_by_config[config_key]
        if dataset_idx >= len(datasets):
            raise ValueError(f"数据集索引 {dataset_idx} 超出范围，配置 {config_key} 共有 {len(datasets)} 个数据集")

        # 获取解析后的数据集
        parsed_dataset = datasets[dataset_idx]
        dataset_type = parsed_dataset['dataset_type']
        scm = parsed_dataset['scm']

        # 根据数据集类型处理数据
        if dataset_type == 'standard':
            # 标准数据集只有一组数据
            xs_data, ys_data = DataProcessor.convert_tensors_to_numpy(
                parsed_dataset['x'], parsed_dataset['y']
            )
            # 分割训练测试数据
            train_xs, train_ys, test_xs, test_ys = DataProcessor.split_train_test(
                xs_data, ys_data, train_test_split_ratio
            )

            result_data = {
                'train_xs': train_xs,
                'train_ys': train_ys,
                'test_xs': test_xs,
                'test_ys': test_ys,
                'xs_full': xs_data,
                'ys_full': ys_data
            }
        else:
            # 干预/扰动数据集有两组数据
            xs_original, ys_original = DataProcessor.convert_tensors_to_numpy(
                parsed_dataset['x_original'], parsed_dataset['y_original']
            )
            xs_modified, ys_modified = DataProcessor.convert_tensors_to_numpy(
                parsed_dataset['x_modified'], parsed_dataset['y_modified']
            )

            # 分割训练测试数据
            train_xs, train_ys, test_xs_original, test_ys_original = DataProcessor.split_train_test(
                xs_original, ys_original, train_test_split_ratio
            )
            _, _, test_xs_modified, test_ys_modified = DataProcessor.split_train_test(
                xs_modified, ys_modified, train_test_split_ratio
            )

            result_data = {
                'train_xs': train_xs,
                'train_ys': train_ys,
                'test_xs_original': test_xs_original,
                'test_ys_original': test_ys_original,
                'test_xs_modified': test_xs_modified,
                'test_ys_modified': test_ys_modified,
                'xs_original_full': xs_original,
                'ys_original_full': ys_original,
                'xs_modified_full': xs_modified,
                'ys_modified_full': ys_modified
            }

        # 获取特征名
        sample_x = xs_original if dataset_type != 'standard' else xs_data
        feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(sample_x.shape[1])]
        
        # 构建返回结果
        result = {
            'dataset_name': parsed_dataset['dataset_name'],
            'dataset_idx': dataset_idx,
            'config_key': config_key,
            'dataset_type': dataset_type,
            'scm': scm,
            'feature_names': feature_names,
            'data_info': parsed_dataset['data_info'],
            'config_info': parsed_dataset['config_info']
        }

        # 添加数据分割结果
        result.update(result_data)

        # 添加数据形状信息
        if dataset_type == 'standard':
            result['data_shapes'] = {
                'n_samples': sample_x.shape[0],
                'n_features': sample_x.shape[1],
                'train_samples': train_xs.shape[0],
                'test_samples': test_xs.shape[0]
            }
        else:
            result['data_shapes'] = {
                'n_samples_original': xs_original.shape[0],
                'n_samples_modified': xs_modified.shape[0],
                'n_features': sample_x.shape[1],
                'train_samples': train_xs.shape[0],
                'test_samples_original': test_xs_original.shape[0],
                'test_samples_modified': test_xs_modified.shape[0]
            }

        return result

    def get_all_dataset_splits(self, train_test_split_ratio: float = 0.7) -> Dict[str, List[Dict]]:
        """
        获取所有配置下所有数据集的训练/测试/干预数据分割

        参数:
            train_test_split_ratio: 训练测试分割比例

        返回:
            按配置分组的数据集分割字典，格式为：
            {
                'config_key': [
                    {dataset_splits_for_dataset_0},
                    {dataset_splits_for_dataset_1},
                    ...
                ]
            }
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        all_splits = {}
        for config_key, datasets in self.datasets_by_config.items():
            config_splits = []
            for dataset_idx in range(len(datasets)):
                splits = self.get_dataset_splits(config_key, dataset_idx, train_test_split_ratio)
                config_splits.append(splits)
            all_splits[config_key] = config_splits

        return all_splits

    def get_all_datasets_info(self) -> List[Dict]:
        """
        获取所有数据集的基本信息

        返回:
            包含所有数据集信息的列表
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        datasets_info = []
        for config_key, datasets in self.datasets_by_config.items():
            for idx, parsed_dataset in enumerate(datasets):
                dataset_type = parsed_dataset['dataset_type']
                scm = parsed_dataset['scm']

                # 根据数据集类型获取样本数据
                if dataset_type == 'standard':
                    sample_x, _ = DataProcessor.convert_tensors_to_numpy(
                        parsed_dataset['x'], parsed_dataset['y']
                    )
                    n_samples = sample_x.shape[0]
                    n_samples_modified = None
                else:
                    sample_x, _ = DataProcessor.convert_tensors_to_numpy(
                        parsed_dataset['x_original'], parsed_dataset['y_original']
                    )
                    xs_modified, _ = DataProcessor.convert_tensors_to_numpy(
                        parsed_dataset['x_modified'], parsed_dataset['y_modified']
                    )
                    n_samples = sample_x.shape[0]
                    n_samples_modified = xs_modified.shape[0]

                # 获取特征名
                feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(sample_x.shape[1])]

                # 提取SNR信息
                snr_mean = self._extract_snr_mean(scm)

                info = {
                    'config_key': config_key,
                    'config_name': parsed_dataset['config_info']['name'],
                    'dataset_idx': idx,
                    'dataset_name': parsed_dataset['dataset_name'],
                    'dataset_type': dataset_type,
                    'n_samples': n_samples,
                    'n_samples_modified': n_samples_modified,
                    'n_features': sample_x.shape[1],
                    'feature_names': feature_names,
                    'snr_mean': snr_mean,
                    'dag_nodes': len(scm.dag.nodes()) if hasattr(scm, 'dag') else None,
                    'dag_edges': len(scm.dag.edges()) if hasattr(scm, 'dag') else None,
                    'data_info': parsed_dataset['data_info'],
                    'config_info': parsed_dataset['config_info']
                }
                datasets_info.append(info)

        return datasets_info

    def save_dataset_to_csv(self, config_key: str = None, dataset_idx: int = 0, output_dir: str = 'data_output'):
        """
        将指定数据集保存为CSV文件

        参数:
            config_key: 配置键，如果为None则使用第一个配置
            dataset_idx: 数据集索引
            output_dir: 输出目录
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 获取数据分割
        data_splits = self.get_dataset_splits(config_key, dataset_idx)
        dataset_name = data_splits['dataset_name']

        # 保存训练数据
        train_data = np.column_stack([data_splits['train_xs'], data_splits['train_ys']])
        train_columns = data_splits['feature_names'] + ['target']
        train_df = pd.DataFrame(train_data, columns=train_columns)
        train_path = os.path.join(output_dir, f'{dataset_name}_train.csv')
        train_df.to_csv(train_path, index=False)

        saved_paths = {'train_path': train_path}

        # 根据数据集类型保存测试数据
        if data_splits['dataset_type'] == 'standard':
            # 标准数据集只有一组测试数据
            test_data = np.column_stack([data_splits['test_xs'], data_splits['test_ys']])
            test_df = pd.DataFrame(test_data, columns=train_columns)
            test_path = os.path.join(output_dir, f'{dataset_name}_test.csv')
            test_df.to_csv(test_path, index=False)
            saved_paths['test_path'] = test_path

            print(f"数据集 {dataset_name} (标准数据集) 已保存到:")
            print(f"  训练数据: {train_path}")
            print(f"  测试数据: {test_path}")
        else:
            # 干预/扰动数据集有两组测试数据
            test_original_data = np.column_stack([data_splits['test_xs_original'], data_splits['test_ys_original']])
            test_original_df = pd.DataFrame(test_original_data, columns=train_columns)
            test_original_path = os.path.join(output_dir, f'{dataset_name}_test_original.csv')
            test_original_df.to_csv(test_original_path, index=False)

            test_modified_data = np.column_stack([data_splits['test_xs_modified'], data_splits['test_ys_modified']])
            test_modified_df = pd.DataFrame(test_modified_data, columns=train_columns)
            test_modified_path = os.path.join(output_dir, f'{dataset_name}_test_{data_splits["dataset_type"]}.csv')
            test_modified_df.to_csv(test_modified_path, index=False)

            saved_paths.update({
                'test_original_path': test_original_path,
                'test_modified_path': test_modified_path
            })

            print(f"数据集 {dataset_name} ({data_splits['dataset_type']}数据集) 已保存到:")
            print(f"  训练数据: {train_path}")
            print(f"  原始测试数据: {test_original_path}")
            print(f"  {data_splits['dataset_type']}测试数据: {test_modified_path}")

        return saved_paths

    def draw_dag(self, config_key: str = None, dataset_idx: int = 0, output_dir: str = 'dag_output'):
        """
        绘制指定数据集的DAG图

        参数:
            config_key: 配置键，如果为None则使用第一个配置
            dataset_idx: 数据集索引
            output_dir: 输出目录
        """
        if not self.datasets_by_config:
            raise ValueError("请先调用 generate_datasets() 生成数据集")

        # 如果未指定配置，使用第一个配置
        if config_key is None:
            config_key = list(self.datasets_by_config.keys())[0]

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        datasets = self.datasets_by_config[config_key]
        parsed_dataset = datasets[dataset_idx]
        dataset_name = parsed_dataset['dataset_name']
        scm = parsed_dataset['scm']

        # 绘制DAG图
        dag_filename = f'dag_{dataset_name}.png'
        dag_path = os.path.join(output_dir, dag_filename)

        try:
            intervention_or_perturb_nodes = getattr(scm, 'intervention_nodes', None)
            if intervention_or_perturb_nodes is None:
                intervention_or_perturb_nodes = getattr(scm, 'perturbation_nodes', None)

            draw_graph(
                scm.dag,
                dag_path,
                target_node=getattr(scm, 'selected_target', None),
                intervention_nodes=intervention_or_perturb_nodes,
                unobserved_nodes=getattr(scm, 'unobserved_nodes', None),
                selected_features=getattr(scm, 'selected_features', None),
                assignment=getattr(scm, 'assignment', None),
                scm=scm,
                model_results=None
            )
            print(f"DAG图已保存到: {dag_path}")
            return dag_path
        except Exception as e:
            print(f"绘制DAG图时出错: {str(e)}")
            return None

    def _extract_snr_mean(self, scm) -> float:
        """提取SNR均值"""
        snr_mean = np.nan
        if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
            actual_snr = scm.snr_validation_results.get('actual_snr', {})
            if actual_snr:
                valid_snr_values = [v for v in actual_snr.values() if v is not None and not np.isnan(v)]
                if valid_snr_values:
                    snr_mean = np.mean(valid_snr_values)
        return snr_mean

    def _generate_custom_functions_configs(self) -> Dict[str, Dict]:
        """生成自定义函数配置"""
        custom_functions_configs = {}

        # 使用配置类生成SNR配置
        for i in self.data_config.snr_config.parent_snr_values:
            for j_mult in self.data_config.snr_config.child_snr_multipliers:
                for k_mult in self.data_config.snr_config.other_snr_multipliers:
                    j = i * j_mult
                    k = max(i, j) * k_mult

                    key = f'parent_snr_{i}_child_snr_{j}_other_snr_{k}'
                    parent_snr = float(i)
                    child_snr = float(j)
                    other_snr = float(k)

                    # 为每个节点类型生成不同的函数配置
                    def create_node_config(node_config, snr_value):
                        """创建单个节点的配置字典"""
                        config = {
                            'type': node_config.function_type
                        }

                        # 神经网络相关参数（仅对neural_network类型有效）
                        if node_config.function_type == 'random_neural_network':
                            config['hidden_dim'] = node_config.hidden_dim
                            config['depth'] = node_config.depth
                            config['activation'] = node_config.activation

                        # 函数特定参数
                        if node_config.coefficients is not None:
                            config['coefficients'] = node_config.coefficients
                        if node_config.bias is not None:
                            config['bias'] = node_config.bias
                        if node_config.degree is not None:
                            config['degree'] = node_config.degree
                        if node_config.scale is not None:
                            config['scale'] = node_config.scale
                        if node_config.frequency is not None:
                            config['frequency'] = node_config.frequency
                        if node_config.n_components is not None:
                            config['n_components'] = node_config.n_components
                        if node_config.centers is not None:
                            config['centers'] = node_config.centers

                        # SNR和noise_std互斥配置
                        if node_config.target_snr is not None:
                            # 如果节点配置中指定了target_snr，使用节点配置的值
                            config['target_snr'] = node_config.target_snr
                        elif node_config.noise_std is not None:
                            # 如果节点配置中指定了noise_std，使用固定噪声标准差
                            config['noise_std'] = node_config.noise_std
                        else:
                            # 否则使用SNR配置生成的值
                            config['target_snr'] = snr_value

                        # 噪声均值配置
                        if node_config.noise_mean is not None:
                            config['noise_mean'] = node_config.noise_mean

                        # 噪声均值模式配置
                        if node_config.noise_mean_mode != 'fixed':
                            config['noise_mean_mode'] = node_config.noise_mean_mode

                        # g函数配置
                        if node_config.g_function_config is not None:
                            # 如果有完整的g函数配置，使用完整配置
                            config['g_function_config'] = node_config.g_function_config
                        elif node_config.g_function_type != 'identity':
                            # 否则使用简单的g函数类型
                            config['g_function_type'] = node_config.g_function_type

                        return config

                    target_function_config = create_node_config(self.data_config.node_function_configs.target_config, parent_snr)
                    target_child_function_config = create_node_config(self.data_config.node_function_configs.target_child_config, child_snr)
                    other_function_config = create_node_config(self.data_config.node_function_configs.other_config, other_snr)

                    custom_functions_configs[key] = {
                        'name': key,
                        'config': {
                            'target': target_function_config,
                            'target_child': target_child_function_config,
                            'Other_type': other_function_config
                        }
                    }

        return custom_functions_configs


def main():
    """主函数：演示如何使用数据提供器"""
    print("SCM数据生成和获取演示")
    print("=" * 50)

    # 基础配置
    h_config = {
        'device': 'cpu',
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'sample_std': False,
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'train_test_split_ratio': 0.7,
        'task': 'regression',
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'use_monte_carlo_precompute': False
    }

    # SNR配置
    snr_config = SNRConfig(
        parent_snr_values=[2.0, 4.0],  # 可以修改为 [1.0, 2.0, 3.0] 等多个值
        child_snr_multipliers=[0.1, 1.0],  # 子节点SNR倍数
        other_snr_multipliers=[0.1, 1.0]   # 其他节点SNR倍数
    )

    # 节点函数配置 - 支持不同节点类型使用不同函数和g函数
    node_function_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        ),

        target_child_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        ),

        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        )
    )

    # 数据生成配置
    data_config = DataGenerationConfig(
        n_datasets=2,
        dataset_type='perturbation',  # 可选: 'standard', 'intervention', 'perturbation'
        intervention_node_type='all_non_family',
        intervention_value_method='sample',
        perturbation_node_type='all_non_family',
        perturbation_value_method='sample',
        custom_dag_type='random_SF',
        custom_dag_size='small',
        avg_in_degree=(1.2, 1.7),
        device='cuda' if torch.cuda.is_available() else 'cpu',
        snr_config=snr_config,
        node_function_configs=node_function_configs
    )

    # 创建数据提供器并生成数据集
    print("1. 生成数据集...")
    provider = DataProvider(h_config, data_config)
    provider.generate_datasets()

    # 获取所有数据集信息
    print("\n2. 获取数据集信息...")
    datasets_info = provider.get_all_datasets_info()
    for info in datasets_info:
        print(f"  配置 {info['config_key']}: 数据集 {info['dataset_idx']} ({info['dataset_name']})")
        print(f"    数据集类型: {info['dataset_type']}")
        if info['dataset_type'] == 'standard':
            print(f"    样本数: {info['n_samples']}")
        else:
            print(f"    样本数: 原始={info['n_samples']}, {info['dataset_type']}={info['n_samples_modified']}")
        print(f"    特征数: {info['n_features']}")
        print(f"    SNR均值: {info['snr_mean']:.3f}" if not np.isnan(info['snr_mean']) else "    SNR均值: N/A")
        print(f"    DAG: {info['dag_nodes']} 节点, {info['dag_edges']} 边")

    # 获取第一个配置的第一个数据集的分割
    print("\n3. 获取数据集分割...")
    first_config_key = list(provider.datasets_by_config.keys())[0]
    data_splits = provider.get_dataset_splits(config_key=first_config_key, dataset_idx=0)
    print(f"  配置: {data_splits['config_key']}")
    print(f"  数据集: {data_splits['dataset_name']}")
    print(f"  数据集类型: {data_splits['dataset_type']}")
    print(f"  特征名: {data_splits['feature_names']}")
    print(f"  训练集: {data_splits['train_xs'].shape}")

    if data_splits['dataset_type'] == 'standard':
        print(f"  测试集: {data_splits['test_xs'].shape}")
    else:
        print(f"  原始测试集: {data_splits['test_xs_original'].shape}")
        print(f"  {data_splits['dataset_type']}测试集: {data_splits['test_xs_modified'].shape}")

    # 演示获取所有配置的数据集
    print("\n3.5. 获取所有配置的数据集分割...")
    all_splits = provider.get_all_dataset_splits()
    print(f"  获取到 {len(all_splits)} 个配置的数据集分割")
    for config_key, splits_list in all_splits.items():
        print(f"    配置 {config_key}: {len(splits_list)} 个数据集")

    # 保存数据到CSV
    print("\n4. 保存数据到CSV...")
    provider.save_dataset_to_csv(config_key=first_config_key, dataset_idx=0)

    # 绘制DAG图
    print("\n5. 绘制DAG图...")
    provider.draw_dag(config_key=first_config_key, dataset_idx=0)

    print("\n演示完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
