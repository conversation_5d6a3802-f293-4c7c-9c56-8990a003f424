# 环境配置

**跑哪个版本，就把哪个版本的文件夹名字改成tabpfn**
**注意：：tabpfn 的v1和v2不能同时装到一个环境里面(名字一样），可以搞两个虚拟环境，或者每次切换时候自己手工切换v1和v2**

```bash
conda env create -f environment.yml  # 创建一个名字是PFN的虚拟环境；可以打开environment.yml自己改名字
cd PATH-TO TabPFN-v2
pip install -e .
```
**切换tabpfn版本时候注意先uninstall旧版本，确保版本切换正确**

# 参数配置
修改ST_train.py

```python
base_path = './trained_models/exp1' # 配置模型权重输出目录
```

如果需要加载已经训练好的模型打开注释部分
```python 
    model_state = None
#    model_info_tr = torch.load(open('路径/models_diff/prior_diff_real_checkpoint_n_0_epoch_13.cpkt', 'rb'))
 #   model_state = model_info_tr[0]
  #  module_prefix = 'module.'
   # model_state = {k.replace(module_prefix, ''): v for k, v in model_state.items()}
    
```

其它参数自己explore就可以 

# 进行对比

对于单卡而言，每个epoch看到的数据集数量是 8*1024。
如果是多卡，数据集数量直接乘以卡数量。
目前实验基本都是以8卡为基准，即一个epoch数据集数量是8\*1024\*8
如果增加节点，手动减小num_steps；如果卡比8卡少，手动增加num_steps

*v2版本的模型比较大，batch size需要改小一点*
```python
config['aggregate_k_gradients'] = 8
scale = 1/8
config['batch_size'] = 8*config['aggregate_k_gradients']*scale 
config['num_steps'] = 1024//config['aggregate_k_gradients']/scale
```

# 执行训练
```bash
cd tabpfn
conda activate PFN
. run.sh 
```

注意run.sh根据自己的运行环境修改
```bash
torchrun --nproc_per_node=8 ST_train.py # 这是8卡
# 或者
CUDA_VISIBLE_DEVICES=2,3 torchrun --nproc_per_node=2 ST_train.py # 这是在2、3卡
```

# 模型测试
v2版本的模型需要好之后，需要调整模型ckpt的结构，代码在：
v2模型格式修改.ipynb

修改一下参数即可
```python 
model_ours = '训练好的模型路径/prior_diff_real_checkpoint_n_0_epoch_3.cpkt' 
model_default = './tabpfn-v2-classifier.ckpt' # 官方模型路径
model_save = '输出模型路径/prior_diff_real_checkpoint_n_0_epoch_3.cpkt'
```

**！！！！**
注意，测试v2的时候需要
```bash
pip uninstall tabpfn
pip install tabpfn
```
来把版本切换成2.0

## cc18数据集相关操作
### 数据集目录调整建议
cc18数据集的目录文件夹 `/openml/`，建议转移至用户主目录下的 `~/.cache/` 路径下。
不放这个它会自己下载，但是非常慢，直接用我们下载好的移动过去就可以

### 数据集测试运行
- **测试cc18数据集（v1版本）**：
```bash
python test_cc18_v1.py
```
- **测试cc18数据集（v2版本）**：
```bash
python test_cc18_v2.py
```
### 脚本参数设置
对于 `test_cc18_v1.py` 和 `test_cc18_v2.py` 这两个脚本，修改以下参数可实现正常输出：
```python
save_root  = 'output_classifier_v2_default_0225'  # 输出路径，格式：版本+模型类型
version = 'v2'  #或v1
model_ckpt_path = 'tabpfn/tabpfn-v2-03ep-mfp.ckpt'
```
## kaggle数据集

### 数据集准备
解压 `pkl.zip`，可得到374个kaggle相关数据集。解压后的路径地址，需更改 `test_kaggle_v1.py` 和 `test_kaggle_v2.py` 脚本中的 `data_root` 值，原路径为 `data_root = '../../data/pkls'` ，按实际解压路径修改。

### tabPFN不同版本测试kaggle数据集
注意修改下面classifier里面的模型路径
运行对应版本测试脚本：
- **tabPFN v1**：
```bash
python test_kaggle_v1.py
```
- **tabPFN v2**：
```bash
python test_kaggle_v2.py
```

## 测试集可视化操作
### 测试集可视化.ipynb
该文件通过指定 `data_v1_default`、`data_v1_reproduce`、`data_v2_default` 等多个模型结果 `csv` 文件地址来进行可视化操作。
仅供参考 
### 测试集可视化_batch.ipynb
此文件通过批量运行同一个模型的多个 `epoch` 文件来实现可视化。 
仅供参考 