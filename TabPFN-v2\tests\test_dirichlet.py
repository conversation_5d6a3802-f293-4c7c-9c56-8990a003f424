import torch
import numpy as np
import time
import random
import pytest

def get_prototype(temp, num_root, seq_len, num_prototypes):
    with torch.no_grad():
        softmax_temp = max(abs(temp), 1e-4)
        alpha = torch.randn(num_root, seq_len, num_prototypes)
        alpha = torch.nn.functional.softmax(alpha / softmax_temp, dim=-1)
    return alpha

def test_get_prototype_positive_temp():
    """测试正温度参数下的输出"""
    temp = 1.0
    num_root = 3
    seq_len = 5
    num_prototypes = 4
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.shape == (num_root, seq_len, num_prototypes)
    assert torch.all(alpha >= 0) and torch.all(alpha <= 1)
    assert torch.allclose(alpha.sum(dim=-1), torch.ones(num_root, seq_len), atol=1e-6)

def test_get_prototype_negative_temp():
    """测试负温度参数下的输出"""
    temp = -0.5
    num_root = 2
    seq_len = 3
    num_prototypes = 2
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.shape == (num_root, seq_len, num_prototypes)
    assert torch.all(alpha >= 0) and torch.all(alpha <= 1)
    assert torch.allclose(alpha.sum(dim=-1), torch.ones(num_root, seq_len), atol=1e-6)

def test_get_prototype_zero_temp():
    """测试零温度参数下的输出（应自动转为0.1）"""
    temp = 0.0
    num_root = 1
    seq_len = 2
    num_prototypes = 3
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.shape == (num_root, seq_len, num_prototypes)
    assert torch.all(alpha >= 0) and torch.all(alpha <= 1)
    assert torch.allclose(alpha.sum(dim=-1), torch.ones(num_root, seq_len), atol=1e-6)

def test_get_prototype_small_temp():
    """测试极小温度参数下的输出（应自动转为0.1）"""
    temp = 1e-10
    num_root = 2
    seq_len = 4
    num_prototypes = 2
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.shape == (num_root, seq_len, num_prototypes)
    assert torch.all(alpha >= 0) and torch.all(alpha <= 1)
    assert torch.allclose(alpha.sum(dim=-1), torch.ones(num_root, seq_len), atol=1e-6)

def test_get_prototype_large_temp():
    """测试大温度参数下的输出"""
    temp = 100.0
    num_root = 3
    seq_len = 3
    num_prototypes = 5
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.shape == (num_root, seq_len, num_prototypes)
    assert torch.all(alpha >= 0) and torch.all(alpha <= 1)
    assert torch.allclose(alpha.sum(dim=-1), torch.ones(num_root, seq_len), atol=1e-6)

def test_get_prototype_cpu_tensor():
    """测试输出是否为CPU tensor"""
    temp = 0.5
    num_root = 2
    seq_len = 2
    num_prototypes = 2
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.device.type == 'cpu'

def test_get_prototype_randomness():
    """测试不同随机种子下的输出不同"""
    temp = 1.0
    num_root = 2
    seq_len = 2
    num_prototypes = 2
    
    torch.manual_seed(42)
    alpha1 = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    torch.manual_seed(43)
    alpha2 = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert not torch.allclose(alpha1, alpha2)

def test_get_prototype_edge_case():
    """测试边界情况：单原型"""
    temp = 1.0
    num_root = 1
    seq_len = 1
    num_prototypes = 1
    alpha = get_prototype(temp, num_root, seq_len, num_prototypes)
    
    assert alpha.shape == (num_root, seq_len, num_prototypes)
    assert torch.allclose(alpha, torch.ones(num_root, seq_len, num_prototypes))
    
if __name__ == "__main__":
    
    num_prototypes = 10
    prototypes_p = torch.ones(num_prototypes) / num_prototypes
    num_root = 3
    seq_len = 1500
    temp = [0.001, 0.01, 0.05, 0.1, 0.2, 0.24, 0.35, 0.45, 0.5, 0.8, 1, 1.4, 2, 3, 5]
    idx = 0
    idx_row = int(seq_len * 1)
    count = 0
    times = 1000
    
    for t in temp:
        d1 = []
        d2 = []
        count = 0
        for _ in range(times):
            # alpha1 = torch.distributions.dirichlet.Dirichlet(
            #             torch.ones(num_prototypes) * prototypes_p
            #             ).sample([num_root, seq_len]).float()   # (num_root, seq_len, num_prototypes)
            
            # alpha1 = alpha1[idx]
            # diff1 = torch.mean(torch.abs(alpha1[:idx_row] - torch.ones(num_prototypes)/num_prototypes))
            # # print(diff1)
            # d1.append(diff1)
            
            alpha2 = get_prototype(t, num_root, seq_len, num_prototypes)
            alpha2 = alpha2[idx]
            diff2 = torch.mean(torch.abs(alpha2[:idx_row] - torch.ones(num_prototypes)/num_prototypes))
            # print(diff2)
            d2.append(diff2)
            
            # if torch.abs(diff2 - diff1) > 1e-2:
            #     count += 1
            # if diff2 - diff1 > 1e-2:
            #     count += 1
        
        # print(count/times)
        # print(f'dirichlet {np.mean(d1)}')
        print(f'softmax temperature {t}, {np.mean(d2)}')
    