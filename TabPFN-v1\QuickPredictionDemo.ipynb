{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This notebook shows how to use TabPFN for tabular prediction with a scikit learn wrapper.\n", "\n", "classifier = TabPFNClassifier(device='cpu')\n", "classifier.fit(train_xs, train_ys)\n", "prediction_ = classifier.predict(test_xs)\n", "\n", "The fit function does not perform any computations, but only saves the training data. Computations are only done at inference time, when calling predict.\n", "Note that the presaved models were trained for up to 100 features, 10 classes and 1000 samples. While the model does not have a hard bound on the number of samples, the features and classes are restricted and larger sizes lead to an error."]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import torch\n", "import numpy as np\n", "import os\n", "\n", "from scripts.model_builder import get_default_spec, save_model, load_model_only_inference\n", "from scripts.transformer_prediction_interface import transformer_predict, get_params_from_config, TabPFNClassifier\n", "from scripts.differentiable_pfn_evaluation import eval_model, eval_model_range\n", "\n", "from datasets import load_openml_list, open_cc_dids, open_cc_valid_dids, test_dids_classification\n", "\n", "from scripts import tabular_metrics\n", "import random"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_path = '.'"]}, {"cell_type": "markdown", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Load datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["max_samples = 10000\n", "bptt = 10000\n", "\n", "cc_test_datasets_multiclass, cc_test_datasets_multiclass_df = load_openml_list(open_cc_dids, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = max_samples, num_feats=100, return_capped=True)\n", "cc_valid_datasets_multiclass, cc_valid_datasets_multiclass_df = load_openml_list(open_cc_valid_dids, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = max_samples, num_feats=100, return_capped=True)\n", "\n", "# Loading longer OpenML Datasets for generalization experiments (optional)\n", "# test_datasets_multiclass, test_datasets_multiclass_df = load_openml_list(test_dids_classification, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 10000, num_feats=100, return_capped=True)\n", "\n", "random.seed(0)\n", "random.shuffle(cc_valid_datasets_multiclass)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_datasets(selector, task_type, suite='cc'):\n", "    if task_type == 'binary':\n", "        ds = valid_datasets_binary if selector == 'valid' else test_datasets_binary\n", "    else:\n", "        if suite == 'openml':\n", "            ds = valid_datasets_multiclass if selector == 'valid' else test_datasets_multiclass\n", "        elif suite == 'cc':\n", "            ds = cc_valid_datasets_multiclass if selector == 'valid' else cc_test_datasets_multiclass\n", "        else:\n", "            raise Exception(\"Unknown suite\")\n", "    return ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_string, longer, task_type = '', 1, 'multiclass'\n", "eval_positions = [1000]\n", "bptt = 2000\n", "    \n", "test_datasets, valid_datasets = get_datasets('test', task_type, suite='cc'), get_datasets('valid', task_type, suite='cc')"]}, {"cell_type": "markdown", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Run on a single dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[(i, test_datasets[i][0]) for i in range(len(test_datasets))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evaluation_dataset_index = 0 # Index of the dataset to predict\n", "ds = test_datasets[evaluation_dataset_index]\n", "print(f'Evaluation dataset name: {ds[0]} shape {ds[1].shape}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xs, ys = ds[1].clone(), ds[2].clone()\n", "eval_position = xs.shape[0] // 2\n", "train_xs, train_ys = xs[0:eval_position], ys[0:eval_position]\n", "test_xs, test_ys = xs[eval_position:], ys[eval_position:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["classifier = TabPFNClassifier(device='cpu')\n", "classifier.fit(train_xs, train_ys)\n", "prediction_ = classifier.predict_proba(test_xs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["roc, ce = tabular_metrics.auc_metric(test_ys, prediction_), tabular_metrics.cross_entropy(test_ys, prediction_)\n", "'AUC', float(roc), 'Cross Entropy', float(ce)"]}, {"cell_type": "markdown", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Run on all datasets\n", "This section runs a differentiable hyperparameter tuning run and saves the results to a results file, which can be inserted in TabularEval.ipynb to compare to other baselines."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_positions=[1000]\n", "bptt=2000\n", "\n", "N_models = 3\n", "models_per_block = 1\n", "\n", "eval_addition = 'user_run'\n", "device = 'cpu'\n", "\n", "eval_model_range(i_range=[0], e=-1\n", "                          , valid_datasets=[]#cc_valid_datasets_multiclass\n", "                          , test_datasets=cc_test_datasets_multiclass\n", "                          , train_datasets=[]\n", "                          , eval_positions_test=eval_positions\n", "                          , bptt_test=bptt\n", "                          , add_name=model_string\n", "                          , base_path=base_path\n", "                          , selection_metric='auc'\n", "                          , best_grad_steps=0\n", "                          , eval_addition=eval_addition\n", "                          , N_ensemble_configurations_list = [32]\n", "                          , device=device)#range(0, 10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run generalization experiments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Loading longer OpenML Datasets for generalization experiments (optional)\n", "test_datasets_multiclass, test_datasets_multiclass_df = load_openml_list(test_dids_classification, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 10000, num_feats=100, return_capped=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_datasets_longer_generalization = [ds for ds in test_datasets_multiclass if ds[1].shape[0] >= 10000]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_gen(classifier_key, split):\n", "    ces = []\n", "    for k in tqdm(range(0, len(test_datasets_longer_generalization))):\n", "        x, y = test_datasets_longer_generalization[k][1], test_datasets_longer_generalization[k][2].numpy()\n", "        x = normalize_data(x).numpy()\n", "        x[np.isnan(x)] = 0.0\n", "        print(x.shape[0])\n", "        \n", "        if x.shape[0] < 10000:\n", "            continue\n", "        if len(np.unique(y)) > 2:\n", "            continue\n", "\n", "        for bptt_ in [500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 8500, 9000, 9500, 10000]:\n", "            bptt_ = bptt_ // 2\n", "            #model = classifier_dict[classifier_key]\n", "            x_, y_ = x.copy(), y.copy()\n", "            x_train, x_test, y_train, y_test = train_test_split(x_, y_, test_size=0.5, random_state=split)\n", "            x_train, y_train = x_train[0:bptt_], y_train[0:bptt_]\n", "            model.fit(x_train, y_train) # ranking[0:j]\n", "            pred = model.predict_proba(x_test) # ranking[0:j]\n", "            ce = tabular_metrics.auc_metric(y_test, pred)\n", "            ces += [{'bptt': bptt_, 'k': k, 'm': float(ce), 'method': classifier_key, 'split': split}]\n", "            print(x_train.shape, ce)\n", "    with open(f'generalization_{classifier_key}_{split}.obj',\"wb\") as fh:\n", "        pickle.dump(ces,fh)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_gen('tabpfn', 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ces = []\n", "for classifier_key in classifier_dict:\n", "    for split in range(0,5):\n", "        try:\n", "            with open(f'generalization_{classifier_key}_{split}.obj',\"rb\") as fh:\n", "                ces += pickle.load(fh)\n", "        except:\n", "            pass\n", "df = pd.DataFrame(ces)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.groupby(['bptt', 'split', 'method']).mean().reset_index()\n", "fig, ax = plt.subplots(1,1, figsize=(8, 6)) # , sharey=True\n", "\n", "colors = iter(sns.color_palette(\"tab10\"))\n", "for classifier_key in ['tabpfn']:#df.method.unique():\n", "    c = next(colors)\n", "    sns.lineplot(x='bptt', y='m', data=df[df.method==classifier_key], label=relabeler[classifier_key], color=c, ax = ax)\n", "    #ax.text(x = df[df.method==classifier_key].iloc[50].bptt, # x-coordinate position of data label\n", "    # y = df[df.method==classifier_key].iloc[50].m, # y-coordinate position of data label, adjusted to be 150 below the data point\n", "    # s = classifier_key, # data label, formatted to ignore decimals\n", "    # color = c, size=12) # set colour of line\n", "    \n", "ax.get_legend().remove()\n", "ax.set(xlabel='Number of training samples')\n", "ax.set(ylabel='ROC AUC')\n", "plt.axvline(x=1024, linestyle='dashed', color='red')\n", "plt.ylim((0.73,0.79))\n", "plt.xlim((250,5000))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 4}