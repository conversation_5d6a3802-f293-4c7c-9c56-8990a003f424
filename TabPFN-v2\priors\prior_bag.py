import torch

from .utils import get_batch_to_dataloader
from utils import default_device


def get_batch(batch_size, seq_len, num_features, device=default_device
              , hyperparameters=None, batch_size_per_gp_sample=None, **kwargs):

    # 目前batch_size=1 2*6=64--1  2*7=128==2  2*8=256==3
    # 如果batch_size>128,则num_models>2 返回数据结构有变化

    # batch_size:从gbm 故意写死1传入,否则下游接口不适配
    # 官方v1默认2,v2可以是2/4/8

    # batch_size上游写死了1,batch_size_per_gp_sample 写死了None
    # 当入参batch_size<=128,num_models=1
    # 当入参batch_siz>128
    batch_size_per_gp_sample = batch_size_per_gp_sample or (min(64, batch_size))

    num_models = batch_size // batch_size_per_gp_sample
    assert num_models * batch_size_per_gp_sample == batch_size, f'Batch size ({batch_size}) not divisible by batch_size_per_gp_sample ({batch_size_per_gp_sample})'

    # seq_len决定生成数据行数
    # num_features 决定生成数据列数
    args = {'device': device, 'seq_len': seq_len, 'num_features': num_features, 'batch_size': batch_size_per_gp_sample}

    #   源自model_builder.py 的get_model 方法定义
    # 当前写死 prior_bag_get_batch = (get_batch_gp, get_batch_mlp)
    prior_bag_priors_get_batch = hyperparameters['prior_bag_get_batch']

    #   源自model_builder.py 的get_model 方法定义
    #    当前写死 'prior_bag_exp_weights_1': 2.0   所以此处是

    # prior_bag_priors_get_batch 是一维数组
    # 如果prior_bag_priors_get_batch=2 则生成1个元素 range(1, 1)为空 ange(1, 2)=[1]
    #  这一段代码hyperparameters[f'prior_bag_exp_weights_{i}'] for i
    # 生成[prior_bag_exp_weights_{1},prior_bag_exp_weights_{2},.....]
    # 结果
    # [1.0]+[2] = [1,2]
    # prior_bag_priors_p = [1,2]组成数组两个元素
    prior_bag_priors_p = [1.0] + [hyperparameters[f'prior_bag_exp_weights_{i}'] for i in
                                  range(1, len(prior_bag_priors_get_batch))]
    # weights=tensor([1.0000, 5.0501])
    weights = torch.tensor(prior_bag_priors_p, dtype=torch.float)  # create a tensor of weights

    #  torch.multinomial 是 PyTorch 中一个用于多项式分布采样的函数。
    #  它根据给定的概率分布，从一组离散选项中随机抽取样本。简单来说，它就像一个“加权随机选择器”，概率越高的事项越有可能被选中。

    # 并行64线程时,概率选择

    # num_models 决定了 batch_assignments长度
    # batch_assignments 元素值决定了选择哪个模型(模型概率由torch.softmax(weights, 0)决定 作用 1概率归一化 2差异放大)
    # num_models=1时(目前是gp权重1,mlp权重2--选中概率高) batch_assignments=[1] 或 [0]
    # num_models=2时 batch_assignments=[0,0] 或 [0,1] 或 [1,0] 或 [1,1]
    batch_assignments = torch.multinomial(torch.softmax(weights, 0), num_models, replacement=True).numpy()

    if 'verbose' in hyperparameters and hyperparameters['verbose']:
        print('PRIOR_BAG:', weights, batch_assignments)

    # prior_bag_get_batch列表包含多个基础数据生成器（如当前目录下的fast_gp.py/mlp.py等）
    # 新增先验只需扩展该列表，无需修改核心逻辑

    # 数据生成--串行,考虑协程并行
    # batch_assignments 元素
    sample = [prior_bag_priors_get_batch[int(prior_idx)](hyperparameters=hyperparameters, **args, **kwargs) for
              prior_idx in batch_assignments]

    x, y, y_, dicts = zip(*sample)
    x, y, y_ = (torch.cat(x, 1).detach()
                                        , torch.cat(y, 1).detach()
                                        , torch.cat(y_, 1).detach())
    return x, y, y_, dicts

DataLoader = get_batch_to_dataloader(get_batch)