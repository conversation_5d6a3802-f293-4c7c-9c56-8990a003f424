import unittest
import torch
import numpy as np
import networkx as nx
import random


from matplotlib import pyplot as plt
from priors.utils import gamma_sampler_f, beta_sampler_f, normal_sampler_f, uniform_sampler_f, randint_sampler, get_threshold_func_beta
import priors.scm as scm
class TestInputWarping(unittest.TestCase):
    def setUp(self):
        self.x = torch.tensor([[0.1, 0.2, 0.3],[0.1, 0.2, 0.3]], dtype=torch.float32) #X.shape>1
        self.hyperparameters_kumar = {
            'input_warping_type': 'kumar',
            'input_warping_norm': True,
            'input_warping_use_icdf': False,
            'input_warping_c0_std': 0.8002534583197192,
            'input_warping_c1_std': 0.9759720822120248
        }
        self.hyperparameters_none = {
            'input_warping_type': 'none',
            'input_warping_norm': False
        }

    def test_kumar_input_warping(self):
        result = scm.input_warpping(self.x.clone(), self.hyperparameters_kumar)
        self.assertTrue(torch.all(result >= 0) and torch.all(result <= 1))

    def test_no_input_warping(self):
        result = scm.input_warpping(self.x.clone(), self.hyperparameters_none)
        self.assertTrue(torch.equal(result, self.x))

    def test_invalid_input_warping_type(self):
        invalid_hyperparameters = {'input_warping_type': 'invalid'}
        with self.assertRaises(ValueError):
            scm.input_warpping(self.x.clone(), invalid_hyperparameters)

    def test_fixed_c0_c1(self):
        fixed_c0_c1_hyperparameters = {
            **self.hyperparameters_kumar,
            'fix_input_warping_c0': False,
            'fix_input_warping_c1': True
        }
        result = scm.input_warpping(self.x.clone(), fixed_c0_c1_hyperparameters)
        self.assertTrue(torch.all(result >= 0) and torch.all(result <= 1))


class TestGetDAG(unittest.TestCase):

    def test_constant_samplers(self):
        # Test with constant samplers
        dags = scm.get_DAG(
            sampler_num_dag=3,
            sampler_num_node=5,
            sampler_prob=0.3
        )
        self.assertEqual(len(dags), 3)
        for dag in dags:
            self.assertEqual(dag.number_of_nodes(), 5)

    def test_callable_samplers(self):
        # Test with callable samplers
        def sampler_num_dag():
            return 4

        def sampler_num_node():
            return 6

        def sampler_prob():
            return 0.5

        dags = scm.get_DAG(
            sampler_num_dag=sampler_num_dag,
            sampler_num_node=sampler_num_node,
            sampler_prob=sampler_prob
        )
        self.assertEqual(len(dags), 4)
        for dag in dags:
            self.assertEqual(dag.number_of_nodes(), 6)
            self.save_dag_image(dag)
    def test_min_num_nodes(self):
        # Test when the number of nodes is less than the minimum required
        dags = scm.get_DAG(
            sampler_num_dag=3,
            sampler_num_node=5,
            sampler_prob=0.3
        )
        self.assertEqual(len(dags), 3)
        for dag in dags:
            self.assertGreaterEqual(dag.number_of_nodes(), 5)  # 2 * 3 + 2 = 8

    def test_invalid_samplers(self):
        # Test missing required samplers raises AssertionError
        with self.assertRaises(AssertionError):
            scm.get_DAG(
                sampler_num_dag=None,
                sampler_num_node=5,
                sampler_prob=0.3
            )

        with self.assertRaises(AssertionError):
            scm.get_DAG(
                sampler_num_dag=3,
                sampler_num_node=None,
                sampler_prob=0.3
            )

        with self.assertRaises(AssertionError):
            scm.get_DAG(
                sampler_num_dag=3,
                sampler_num_node=5,
                sampler_prob=None
            )

    def test_negative_probability(self):
        # Test negative probability clamps to [0, 1]
        dags = scm.get_DAG(
            sampler_num_dag=3,
            sampler_num_node=5,
            sampler_prob=-0.5
        )
        self.assertEqual(len(dags), 3)
        for dag in dags:
            self.assertEqual(dag.number_of_nodes(), 5)  # 2 * 3 + 2 = 8

    def test_high_probability(self):
        # Test high probability clamps to [0, 1]
        dags = scm.get_DAG(
            sampler_num_dag=3,
            sampler_num_node=5,
            sampler_prob=1.5
        )
        self.assertEqual(len(dags), 3)
        for dag in dags:
            self.assertEqual(dag.number_of_nodes(), 5)  # 2 * 3 + 2 = 8

    def save_dag_image(self, dag: nx.DiGraph, filename="a.png"):
        plt.figure(figsize=(8, 6))
        pos = nx.spring_layout(dag)
        nx.draw(dag, pos, with_labels=True, node_size=10, node_color='skyblue', font_size=10, font_weight='bold',
                arrowsize=20)
        plt.title("Directed Acyclic Graph (DAG)")
        plt.savefig(filename)
        plt.close()

class TestRootDataGeneration(unittest.TestCase):

    def setUp(self):
        self.num_root = 5
        self.seq_len = 10
        self.num_features_used = 3
        self.device = 'cpu'
        self.hyperparameters = {
            'sampler_prototypes_mix': lambda: random.random(),
            'prototypes_fraction': 0.5,
            'prototypes_p': np.array([1.0]),
            'scm_init_data_sampling_method': 'normal'
        }

    def test_normal_sampling(self):
        self.hyperparameters['scm_init_data_sampling_method'] = 'normal'
        root_data = scm.root_data_generation(
            self.num_root,
            self.seq_len,
            self.num_features_used,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(root_data.shape, (self.num_root, self.seq_len, self.num_features_used))
        self.save_tensor_image(root_data, 'normal_sampling.png')

    def test_uniform_sampling(self):
        self.hyperparameters['scm_init_data_sampling_method'] = 'uniform'
        root_data = scm.root_data_generation(
            self.num_root,
            self.seq_len,
            self.num_features_used,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(root_data.shape, (self.num_root, self.seq_len, self.num_features_used))
        self.save_tensor_image(root_data, 'uniform_sampling.png')

    def test_mixed_sampling(self):
        self.hyperparameters['scm_init_data_sampling_method'] = 'mixed'
        root_data = scm.root_data_generation(
            self.num_root,
            self.seq_len,
            self.num_features_used,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(root_data.shape, (self.num_root, self.seq_len, self.num_features_used))
        self.save_tensor_image(root_data, 'mixed_sampling.png')

    def test_prototypes_mix_true(self):
        self.hyperparameters['sampler_prototypes_mix'] = lambda: 0.7
        root_data = scm.root_data_generation(
            self.num_root,
            self.seq_len,
            self.num_features_used,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(root_data.shape, (self.num_root, self.seq_len, self.num_features_used))
        self.save_tensor_image(root_data, 'prototypes_mix_true.png')

    def test_prototypes_mix_false(self):
        self.hyperparameters['sampler_prototypes_mix'] = lambda: 0.3
        root_data = scm.root_data_generation(
            self.num_root,
            self.seq_len,
            self.num_features_used,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(root_data.shape, (self.num_root, self.seq_len, self.num_features_used))
        self.save_tensor_image(root_data, 'prototypes_mix_false.png')

    def save_tensor_image(self, tensor: torch.Tensor, filename: str):
        plt.figure(figsize=(10, 8))
        normalized = (tensor.detach().cpu().numpy() - tensor.detach().cpu().numpy().min()) / (tensor.detach().cpu().numpy().max() - tensor.detach().cpu().numpy().min())
        plt.imshow(normalized, aspect='auto', cmap='viridis')
        plt.colorbar(label='Value')
        plt.title("Generated Root Data")
        plt.xlabel("Feature Index")
        plt.ylabel("Sequence Length")
        plt.savefig(filename)
        plt.close()



class TestGetBatch(unittest.TestCase):

    def setUp(self):
        self.batch_size = 3
        self.seq_len = 10
        self.num_features = 5
        self.device = 'cpu'
        mlp_sampler_modulo = normal_sampler_f(1, 2)
        mlp_sampler_power = uniform_sampler_f(2.5, 2.5)
        categorical_sampler_K = gamma_sampler_f(3, 2)
        sampler_edge_noise_std = normal_sampler_f(1, 1)
        seq_len = 1234
        prototypes_fraction = .4
        num_proto = int(seq_len * prototypes_fraction)
        self.hyperparameters = {
            'num_features_used': 32,
            #  'K': 5,
             'mlp_sampler_modulo': mlp_sampler_modulo,
             'mlp_sampler_power': mlp_sampler_power,
             'categorical_sampler_K': categorical_sampler_K,
             'sampler_edge_noise_std': sampler_edge_noise_std,
             'tree_num_sample_fit': 100,
             'tree_max_num_classes': 10,
             'tree_max_depth': 3,
             'edge_fixed_gaussian_noise': True,
             'sampler_prototypes_mix': lambda: random.random(),
             'prototypes_fraction': prototypes_fraction,   # float
             'prototypes_p': torch.ones(num_proto, device=self.device)/num_proto,
             'DAG_mode': 'multi',
             'scm_init_data_sampling_method': 'uniform',
             'init_data_uniform_scale': 1.5,
             'init_data_normal_std': 1,
             'sampler_edge_funcs_probability': np.ones(3)/3,
            #  'sampler_edge_funcs_probability': np.array([0.4, 0.4, 0.2]),
            #  'sampler_num_dag': randint_sampler(1, 4),
            #  'sampler_num_node': log_uniform_sampling(1.25, 2.75),
             'sampler_num_dag': 2,
             'sampler_num_node': 9,
             'sampler_prob': beta_sampler_f(1.5, 3),
             'sampler_num_dag_node': 'uniform',
             'parents_aggregation_method': 'mixed',
             'tree_leaf_prob': 0.1,
             'tree_threshold_func': get_threshold_func_beta,
             'noise_std':sampler_edge_noise_std()
        }

    def test_get_batch_normal_sampling(self):
        self.hyperparameters['scm_init_data_sampling_method'] = 'normal'
        batch_x, batch_y, _, _ = scm.get_batch(
            self.batch_size,
            self.seq_len,
            self.num_features,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(batch_x.shape, (self.seq_len, self.batch_size, self.num_features))
        self.assertEqual(batch_y.shape, (self.seq_len,))
        self.save_tensor_image(batch_x, 'batch_normal_sampling.png')

    def test_get_batch_uniform_sampling(self):
        self.hyperparameters['scm_init_data_sampling_method'] = 'uniform'
        batch_x, batch_y, _, _ = scm.get_batch(
            self.batch_size,
            self.seq_len,
            self.num_features,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(batch_x.shape, (self.seq_len, self.batch_size, self.num_features))
        self.assertEqual(batch_y.shape, (self.seq_len,))
        self.save_tensor_image(batch_x, 'batch_uniform_sampling.png')

    def test_get_batch_mixed_sampling(self):
        self.hyperparameters['scm_init_data_sampling_method'] = 'mixed'
        batch_x, batch_y, _, _ = scm.get_batch(
            self.batch_size,
            self.seq_len,
            self.num_features,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(batch_x.shape, (self.seq_len, self.batch_size, self.num_features))
        self.assertEqual(batch_y.shape, (self.seq_len,))
        self.save_tensor_image(batch_x, 'batch_mixed_sampling.png')

    def test_prototypes_mix_true(self):
        self.hyperparameters['sampler_prototypes_mix'] = lambda: 0.7
        batch_x, batch_y, _, _ = scm.get_batch(
            self.batch_size,
            self.seq_len,
            self.num_features,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(batch_x.shape, (self.seq_len, self.batch_size, self.num_features))
        self.assertEqual(batch_y.shape, (self.seq_len,))
        self.save_tensor_image(batch_x, 'batch_prototypes_mix_true.png')

    def test_prototypes_mix_false(self):
        self.hyperparameters['sampler_prototypes_mix'] = lambda: 0.3
        batch_x, batch_y, _, _ = scm.get_batch(
            self.batch_size,
            self.seq_len,
            self.num_features,
            self.hyperparameters,
            self.device
        )
        self.assertEqual(batch_x.shape, (self.seq_len, self.batch_size, self.num_features))
        self.assertEqual(batch_y.shape, (self.seq_len,))
        self.save_tensor_image(batch_x, 'batch_prototypes_mix_false.png')

    def save_tensor_image(self, tensor: torch.Tensor, filename: str):
        plt.figure(figsize=(10, 8))
        plt.imshow(tensor.detach().cpu().numpy()[..., 0], aspect='auto', cmap='viridis')
        plt.colorbar(label='Value')
        plt.title("Generated Batch Data")
        plt.xlabel("Feature Index")
        plt.ylabel("Sequence Length")
        plt.savefig(filename)
        plt.close()


if __name__ == '__main__':
    unittest.main()