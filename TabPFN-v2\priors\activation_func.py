import torch
import torch.nn as nn
import random
from typing import *
from types import FunctionType
import numpy as np
from utils import normalize_data
import time

def get_funcs(
    sampler_power: FunctionType, 
    sampler_modulo: FunctionType, 
    val_step: float = 0.5,
    strategy: str = 'uniform',
    num_mixed: int = 3,
    offset_mixed: float = 0,
    v1_implementation: bool = True,
    act_func_name: str = "",
    rank_operation: bool = False,
    rank_sampler_num_classes: Union[FunctionType, int] = None,
    rank_balanced: bool = False,
    class_weights: Union[List, None] = None, 
    rank_sampler_temp: Union[FunctionType, float] = None) -> Union[nn.Module, Tuple]:
    """
    返回备选的一个或多个激活函数.
    
    Args:
        sampler_power (FunctionType): 特定概率分布的取样器, e.g. beta_sampler_f = lambda a, b : lambda : np.random.beta(a, b).
        sampler_modulo (FunctionType): 特定概率分布的取样器, e.g. beta_sampler_f = lambda a, b : lambda : np.random.beta(a, b).
        val_step (float): step function在0点处的值.
        strategy (str, optional): 选择激活函数的策略. 默认为'uniform', 可选'mixed'.
        num_mixed (int, optional): 用于输出混合的激活函数数量. 默认为3.
        offset_mixed (float, optional): 用于提高某一个激活函数的权重. 默认为0.
        v1_implementation (bool, optional): 是否使用V1实现的激活函数组合. 默认为True.

    Returns:
        Union[nn.Module, Tuple]: 某个特定的激活函数或者几个激活函数及其输出的混合权重.
    """
    pows = sampler_power()
    val_modulo = sampler_modulo()
    
    if v1_implementation:
        func_list = [
            (nn.Identity(), 'identity'),
            (nn.Tanh(), 'tanh'),
            (nn.ReLU(), 'relu')
        ]
    else:
        func_list = [
            (nn.Identity(), 'identity'),
            (nn.Tanh(), 'tanh'),
            (Log(), 'log'),
            (Abs(), 'abs'),
            (Sine(), 'sine'),
            (Square(), 'square'),
            (nn.Sigmoid(), 'sigmoid'),
            (nn.Softplus(), 'softplus'),
            # (Power(pows), 'power'),
            (StepFunc(val_step), 'step'),
            (Modulo(val_modulo), 'modulo')
        ]
    
    if rank_operation:
        if isinstance(rank_sampler_num_classes, int):
            num_classes = rank_sampler_num_classes
        else:
            num_classes = rank_sampler_num_classes()
        if isinstance(rank_sampler_temp, float):
            temp = rank_sampler_temp
        else:
            temp = abs(rank_sampler_temp())
        
        func_list.append(
            (Rank(num_classes, rank_balanced, class_weights, temp), 'rank')
        )
    
    if len(act_func_name) > 1:
        for func in func_list:
            if func[1] == act_func_name:
                return func
    
    if strategy == 'uniform':
        # 根据 uniform分布随机选择一个激活函数
        # res = random.choice(func_list)
        # if res[1] == 'power':
        #     res = (res[0], f"power({pows})")
        return random.choice(func_list)
    
    elif strategy == 'mixed':
        # 根据 uniform分布随机选择 num_mixed个激活函数，并以 weights对各个激活函数的输出进行加权求和.
        assert num_mixed > 1, "num_mixed should be greater than 1."
        assert offset_mixed >= 0, "offset_mixed should be greater than or equal to 0."
        
        # 随机选择 num_mixed 个激活函数
        choices = random.choices(func_list, k=num_mixed)
        
        # 随机生成各个激活函数输出的权重
        weights = torch.rand(num_mixed)
        
        # 对某一个激活函数的权重加权
        idx_offset = random.choice(list(range(num_mixed)))
        weights[idx_offset] += offset_mixed
        
        # 使权重总和为 1
        weights /= weights.sum()
        return choices, weights


class Log(nn.Module):
    def __init__(self) -> None:
        super().__init__()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return torch.log(torch.abs(x) + 1e-5)


class Abs(nn.Module):
    def __init__(self) -> None:
        super().__init__()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return torch.abs(x)


class Sine(nn.Module):
    def __init__(self) -> None:
        super().__init__()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return torch.sin(x)


class Square(nn.Module):
    def __init__(self) -> None:
        super().__init__()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        newx = torch.square(x)
        newx = normalize_data(newx)
        return newx


class Power(nn.Module):
    def __init__(self, pows: float, upper: float = 2) -> None:
        super().__init__()
        self.upper = upper
        self.pows = pows
        self.pows = self.upper if self.pows > self.upper else self.pows
        self.pows = -self.upper if self.pows < -self.upper else self.pows
        
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        sign_x = torch.sign(x)  # 保存原始符号
        absx = torch.abs(x)
        powerx = torch.pow(absx, self.pows)
        newx = sign_x * powerx
        newx2 = normalize_data(newx)
        return torch.pow(x, self.pows)
    

class StepFunc(nn.Module):
    def __init__(self, val: float = 0.5) -> None:
        super().__init__()
        self.val = torch.Tensor((val,))
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return torch.heaviside(x, self.val)
    
    
class Modulo(nn.Module):
    def __init__(self, val_modulo: float) -> None:
        super().__init__()
        self.val_modulo = val_modulo
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return torch.remainder(x, self.val_modulo)

class Rank(nn.Module):
    def __init__(self, 
                 num_classes: int, 
                 balanced: bool = True, 
                 class_weights: List[float] = None,
                 temp: float = 1) -> None:
        super().__init__()
        self.num_classes = num_classes
        self.balanced = balanced
        self.class_weights = class_weights
        self.temp = abs(temp)
        self.register_buffer("class_weights_buffer", None)
        if not balanced and class_weights is not None:
            self.register_buffer(
                "class_weights_buffer", torch.tensor(class_weights, dtype=torch.float32)
            )
    
    def forward2(self, x: torch.Tensor) -> torch.Tensor:
        idx_sorted = torch.argsort(x, dim=1, descending=False)
        x_sorted = x[torch.arange(x.shape[0]).unsqueeze(1), idx_sorted]

        # balance
        if self.balanced:
            split_points = torch.linspace(0, 1, self.num_classes+1)[1:-1]
            thresholds = torch.quantile(x_sorted, split_points, dim=1).transpose(1,0)
            
        # imbalance
        else:
            if self.class_weights == None:
                assert self.temp != None, 'temp must be provided when class_weights is not None'
                logits = torch.rand(self.num_classes)
                self.class_weights = list(torch.softmax(logits / self.temp, dim=0))

            cum_weights = torch.tensor(self.class_weights).cumsum(dim=0)
            # 获取排序后的特征值并转换为张量
            threshold_indices = (x_sorted.shape[1] * cum_weights).int() - 1
            thresholds = x_sorted[:, threshold_indices.tolist()]
            
        x_quant = torch.stack([
            torch.searchsorted(thresholds[i], x[i])
            for i in range(x.size(0))
        ])
        return x_quant.float()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # t1 = time.time()
        B, N = x.shape
        idx_sorted = torch.argsort(x, dim=1, descending=False)
        x_sorted = torch.gather(x, dim=1, index=idx_sorted)
        # t2 = time.time()

        if self.balanced:
            split_points = torch.linspace(0, 1, self.num_classes + 1, device=x.device)[1:-1]
            thresholds = torch.quantile(x_sorted, split_points, dim=1, keepdim=False).transpose(1, 0)
            t4 = time.time()
        else:
            if self.class_weights_buffer is None:
                logits = torch.rand(self.num_classes, device=x.device)
                weights = torch.softmax(logits / self.temp, dim=0)
                cum_weights = weights.cumsum(dim=0)
            else:
                cum_weights = self.class_weights_buffer.cumsum(dim=0)

            # threshold_indices = (cum_weights * N).long() - 1
            threshold_indices = (cum_weights * N).long().clamp(max=N - 1)
            threshold_indices2 = threshold_indices.unsqueeze(0).expand(B, -1)
            thresholds = torch.gather(x_sorted, 1, threshold_indices2)
            
            t3 = time.time()

        # Batched searchsorted: leverage torch.searchsorted with matching shapes
        # x: (B, N), thresholds: (B, C-1)
        x_quant = torch.searchsorted(thresholds, x, right=False)
        # t4 = time.time()
        # print(f"sort: {(t2-t1)*1000}ms, balancd: {(t3-t2)*1000}ms, search: {(t4-t3)*1000}ms")
        return x_quant.float()


if __name__ == '__main__':
    
    import time
    
    rank = Rank(10, False, None, 1)
    x = torch.rand(64, 1234, 20)
    
    t1 = time.time()
    o = rank(x)
    print(time.time() - t1)
    
    
    
    