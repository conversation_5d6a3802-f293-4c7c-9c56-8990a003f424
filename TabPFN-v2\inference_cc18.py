#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 
# Filename: inference_cc18.py
# Author: haoyuan
# Created: 2025-04-15
# Description: This is a simple Python script with header information.
# python inference_cc18.py --model_path 'docker_volume_path/generator_models/gen_703_class6_0414_1801/prior_diff_real_checkpoint_n_0_epoch_730.cpkt' --gpuID 0 --data_dir 'docker_volume_path/datasets/openml/'
import openml

from inference.classifier import TabPFNClassifier

import time
import torch
import numpy as np
import os
from tqdm import tqdm

from datasets import load_openml_list, open_cc_dids, open_cc_valid_dids, test_dids_classification,valid_large_classification

from scripts import tabular_metrics
import random

import gc
from xgboost import XGBClassifier
import pandas as pd
base_path = '.'



def get_datasets(selector, task_type, suite='cc'):
    if task_type == 'binary':
        ds = valid_datasets_binary if selector == 'valid' else test_datasets_binary
    else:
        if suite == 'openml':
            ds = valid_datasets_multiclass if selector == 'valid' else test_datasets_multiclass
        elif suite == 'cc':
            ds = cc_valid_datasets_multiclass if selector == 'valid' else cc_test_datasets_multiclass
        else:
            raise Exception("Unknown suite")
    return ds



# 在脚本开头添加参数解析
import argparse  # 新增导入


# 在参数解析部分添加新的参数
if __name__ == '__main__':
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='运行TabPFN评估')
    parser.add_argument('--model_path', type=str, required=True,
                      help='输入模型文件路径，例如 /path/to/model.cpkt')
    parser.add_argument('--gpuID', type=int, default=0,
                      help='指定使用的GPU ID，默认为0')
    parser.add_argument('--data_dir', type=str, default='./data',
                      help='指定cc18数据集本地存储目录，默认为./data')
    args = parser.parse_args()

    # 确保目录存在
    os.makedirs(args.data_dir, exist_ok=True)
    
    # 设置OpenML缓存目录
    import openml.config
    # 创建完整的OpenML缓存目录结构
    openml_cache_dir = os.path.join(os.path.abspath(args.data_dir), 'org', 'openml', 'www')
    os.makedirs(openml_cache_dir, exist_ok=True)
    openml.config.cache_directory = os.path.abspath(args.data_dir)  # 设置基础目录
    openml.config.set_root_cache_directory(os.path.abspath(args.data_dir))  # 确保所有子目录都使用这个基础目录
    # print(f"OpenML缓存目录设置为: {openml_cache_dir}")

    # 验证缓存目录是否设置成功
    # print(f"当前OpenML缓存目录: {openml.config.get_cache_directory()}")

    model_save = args.model_path
    # 修改原有硬编码路径为参数获取
    model_file = args.model_path
    model_ours = model_file  # 替换原有 base_path 和 model_file_name 的定义
    model_save_name = os.path.basename(model_ours).replace('.cpkt', '-mfp.cpkt')
    model_save = os.path.join(os.path.dirname(model_ours), model_save_name)
    model_default = 'tabpfn-v2-classifier.cpkt'
    model_m = torch.load(open(model_ours,'rb'))
    model_d = torch.load(open(model_default,'rb'))
    my_stat = model_m[0]

    new_stat = {}
    for k, v in my_stat.items():
        new_stat[k.replace('module.','')] = v
    for k, v in model_d['state_dict'].items():
        model_d['state_dict'][k]=new_stat[k]

    torch.save(model_d, open(model_save,'wb'))
    # print(model_save, 'model_save, done')

    # 动态生成保存路径
    cc18_results_path = model_file.replace('.cpkt', '_cc18_results')
    save_root_dir = model_file.split('/')[-3]
    save_root = cc18_results_path.replace(save_root_dir, save_root_dir+'_cc18')
    os.makedirs(save_root, exist_ok=True)
    # 新增检查：如果结果文件存在则提前退出
    result_file = os.path.join(save_root, 'all_rst.csv')
    if os.path.exists(result_file):
        print(f"结果文件已存在，跳过执行: {result_file}")
        exit(0)

    # cc18_tasks = openml.tasks.list_tasks(tag='OpenML-CC18')
    # 用于存储数据集 ID 的集合
    dataset_ids = set()

    # 遍历任务列表，提取数据集 ID
    # for task_id, task_info in cc18_tasks.items():
    #     dataset_ids.add(task_info['did'])
    cc18_dataset_ids = [
        1049, 1053, 1067, 11, 14, 1462, 1468, 1480, 1486, 1489, 1497, 1501, 1510, 16, 182, 22, 23381, 28, 3, 307, 32, 38, 40668, 40701, 40966, 40978, 40982, 40984, 41027, 44, 4538, 46, 50, 554, 6332,
        1050, 1063, 1068, 12, 1461, 1464, 1475, 1485, 1487, 1494, 15, 151, 1590, 18, 188, 23, 23517, 29, 300, 31, 37, 40499, 40670, 40923, 40975, 40979, 40983, 40994, 4134, 4534, 458, 469, 54, 6
    ]
    valid_large_classification = list(cc18_dataset_ids)
    # 安全移除元素，如果不存在则不操作
    for did in [40927, 1478, 40996]:
        if did in valid_large_classification:
            valid_large_classification.remove(did)
    
    cc_test_datasets_multiclass, cc_test_datasets_multiclass_df = load_openml_list(
        valid_large_classification, 
        multiclass=True, 
        shuffled=True, 
        filter_for_nan=False, 
        max_samples=10000000, 
        num_feats=10000, 
        return_capped=False
    )

    model_string, longer, task_type = '', 1, 'multiclass'
    test_datasets= get_datasets('test', task_type, suite='cc')
    

 
    rsts = []
    version = 'v2'  # v1 or v2 这里记得改

    for evaluation_dataset_index in tqdm(range(len(test_datasets))):

        ds = test_datasets[evaluation_dataset_index]
        # print(f'Evaluation dataset name: {ds[0]} shape {ds[1].shape}')
        if ds[1].shape[1] > 100:
            continue
        if ds[1].shape[0] > 50000:
            continue
        # data_device = 'cpu' if ds[1].shape[0] > 50000 else 'cuda:6'
        # gpu_id = int(args.gpuID) % 4 + 4  # 从命令行参数获取GPU ID 轮训使用 4,5,6,7
        gpu_id = int(args.gpuID) % 8  # 从命令行参数获取GPU ID   轮训使用全部8卡
        data_device = f'cuda:{gpu_id}'  # 修改此行

        gc.collect()  # 强制进行垃圾回收
        if 'cuda' in data_device:
            torch.cuda.empty_cache()  # 再次确保显存释放

        rst = {'dataset name':ds[0], 'num_data':ds[1].shape[0],'num_feat':ds[1].shape[1], 'num_class':len(np.unique(ds[2]))}
        xs, ys = ds[1].clone(), ds[2].clone()
        eval_position = int(xs.shape[0] *0.50)
        train_xs, train_ys = xs[0:eval_position], ys[0:eval_position]
        test_xs, test_ys = xs[eval_position:], ys[eval_position:]
        train_xs.shape, train_ys.shape, test_xs.shape, test_ys.shape
        
        classifier = XGBClassifier(device=data_device)
        classifier.fit(train_xs, train_ys)
        prediction_ = classifier.predict_proba(test_xs)
        
        roc, ce = tabular_metrics.auc_metric(test_ys, prediction_), tabular_metrics.cross_entropy(test_ys, prediction_)
        class_num = prediction_.shape[1]
        output_df = {'label':test_ys.numpy()}
        # for i in range(class_num):
        #     output_df[f'pred_{i}'] = prediction_[:,i]
        # pd.DataFrame(output_df).to_csv(os.path.join(save_root, ds[0]+'_pred_xgboost.csv'), index=False)
        rst['AUC_xgboost'] = float(roc)

        # print(f"cuda status: {torch.cuda.is_available()}")
        # try:
        classifier = TabPFNClassifier(device=data_device, ignore_pretraining_limits=True, model_path=model_save)
        classifier.fit(train_xs, train_ys)
        
        prediction_ = classifier.predict_proba(test_xs)
        roc, ce = tabular_metrics.auc_metric(test_ys, prediction_), tabular_metrics.cross_entropy(test_ys, prediction_)

        rst['AUC_PFN'] = float(roc)
        output_df = {'label':test_ys.numpy()}
        # for i in range(class_num):
        #     output_df[f'pred_{i}'] = prediction_[:,i]
        # pd.DataFrame(output_df).to_csv(os.path.join(save_root, ds[0]+'_pred_pfc.csv'), index=False)
        # except:
        #     rst['AUC_PFN'] = -1
        rsts.append(rst)

        gc.collect()  # 强制进行垃圾回收
        if 'cuda' in data_device:
            torch.cuda.empty_cache()  # 再次确保显存释放
        # print(rst)
    rsts = pd.DataFrame(rsts)
    rsts.to_csv(os.path.join(save_root, 'all_rst.csv'), index=False)
