import random
import time
import warnings
from datetime import datetime

import torch

import numpy as np

# import matplotlib.pyplot as plt
from scripts.differentiable_pfn_evaluation import eval_model_range
from scripts.model_builder import get_model, get_default_spec, save_model, load_model
from scripts.transformer_prediction_interface import transformer_predict, get_params_from_config, load_model_workflow

from scripts.model_configs import *

# from datasets import load_openml_list, open_cc_dids, open_cc_valid_dids
from priors.utils import plot_prior, plot_features
from priors.utils import uniform_int_sampler_f, gamma_sampler_f, beta_sampler_f, normal_sampler_f, uniform_sampler_f, randint_sampler, get_threshold_func_beta, log_uniform_sampling, beta_sampler_range

from scripts.tabular_metrics import calculate_score_per_method, calculate_score
from scripts.tabular_evaluation import evaluate

from priors.differentiable_prior import DifferentiableHyperparameterList, draw_random_style, merge_style_with_info
from scripts import tabular_metrics
from notebook_utils import *
import argparse

import openml
from datasets import load_openml_list, open_cc_dids, open_cc_valid_dids, test_dids_classification,valid_large_classification
from tqdm import tqdm 
import pandas as pd
import gc
from utils import load_model_dicts

import signal
signal.signal(signal.SIGHUP, signal.SIG_IGN)
import socket
def get_host_info():
    hostname = socket.gethostname()
    ip_address = socket.gethostbyname(hostname)
    return f"{hostname}-{ip_address}"

device = "cuda"
# base_path = './'
# base_path = './测试模型/'        # TODO 参数化       
host_info = get_host_info()
# 节点信息，将日志与物理机关联的信息
vid = {

}


def restore_functions(config: dict):
    if args.num_features_distribution == 'beta':
        config['differentiable_hyperparameters']['num_features_used'] = {'distribution': 'meta_beta', 'scale': config['num_features'] - 2, 'b':config['num_features_beta_b'], 'k':config['num_features_beta_k'], 'data_type': 'int', 'bias':2} # 注意：这里的b是beta分布的a，k是贝塔分布的b！！！！！
        if 'num_features_used' in config:
            del config['num_features_used']
    else:
        config["num_features_used"] = {'uniform_int_sampler_f(3,max_features)': uniform_int_sampler_f(1, config['num_features'])}
        if 'num_features_used' in config['differentiable_hyperparameters']:
            del config['differentiable_hyperparameters']['num_features_used']
        
    mlp_sampler_modulo = normal_sampler_f(config['mlp_sampler_modulo_mean'], config['mlp_sampler_modulo_std'])
    mlp_sampler_power = uniform_sampler_f(config['mlp_sampler_power_min'], config['mlp_sampler_power_max'])
    categorical_sampler_K = gamma_sampler_f(config['categorical_sampler_a'], config['categorical_sampler_b'])
    sampler_edge_noise_std = normal_sampler_f(config['sampler_edge_noise_std_mean'], config['sampler_edge_noise_std_std'])
    num_proto = int(config['bptt'] * config['prototypes_fraction'])

    config['sampler_prototypes_mix'] = lambda: random.random()
    config['prototypes_p'] = torch.ones(num_proto)/num_proto

    config['mlp_sampler_modulo'] = mlp_sampler_modulo
    config['mlp_sampler_power'] = mlp_sampler_power
    config['categorical_sampler_K'] = categorical_sampler_K
    config['sampler_edge_noise_std'] = sampler_edge_noise_std
    config['sampler_num_dag'] = randint_sampler(config['sampler_num_dag_min'], config['sampler_num_dag_max'])
    config['sampler_num_node'] = log_uniform_sampling(config['sampler_num_node_min'], config['sampler_num_node_max'])

    config['sampler_prob'] = beta_sampler_f(config['sampler_prob_a'], config['sampler_prob_b'])
    # config['tree_threshold_func'] = beta_sampler_range(config['tree_threshold_func_beta_a'], config['tree_threshold_func_beta_b'])
    config['rank_sampler_num_classes'] = randint_sampler(config['rank_sampler_num_classes_min'], config['rank_sampler_num_classes_max'])
    config['rank_sampler_temp'] = uniform_sampler_f(config['rank_sampler_temp_min'], config['rank_sampler_temp_max'])

    if config['using_diffrence_sample_prob']:
        config['differentiable_hyperparameters']['sample_root_prob_x'] = {'distribution': 'meta_trunc_norm', 
                                                                        'max_mean': config['sample_root_prob_x_max_mean'], 
                                                                        'min_mean': config['sample_root_prob_x_min_mean'], 
                                                                        'max_std': config['sample_root_prob_x_max_std'], 
                                                                        'min_std': config['sample_root_prob_x_min_std'], 
                                                                        'round': False,                      
                                                                        'lower_bound': config['sample_root_prob_x_lower_bound'], 
                                                                        'upper_bound': config['sample_root_prob_x_upper_bound']}
        config['differentiable_hyperparameters']['sample_leaf_prob_x'] = {'distribution': 'meta_trunc_norm', 
                                                                        'max_mean': config['sample_leaf_prob_x_max_mean'], 
                                                                        'min_mean': config['sample_leaf_prob_x_min_mean'], 
                                                                        'max_std': config['sample_leaf_prob_x_max_std'], 
                                                                        'min_std': config['sample_leaf_prob_x_min_std'], 
                                                                        'round': False,                      
                                                                        'lower_bound': config['sample_leaf_prob_x_lower_bound'], 
                                                                        'upper_bound': config['sample_leaf_prob_x_upper_bound']}
    config["num_classes"] = uniform_int_sampler_f(2, config['max_num_classes'])
    if isinstance(config['tree_input_scale_str'], str):
        if config['tree_input_scale_str'] == 'uniform':
            config['tree_input_scale'] = uniform_sampler_f(config['tree_input_scale_min'], config['tree_input_scale_max'])
        elif config['tree_input_scale_str'] == 'beta':
            beta_f = beta_sampler_f(config['tree_input_scale_a'], config['tree_input_scale_b'])
            config['tree_input_scale'] = lambda: beta_f() * (config['tree_input_scale_max'] - config['tree_input_scale_min']) + config['tree_input_scale_min']
        else:
            raise ValueError(f"tree_input_scale must be uniform/beta or a float, but got: {config['tree_input_scale']}, type: {type(config['tree_input_scale'])}")
    else:
        config['tree_input_scale'] = config['tree_input_scale_str']
    
    if isinstance(config['reg_tree_input_weight_str'], str):
        if config['reg_tree_input_weight_str'] == 'uniform':
            config['reg_tree_input_weight'] = uniform_sampler_f(config['reg_tree_input_weight_min'], config['reg_tree_input_weight_max'])
        elif config['reg_tree_input_weight_str'] == 'beta':
            beta_f = beta_sampler_f(config['reg_tree_input_weight_a'], config['reg_tree_input_weight_b'])
            config['reg_tree_input_weight'] = lambda: beta_f() * (config['reg_tree_input_weight_max'] - config['reg_tree_input_weight_min']) + config['reg_tree_input_weight_min']
        else:
            raise ValueError('reg_tree_input_weight_str must be uniform/beta or a float')
    else:
        config['reg_tree_input_weight'] = config['reg_tree_input_weight_str']

    
# 获取测试集数据加载器
def get_evaluate_dataloader(config):
    if("evaluate" in config and "dataset" in config["evaluate"] and len(config["evaluate"]["dataset"]) > 0):
        if "num_feats" not in config["evaluate"]:
            config["evaluate"]["num_feats"] = 100
        if "max_samples" not in config["evaluate"]:
            config["evaluate"]["max_samples"] = 10000
        cc18_tasks = openml.tasks.list_tasks(tag=config["evaluate"]["dataset"])  #'OpenML-CC18'
        dataset_ids = set()
        for task_id, task_info in cc18_tasks.items():
            dataset_ids.add(task_info['did'])
        
        valid_large_classification = list(dataset_ids)
        valid_large_classification.remove(40927)
        valid_large_classification.remove(1478)
        valid_large_classification.remove(40996)
        
        # test_datasets, _ = load_openml_list(valid_large_classification, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 10000000, num_feats=10000, return_capped=False)
        test_datasets, _ = load_openml_list(valid_large_classification, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 10000000, num_feats=config["evaluate"]["num_feats"], return_capped=False)

        # 删除长度超过阈值的数据集
        for i in range(len(test_datasets)-1,-1,-1):
            if test_datasets[i][1].shape[1] > config["evaluate"]["num_feats"] or test_datasets[i][1].shape[0] > config["evaluate"]["max_samples"]:
                print(f"Dropping dataset: {test_datasets[i][0]}, shape: {test_datasets[i][1].shape}")
                test_datasets.pop(i)
                continue
        
            
            test_datasets[i].append(np.unique(test_datasets[i][2].numpy()))
            test_datasets[i][1] = test_datasets[i][1].unsqueeze(1)
            test_datasets[i][2] = test_datasets[i][2].unsqueeze(1).float()
            
        return test_datasets
    else:
        return []

def evaluate_model(model, epoch, config, dataset):
    if "evaluate" not in config:
        return None, None
    if "dataset" not in config["evaluate"] or config["evaluate"]["dataset"] is None:
        print("Please specify the dataset to evaluate in the config file.")
        return None, None
    
    if "save_path" not in config["evaluate"] or config["evaluate"]["save_path"] is None:
        config["evaluate"]["save_path"] = "./results"
    if "single_eval_pos" not in config["evaluate"] or config["evaluate"]["single_eval_pos"] is None:
        config["evaluate"]["single_eval_pos"] = 0.5
    if "save_each_dataset_result" not in config["evaluate"] or config["evaluate"]["save_each_dataset_result"] is None:
        config["evaluate"]["save_each_dataset_result"] = False
        
    save_path = config["evaluate"]["save_path"]
    os.makedirs(save_path, exist_ok=True)
    
    print("start evaluate model ...")
    
    if dataset is None or len(dataset) == 0:
        dataset = get_evaluate_dataloader(config)
    

    # 模型设置为评估模式
    model.eval()
    
    device = next(model.parameters()).device
    
    if device.type == 'cuda':
        torch.cuda.empty_cache()  # 再次确保显存释放
    
    rsts = []
    AUC_all = 0
    # print(f"len(dataset) = {len(dataset)}")
    # print(f"range len(dataset) = {range(len(dataset))}")
    
    with torch.no_grad():
        for index, ds in enumerate(dataset):
            num = ds[1].shape[0]
            eval_position = int(num * config["evaluate"]["single_eval_pos"])
            
            rst = {'dataset name':ds[0], 'num_data':ds[1].shape[0],'num_feat':ds[1].shape[1], 'num_class':len(np.unique(dataset[0][2]))}
        
            
            xs, ys = ds[1].clone(), ds[2].clone()
            test_xs, test_ys = xs[eval_position:], ys[eval_position:]
            # test_xs.to(device)
            test_xs_device = ds[1].to(device)
            test_ys_device = ds[2].to(device)
            
            prediction_ = model((None, test_xs_device, test_ys_device), single_eval_pos=eval_position, only_return_standard_out = True)
            prediction_ = prediction_.squeeze(1)
            class_num = len(ds[6])
            prediction_ = prediction_[:, :class_num]
            
            prediction2 = torch.nn.functional.softmax(prediction_, dim=1)
            prediction3 = prediction2 / prediction2.sum(axis=1, keepdims=True) 
            
            prediction3_cpu = prediction3.cpu()
            test_ys = test_ys.squeeze(1)
            roc = tabular_metrics.auc_metric(test_ys, prediction3_cpu), 
            # cd = tabular_metrics.cross_entropy(test_ys, prediction_.cpu())
            
            rst['AUC_PFN'] = float(roc[0])
            AUC_all += float(roc[0])
            # 保存每个数据集的结果
            if config["evaluate"]["save_each_dataset_result"]:
                output_df = {'label':test_ys.numpy()}
                for i in range(class_num):
                    output_df[f'pred_{i}'] = prediction3_cpu[:,i]
                pd.DataFrame(output_df).to_csv(os.path.join(save_path, f"epoch_{epoch}_{ds[0]}_pred_pfc.csv"), index=False)
        
            rsts.append(rst)
    AUC_average = AUC_all / len(rsts)
    rsts = pd.DataFrame(rsts)
    rsts.to_csv(os.path.join(save_path, f"epoch_{epoch}_all_rst.csv"), index=False)
    
    gc.collect()  # 强制进行垃圾回收
    if device.type == 'cuda':
        torch.cuda.empty_cache()  # 再次确保显存释放
        
    # 恢复训练模式
    model.train()
    
    return dataset, AUC_average


def print_models(model_string):
    print(model_string)

    for i in range(80):
        for e in range(50):
            exists = Path(os.path.join("./", f'models_diff/prior_diff_real_checkpoint{model_string}_n_{i}_epoch_{e}.cpkt')).is_file()
            if exists:
                print(os.path.join("./", f'models_diff/prior_diff_real_checkpoint{model_string}_n_{i}_epoch_{e}.cpkt'))
        print()
        


def train_function(config_sample, i, add_name=''):
    start_time = time.time()
    N_epochs_to_save = config_sample['save_model_frequency']
    maximum_runtime = 60 * 22
    dataset = []  # get_evaluate_dataloader(config_sample)
    
    model_dicts = None
    if("resume_weight" in config_sample and len(config_sample['resume_weight']) > 1):
        if not Path(config_sample['resume_weight']).is_file():
            print("resume_weight not found at", config_sample['resume_weight'])
            print('train from scratch')
        else:
            model_infos = load_model_dicts(config_sample['resume_weight'])  # (model_state, optimizer_state, scheduler_state, scaler_state, config_sample)
            if model_infos:
                # model_info_tr = torch.load(open(config_sample['resume_weight'], 'rb'),  map_location=torch.device('cpu'))
                # model_state = model_info_tr[0]
                # module_prefix = 'module.'
                # model_state = {k.replace(module_prefix, ''): v for k, v in model_state.items()}
                
                # optimizer_state = model_info_tr[3]
                # scheduler_state = model_info_tr[4]
                # scaler_state    = model_info_tr[5]
                
                # model_dicts = (model_state, optimizer_state, scheduler_state, scaler_state)
                model_dicts = model_infos[:4]
                
                if 'use_new_params' in config_sample and config_sample['use_new_params']:
                    # config_sample['epoch_in_training'] = model_info_tr[2]['epoch_in_training']
                    config_sample['epoch_in_training'] = model_infos[4]['epoch_in_training']
                else:
                    prior_mlp_activations = config_sample['differentiable_hyperparameters']['prior_mlp_activations']
                    config_sample =  model_infos[4]
                    config_sample["load_from_ckpt"] = True
                    # 参数中存在函数，函数无法直接恢复，需要使用原始数据重新构建函数
                    restore_functions(config_sample)
                    config_sample['differentiable_hyperparameters']['prior_mlp_activations'] = prior_mlp_activations
            else:
                print("resume_weight not found at", config_sample['resume_weight'])
                print('train from scratch')
    
    
    def save_callback(model, optimizer, scheduler, scaler, epoch, ext_info):
        nonlocal dataset
        if not hasattr(model, 'last_saved_epoch'):
            model.last_saved_epoch = 1
        # if ((time.time() - start_time) / (maximum_runtime * 60 / N_epochs_to_save)) > model.last_saved_epoch:
        #N_epochs_to_save = 1
        if epoch % N_epochs_to_save == 0:
            model.last_saved_epoch = epoch
            if(len(dataset) == 0):
                dataset = get_evaluate_dataloader(config_sample)
            _, auc = evaluate_model(model, epoch, config_sample, dataset)
            
            os.makedirs(os.path.join(config_sample['base_path'], task_name), exist_ok=True)
            print('Saving model..', epoch, model.last_saved_epoch )
            config_sample['epoch_in_training'] = epoch
            save_name = os.path.join(config_sample['base_path'], f'{task_name}/prior_diff_real_checkpoint{add_name}_n_{i}_epoch_{model.last_saved_epoch}.cpkt')
            save_model(model, optimizer, scheduler, scaler, config_sample['base_path'], f'{task_name}/prior_diff_real_checkpoint{add_name}_n_{i}_epoch_{model.last_saved_epoch}.cpkt',
                           config_sample, ext_info)
            model.last_saved_epoch = model.last_saved_epoch + N_epochs_to_save # TODO: Rename to checkpoint
            return save_name, auc
        else:
            return None, None

    model = get_model(config_sample
                      , device
                      , should_train=True
                      , verbose=1
                      , epoch_callback = save_callback
                        ,state_dict=model_dicts)
    
    return model

def reload_config(config_type='causal', task_type='multiclass', longer=0):
    config = get_prior_config(config_type=config_type)
    
    config['prior_type'], config['differentiable'], config['flexible'] = 'prior_bag', True, True
    
    model_string = ''
    
    config['recompute_attn'] = True

    config['max_num_classes'] = 10
    config['num_classes'] = uniform_int_sampler_f(2, config['max_num_classes'])
    config['balanced'] = False
    model_string = model_string + '_multiclass'
    
    model_string = model_string + '_'+datetime.now().strftime("%m_%d_%Y_%H_%M_%S")
    
    return config, model_string


config, model_string = reload_config(longer=1)

def get_last_ckpt(path, task_id):
    import re
    folder_list = [d for d in Path(path).glob(f'{task_id}*') if d.is_dir()]
    if len(folder_list) == 0:
        return None
    
    checked_folder = []
    id_len = len(task_id)
    for folder in folder_list:
        tmp = str(folder.name)[id_len:]
        if len(tmp) == 10:
            checked_folder.append(folder)
            
    task_dir = sorted(
        checked_folder,
        key=lambda x: x.stat().st_ctime
    )[-1]
    
    all_files = list(Path(task_dir).glob(f'prior_diff_real_checkpoin*_n_*_epoch_*.cpkt'))
    if len(all_files) > 0:
        max_epoch_file = max(
            all_files,
            key=lambda x: int(re.search(r'_epoch_(\d+)', x.name).group(1)),
            default=None
        )
        return str(max_epoch_file)
    else:
        return None

if False:
    config['bptt_extra_samples'] = None

    # diff
    config['output_multiclass_ordered_p'] = 0.
    del config['differentiable_hyperparameters']['output_multiclass_ordered_p']

    config['multiclass_type'] = 'rank'
    del config['differentiable_hyperparameters']['multiclass_type']

    config['sampling'] = 'normal' # vielleicht schlecht?
    del config['differentiable_hyperparameters']['sampling']

    config['pre_sample_causes'] = True
    # end diff

    config['multiclass_loss_type'] = 'nono' # 'compatible'
    config['normalize_to_ranking'] = False # False

    config['categorical_feature_p'] = .2 # diff: .0

    # turn this back on in a random search!?
    config['nan_prob_no_reason'] = .0
    config['nan_prob_unknown_reason'] = .0 # diff: .0
    config['set_value_to_nan'] = .1 # diff: 1.

    config['normalize_with_sqrt'] = False

    config['new_mlp_per_example'] = True
    config['prior_mlp_scale_weights_sqrt'] = True
    config['batch_size_per_gp_sample'] = None

    config['normalize_ignore_label_too'] = False

    config['differentiable_hps_as_style'] = False
    config['random_feature_rotation'] = True
    config['rotate_normalized_labels'] = True

    config["mix_activations"] = False # False heisst eig True

    config['emsize'] = 512
    config['nhead'] = config['emsize'] // 128
    config['canonical_y_encoder'] = False


    # 默认的总数据量为1024*8*8 
    config['total_dataset'] = 128*1*1 # 每个epoch的数据量 所有卡的
    config['GPU_num'] = 1 # 实际的卡数 仅用于计算batch
    config['batch_sigle'] = 2 # 单卡batch
    config['update_num'] = 64  # 多少个数据量更新一次梯度 需要是GPU_num*batch_sigle的整倍数


    try:
        assert config['update_num']  % (config['batch_sigle'] * config['GPU_num']) == 0
    except AssertionError:
        raise ValueError("config['update_num'] 需要是GPU_num*batch_sigle的整倍数")

    # ----- 以下这些参数自动计算 不要手工修改----- 
    config['aggregate_k_gradients'] = config['update_num'] // (config['batch_sigle'] * config['GPU_num'])
    config['batch_size'] = config['batch_sigle'] * config['aggregate_k_gradients'] 
    config['num_steps'] = config['total_dataset'] // config['batch_size'] //config['GPU_num'] * config['batch_sigle']
    # --- 以上这些参数自动计算 不要手工修改 ---

    config['num_features'] = 160
    config['differentiable_hyperparameters']['num_features_used'] = {'distribution': 'meta_beta', 'scale': config['num_features'] - 1, 'b':8, 'k':0.95, 'data_type': 'int', 'bias':1} # 注意：这里的b是beta分布的a，k是贝塔分布的b！！！！！


    config['total_available_time_in_s'] = None # 60*60*22 # 22 hours for some safety...         # 无效参数
    config['train_mixed_precision'] = True      
    config['efficient_eval_masking'] = True             # 无效参数
    config['lr'] = 0.00018
    config['project_name'] = 'pfn-v2-wk04'
    config['dropout'] = 0.1
    config['weight_decay'] = 0.03
    config['warmup_epochs'] = 5
    config['epochs'] = 1000
    config['bptt'] = 1024+128
    config['max_eval_pos'] = int(config['bptt']*0.95)
    config['min_eval_pos'] = int(config['bptt']*0.40)
    config['task_ID'] = "task_1"
    config['wandb_api_key'] = ""
    # config['min_num_layers_layer_dropout'] = 5  # 只支持单卡 多卡reduce时会有问题 layer-dropout 这个数字越大，drop的层越少；不要太小；不要超过nlayers 不想drop层数可以设置成nlayers+1或者直接注释掉这一行
    # config['resume_weight'] = 'PATH TO CKPT'
    config['use_new_params'] = False

def float_or_str(value):
    try:
        return float(value)
    except ValueError:
        # 如果转换失败，检查是否为特定的字符串
        valid_strings = ['uniform', 'beta']
        if value in valid_strings:
            return value
        else:
            raise argparse.ArgumentTypeError(f"Invalid value: {value}. Allowed strings are {valid_strings} or any float.")


parser = argparse.ArgumentParser(description='PFN训练')
parser.add_argument('--resume', type=str, help='恢复训练的权重文件路径')
parser.add_argument('--disable_auto_resume', action='store_true', default=False, help='关闭自动恢复训练（自动搜索最新的模型文件，若不存在则启动新的训练）')
parser.add_argument('--use_new_params', action='store_true', default=False, help='是否在恢复训练时使用新的参数')

parser.add_argument('--prior_type', type=str, choices=['prior_bag', 'mlp', 'gp', 'gp_mix', 'v2'], default='v2', help='数据生成策略')
parser.add_argument('--disable_differentiable', action='store_true', default=False, help='关闭超参生成（数据生成使用）')
parser.add_argument('--disable_flexible', action='store_true', default=False, help='关闭数据后处理（数据生成使用）')
parser.add_argument('--disable_recompute_attn', action='store_true', default=False, help='关闭反向传播过程中是否重新计算注意力结果')
parser.add_argument('--max_num_classes', type=int, default=10, help='最大类别数量（数据生成使用）')
parser.add_argument('--balanced', action='store_true', default=False, help='是否使用均匀二值化分类器（数据生成使用）')

parser.add_argument('--bptt_extra_samples', type=int, default=0, help='bptt额外样本数')
parser.add_argument('--output_multiclass_ordered_p', type=float, default=0., help='分类进行排序的概率（数据生成使用）')
parser.add_argument('--multiclass_type', type=str, default='rank', choices=['rank', 'value'], help='多分类的分类器类型（数据生成使用）')
parser.add_argument('--sampling', type=str, default='normal', choices=['normal', 'mixed'], help='因变量采样方式（数据生成使用）')
parser.add_argument('--disable_pre_sample_causes', action='store_true', default=False, help='关闭预先设定因果关系的均值和方差（数据生成使用）')
parser.add_argument('--multiclass_loss_type', type=str, default='nono', choices=['nono', 'compatible'], help='多分类loss类型（数据生成使用）')
parser.add_argument('--normalize_to_ranking', action='store_true', default=False, help='是否对特征进行排序（数据生成使用）')
parser.add_argument('--categorical_feature_p', type=float, default=0.2, help='对特征分类的概率（数据生成使用）')
parser.add_argument('--nan_prob_no_reason', type=float, default=0., help='数据无原因缺失概率（数据生成使用）弃用')
parser.add_argument('--nan_prob_unknown_reason', type=float, default=0., help='数据未知原因缺失概率（数据生成使用）弃用')
parser.add_argument('--nan_prob_a_reason', type=float, default=0., help='数据有原因缺失概率（数据生成使用）弃用')
parser.add_argument('--set_value_to_nan', type=float, default=0.1, help='缺失数据设置为NaN、±inf的概率（数据生成使用）弃用')

# --- 新版噪声参数 ---
parser.add_argument('--prob_have_nan', type=float, default=0., help='存在异常值的数据集的比例，包括：nan+±inf')
parser.add_argument('--prob_have_nan_feature_no_reason_min', type=float, default=0.1, help='数据集中存在随机异常值的列比例的最小值,uniform，包括：nan+±inf')
parser.add_argument('--prob_have_nan_feature_no_reason_max', type=float, default=0.2, help='数据集中存在随机异常值的列比例的最大值,uniform，包括：nan+±inf')
parser.add_argument('--prob_have_nan_feature_reason_min', type=float, default=0.1, help='数据集中存在与X相关异常值的列比例的最小值,uniform，包括：nan+±inf')
parser.add_argument('--prob_have_nan_feature_reason_max', type=float, default=0.2, help='数据集中存在与X相关异常值的列比例的最大值,uniform，包括：nan+±inf')
parser.add_argument('--prob_have_nan_value_min', type=float, default=0.05, help='列中异常值比例的最小值,uniform，包括：nan+±inf')
parser.add_argument('--prob_have_nan_value_max', type=float, default=0.5, help='列中异常值比例的最大值,uniform，包括：nan+±inf')
parser.add_argument('--prob_have_nan_type_min', type=float, default=1., help='异常值为nan的比例的最大值,uniform，剩余的异常值在±inf中随机取')
parser.add_argument('--prob_have_nan_type_max', type=float, default=1., help='异常值为nan的比例的最大值,uniform，剩余的异常值在±inf中随机取')


parser.add_argument('--normalize_with_sqrt', action='store_true', default=False, help='是否对标准化后数据开根号（数据生成使用）')
parser.add_argument('--disable_new_mlp_per_example', action='store_true', default=False, help='关闭每次采样生成新的mlp（数据生成使用）')
parser.add_argument('--disable_prior_mlp_scale_weights_sqrt', action='store_true', default=False, help='关闭在权重初始化时使用平方根来调整缩放因子（数据生成使用）')
parser.add_argument('--batch_size_per_gp_sample', type=int, default=0, help='高斯过程采样的batchsize（数据生成使用）')
parser.add_argument('--normalize_ignore_label_too', action='store_true', default=False, help='是否对非法标签进行随机循环偏移标签（数据生成使用）')
parser.add_argument('--differentiable_hps_as_style', action='store_true', default=False, help='是否返回数据生成使用的超参（数据生成使用）')
parser.add_argument('--disable_random_feature_rotation', action='store_true', default=False, help='关闭随机旋转特征顺序（数据生成使用）')
parser.add_argument('--disable_rotate_normalized_labels', action='store_true', default=False, help='关闭随机循环偏移标签（数据生成使用）')
parser.add_argument('--mix_activations', action='store_true', default=False, help='是否使用混合激活函数（数据生成使用）')
parser.add_argument('--canonical_y_encoder', action='store_true', default=False, help='是否使用典型的 y encoder（数据生成使用）')

parser.add_argument('--padding_type', type=str, default='max_num', choices=['max_num', 'min_num', 'random'], help='同批次数据补零方式，max_num:补充至最大有效特征数量，min_num:补充至最小有效特征数量，random:补充至最大与最小有效特征数量间的随机值（数据生成使用）')
parser.add_argument('--padding_fill_type', type=str, default='zero', choices=['zero', 'gaussian_noise'], help='同批次数据补零方式，zero:填充0，gaussian_noise:填充高斯噪声,(0,2)分布,（数据生成使用）')

parser.add_argument('--num_features', type=int, default=160, help='最大特征数量')
parser.add_argument('--num_features_distribution', type=str, default='beta', choices=['uniform', 'beta'], help='随机特征数量的分布（数据生成使用）')
parser.add_argument('--num_features_beta_b', type=float, default=0.95, help='特征数量使用beta分布时的参数b（数据生成使用）, 注意: 此处的b为beta分布的alpha参数')
parser.add_argument('--num_features_beta_k', type=float, default=8, help='特征数量使用beta分布时的参数k（数据生成使用）, 注意: 测出的k为beta分布的gamma参数')

parser.add_argument('--y_balance_max_retry', type=int, default=3, help='标签平衡最大重试次数（数据生成使用）')

parser.add_argument('--using_v1_activations', action='store_true', default=False, help='mlp是否使用V1的激活函数（数据生成使用）')

parser.add_argument('--save_scm', action='store_true', default=False, help='是否保存SCM的相关信息（v2数据生成使用）')
parser.add_argument('--path_save_scm', type=str, default='./Data_SCM/', help='保存SCM相关信息的路径（v2数据生成使用）')
parser.add_argument('--num_features_used_predefined', type=int, default=3, help='根节点的初始数据维度（v2数据生成使用）')
parser.add_argument('--edge_output_dim_scale', type=float, default=1, help='边函数输出维度收缩系数（v2数据生成使用）')
parser.add_argument('--mlp_sampler_modulo_mean', type=float, default=1, help='激活函数求余采样器的正太分布均值（v2数据生成使用）')
parser.add_argument('--mlp_sampler_modulo_std', type=float, default=2, help='激活函数求余采样器的正太分布标准差（v2数据生成使用）')
parser.add_argument('--mlp_sampler_power_min', type=float, default=-2.5, help='激活函数指数采样器的均值分布最小值（v2数据生成使用）')
parser.add_argument('--mlp_sampler_power_max', type=float, default=2.5, help='激活函数指数采样器的均值分布最大值（v2数据生成使用）')
parser.add_argument('--rank_operation', action='store_true', default=False, help='是否添加rank激活函数（v2数据生成使用）')
parser.add_argument('--rank_balanced', action='store_true', default=False, help='是否添加rank激活函数（v2数据生成使用）')
parser.add_argument('--rank_sampler_num_classes_min', type=int, default=2, help='激活函数rank标签数最小值（v2数据生成使用）')
parser.add_argument('--rank_sampler_num_classes_max', type=int, default=10, help='激活函数rank标签数最大值（v2数据生成使用）')
parser.add_argument('--rank_sampler_temp_min', type=float, default=1, help='激活函数rank softmax温度最小值（v2数据生成使用）')
parser.add_argument('--rank_sampler_temp_max', type=float, default=4, help='激活函数rank softmax温度最大值（v2数据生成使用）')
parser.add_argument('--categorical_sampler_a', type=float, default=3, help='离散化中簇树量采样器的a参数（v2数据生成使用）')
parser.add_argument('--categorical_sampler_b', type=float, default=2, help='离散化中簇树量采样器的b参数（v2数据生成使用）')
parser.add_argument('--conv_kernel_size', type=int, default=5, help='边函数Conv1d的kernel size（v2数据生成使用）')
# parser.add_argument('--conv_along_seq', type=float, default=1, help='边函数Conv1d的卷积方向（v2数据生成使用）')
# parser.add_argument('--EDGE_LIST_append', action='store_true', default=False, help='是否使用新的边函数（v2数据生成使用）')
parser.add_argument('--sampler_edge_noise_std_mean', type=float, default=1, help='边函数高斯噪音标准差采样器的均值（v2数据生成使用）')
parser.add_argument('--sampler_edge_noise_std_std', type=float, default=1, help='边函数高斯噪音方差采样器的标准差（v2数据生成使用）')
parser.add_argument('--prototypes_fraction', type=float, default=.02, help='prototypes采样比例（v2数据生成使用）')
parser.add_argument('--disable_edge_fixed_gaussian_noise', action='store_true', default=False, help='关闭使用边噪音（v2数据生成使用）')
parser.add_argument('--scm_init_data_sampling_method', type=str, choices=['uniform', 'normal', 'mixed', 'mixed_v2'], default='normal', help='根数据生成采样模式（v2数据生成使用）')
parser.add_argument('--init_data_uniform_scale', type=float, default=1.5, help='跟数据unifor采样缩放值（v2数据生成使用）')
# parser.add_argument('--init_data_normal_std', type=float, default=1, help='生成根数据高斯采样标准差（v2数据生成使用）')
parser.add_argument('--get_gnr_DAG', action='store_true', default=False, help='生成DAG图结构是否使用GNR,默认GNC（v2数据生成使用）')
parser.add_argument('--sampler_num_dag_min', type=int, default=1, help='dag图采样器最小值（v2数据生成使用）')
parser.add_argument('--sampler_num_dag_max', type=int, default=2, help='dag图采样器最大值（v2数据生成使用）')
parser.add_argument('--sampler_num_node_min', type=float, default=1.25, help='每个DAG中节点数LogUniform采样下限（v2数据生成使用）')
parser.add_argument('--sampler_num_node_max', type=float, default=3.75, help='每个DAG中节点数LogUniform采样上限（v2数据生成使用）')
parser.add_argument('--sampler_prob_a', type=float, default=1.5, help='DAG生成在定向概率beta分布的a（v2数据生成使用）')
parser.add_argument('--sampler_prob_b', type=float, default=3, help='DAG生成在定向概率beta分布的b（v2数据生成使用）')
parser.add_argument('--parents_aggregation_method', type=str, choices=['mixed', 'mean', 'mlp'], default='mean', help='父节点数据聚合方法（v2数据生成使用）')

parser.add_argument('--tree_max_num_classes', type=int, default=5, help='决策树输出类别最大数量（v2数据生成使用）')
parser.add_argument('--tree_max_depth', type=int, default=5, help='决策树最大深度（v2数据生成使用）')
parser.add_argument('--tree_leaf_prob', type=float, default=0.1, help='决策树生成叶节点概率（v2数据生成使用）')
# parser.add_argument('--tree_threshold_func_beta_a', type=float, default=3, help='决策树分类概率beta分布的a（v2数据生成使用）')
# parser.add_argument('--tree_threshold_func_beta_b', type=float, default=3, help='决策树分类概率beta分布的b（v2数据生成使用）')
parser.add_argument('--tree_threshold_mode', type=str, choices=['beta_3_3', 'beta_1p5_3', 'beta_5_5', 'beta_1p8_8', 'uniform'], default = 'beta_3_3', help='决策树分类概率分布类型')

parser.add_argument('--reg_tree_max_depth', type=int, default=5, help='回归树最大深度（v2数据生成使用）')
parser.add_argument('--reg_tree_leaf_prob', type=float, default=0.1, help='回归树生成叶节点概率（v2数据生成使用）')
parser.add_argument('--reg_tree_threshold_mode', type=str, choices=['beta_3_3', 'beta_1p5_3', 'beta_5_5', 'beta_1p8_8', 'uniform'], default = 'beta_3_3', help='回归树分类阈值概率分布类型')
parser.add_argument('--reg_tree_input_weight', type=float_or_str, default=0.1, help='回归树叶节点与输入的加权权重（v2数据生成使用）')
parser.add_argument('--reg_tree_input_weight_min', type=float, default=0, help='回归树叶节点与输入的加权权重分布的最小值（v2数据生成使用）')
parser.add_argument('--reg_tree_input_weight_max', type=float, default=1, help='回归树叶节点与输入的加权权重分布的最大值（v2数据生成使用）')
parser.add_argument('--reg_tree_input_weight_a', type=float, default=3, help='回归树叶节点与输入的加权权重的beta分布的a（v2数据生成使用）')
parser.add_argument('--reg_tree_input_weight_b', type=float, default=3, help='回归树叶节点与输入的加权权重的beta分布的b（v2数据生成使用）')

parser.add_argument('--is_causal_p', type=float, default=0.5, help='生成因果数据的概率（v2数据生成使用）')
parser.add_argument('--disable_use_dirichlet', action='store_true', default=False, help='生成根数据时是否使用dirichlet采样（v2数据生成使用）')
parser.add_argument('--root_data_temp', type=float, default=0.24, help='生成根数据时softmax的temperature（v2数据生成使用）')

parser.add_argument('--input_warpings_ratio', type=float, default=0.2, help='对dataset进行warping的概率（v2数据生成后处理）')
parser.add_argument('--input_warping_type', type=str, choices=['kumar', 'none', None], default='kumar', help='warping方法（v2数据生成后处理）')
parser.add_argument('--input_warping_groups', type=str, default='x', help='对X还是Y进行warping（v2数据生成后处理）')
parser.add_argument('--disable_input_warping_norm', action='store_true', default=False, help='关闭对生成的X进行warping（v2数据生成后处理）')
parser.add_argument('--input_warping_c1_std', type=float, default=0.9759720822120248, help='Kumaraswamy的c1（v2数据生成后处理）')
parser.add_argument('--input_warping_c0_std', type=float, default=0.8002534583197192, help='Kumaraswamy的c0（v2数据生成后处理）')

# TODO : 修改为mean,gap模式，mean +- 0.05
parser.add_argument('--disable_using_diffrence_sample_prob', action='store_true', default=False, help='关闭对根节点、叶节点、中间节点是否使用不同采样概率（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_mean', type=float, default=-1, help='数据采样root节点的概率采样器，高斯均值,>0有效（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_gap', type=float, default=0.1, help='数据采样root节点的概率采样器，高斯均值上下限偏差值（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_min_mean', type=float, default=0.35,   help='数据采样root节点的概率采样器，高斯均值限值（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_max_mean', type=float, default=0.45,   help='数据采样root节点的概率采样器，高斯均值限值（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_min_std', type=float, default=0.039,   help='数据采样root节点的概率采样器，高斯方差限值（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_max_std', type=float, default=0.117,   help='数据采样root节点的概率采样器，高斯方差限值（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_lower_bound', type=float, default=0.3, help='数据采样root节点的概率采样器，极值（v2数据生成后处理）')
parser.add_argument('--sample_root_prob_x_upper_bound', type=float, default=0.5, help='数据采样root节点的概率采样器，极值（v2数据生成后处理）')
parser.add_argument('--auto_adjust_root_num', action='store_true', default=False, help='是否自适应调整X在root节点的采样数量')

parser.add_argument('--sample_leaf_prob_x_mean', type=float, default=-1, help='数据采样root节点的概率采样器，高斯均值,>0有效（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_gap', type=float, default=0.1, help='数据采样root节点的概率采样器，高斯均值上下限偏差值（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_min_mean', type=float, default=0.05,    help='数据采样leaf节点的概率采样器，高斯均值限值（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_max_mean', type=float, default=0.15,    help='数据采样leaf节点的概率采样器，高斯均值限值（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_min_std', type=float, default=0.039,    help='数据采样leaf节点的概率采样器，高斯方差限值（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_max_std', type=float, default=0.117,    help='数据采样leaf节点的概率采样器，高斯方差限值（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_lower_bound', type=float, default=0.01, help='数据采样leaf节点的概率采样器，极值（v2数据生成后处理）')
parser.add_argument('--sample_leaf_prob_x_upper_bound', type=float, default=0.2,  help='数据采样leaf节点的概率采样器，极值（v2数据生成后处理）')

parser.add_argument('--edge_funcs_mlp_p', type=float, default=0.7, help='边函数为MLP的概率（v2数据生成后处理）')
parser.add_argument('--edge_funcs_cate_p', type=float, default=0,  help='边函数为Categorical的概率（v2数据生成后处理）')
parser.add_argument('--edge_funcs_tree_p', type=float, default=0.3,  help='边函数为DecisionTree的概率（v2数据生成后处理）')
parser.add_argument('--edge_funcs_conv1d_p', type=float, default=0,  help='边函数为Conv1d的概率（v2数据生成后处理）')
parser.add_argument('--edge_funcs_mlp_2layers_p', type=float, default=0,  help='边函数为MLP 2 layers的概率（v2数据生成后处理）')
parser.add_argument('--edge_funcs_reg_tree_p', type=float, default=0,  help='边函数为回归树的概率（v2数据生成后处理）')

parser.add_argument('--tree_input_scale', type=float_or_str, default=1.0, help='决策树输入数据维度缩放，支持输入float比例、采样类型【uniform,beta】（v2数据生成后处理）')
parser.add_argument('--tree_input_min_num', type=int, default=3, help='决策树输入数据维度最小值（v2数据生成后处理）')
parser.add_argument('--tree_input_scale_min', type=float, default=0.2, help='决策树输入数据维度缩放比例最小值（v2数据生成后处理）')
parser.add_argument('--tree_input_scale_max', type=float, default=1.0, help='决策树输入数据维度缩放比例最大值（v2数据生成后处理）')
parser.add_argument('--tree_input_scale_a', type=float, default=3, help='决策树输入数据维度缩放比例beta分布的a（v2数据生成后处理）')
parser.add_argument('--tree_input_scale_b', type=float, default=3, help='决策树输入数据维度缩放比例beta分布的b（v2数据生成后处理）')
parser.add_argument('--tree_output_norm', action='store_true', default=False, help='是否将决策树的label映射到[-1, 1]范围内（v2数据生成后处理）')

parser.add_argument('--emsize', type=int, default=192, help='embedding size')

parser.add_argument('--total_dataset', type=int, default=1024*8*8, help='每个epoch的数据量 所有卡的')
parser.add_argument('--GPU_num', type=int, default=8, help='每个epoch的数据量 所有卡的')
parser.add_argument('--batch_sigle', type=int, default=2, help='每个epoch的数据量 所有卡的')
parser.add_argument('--update_num', type=int, default=64, help='每个epoch的数据量 所有卡的')


parser.add_argument('--disalbe_train_mixed_precision', action='store_true', default=False, help='关闭使用混合精度训练')
parser.add_argument('--lr', type=float, default=0.0002, help='学习率')
parser.add_argument('--dropout', type=float, default=0, help='dropout 率')
parser.add_argument('--weight_decay', type=float, default=0.001, help='weight_decay')
parser.add_argument('--warmup_epochs', type=int, default=100, help='warmup_epochs量')
parser.add_argument('--epochs', type=int, default=1000, help='epochs量')
parser.add_argument('--bptt', type=int, default=2048, help='bptt值,支持输入整型数字和字符串，字符串：["uniform"]')
parser.add_argument('--bptt_min', type=int, default=400, help='bptt均匀采样最小值')
parser.add_argument('--bptt_use_fixed', action='store_true', default=False, help='bptt是否使用固定值（默认均匀采样）')
parser.add_argument('--max_eval_pos_factor', type=float, default=0.95, help='max_eval_pos与bptt比值')
parser.add_argument('--min_eval_pos_factor', type=float, default=0.40, help='min_eval_pos与bptt比值')
parser.add_argument('--project_name', type=str, default="test", help='工程名称')
parser.add_argument('--task_ID', type=str, default="task_1", help='任务ID')
parser.add_argument('--wandb_api_key', type=str, default="", help='wandb api key')
parser.add_argument('--base_path', type=str, default="./", help='结果存储路径')

parser.add_argument('--enable_online_evaluation', action='store_true', default=False, help='是否使用在线评估功能')
parser.add_argument('--dataset', type=str, default="OpenML-CC18", choices=['OpenML-CC18'], help='验证使用的数据集名称')
parser.add_argument('--single_eval_pos', type=float, default=0.5, help='评估使用的pos值')
parser.add_argument('--num_feats', type=int, default=100, help='特征数量')
parser.add_argument('--max_samples', type=int, default=50000, help='每个数据集最大样本数量')
parser.add_argument('--save_each_dataset_result', action='store_true', default=False, help='是否保存每个数据集的结果')
parser.add_argument('--save_model_frequency', type=int, default=10, help='保存模型epoch间隔')
parser.add_argument('--max_resum_times', type=int, default=1, help='训练loss爆炸后最多恢复次数（每爆炸一次恢复一次）')

parser.add_argument('--disable_auto_seq_len', action='store_true', default=False, help='关闭当内存不足时自动减小bptt(seq_len)的功能（针对24G显存）')

parser.add_argument('--debug', action='store_true', default=False, help='是否为debug模式')
parser.add_argument('--profiler_on', type=int, default=0, help='是否开启profiler性能分析')

args = parser.parse_args()

config['prior_type'] = args.prior_type
config['differentiable'] = not args.disable_differentiable
config['flexible'] = not args.disable_flexible
config['recompute_attn'] = not args.disable_recompute_attn
config['max_num_classes'] = args.max_num_classes
config['num_classes'] = uniform_int_sampler_f(2, config['max_num_classes'])
config['bptt_extra_samples'] = None if args.bptt_extra_samples == 0 else args.bptt_extra_samples
config['output_multiclass_ordered_p'] = args.output_multiclass_ordered_p
del config['differentiable_hyperparameters']['output_multiclass_ordered_p']
config['multiclass_type'] = args.multiclass_type
del config['differentiable_hyperparameters']['multiclass_type']
config['sampling'] = args.sampling
del config['differentiable_hyperparameters']['sampling']
config['pre_sample_causes'] = not args.disable_pre_sample_causes
config['multiclass_loss_type'] = args.multiclass_loss_type
config['normalize_to_ranking'] = args.normalize_to_ranking
config['categorical_feature_p'] = args.categorical_feature_p
config['nan_prob_no_reason'] = args.nan_prob_no_reason
config['nan_prob_unknown_reason'] = args.nan_prob_unknown_reason
config['nan_prob_a_reason'] = args.nan_prob_a_reason

config['prob_have_nan'] = args.prob_have_nan
config['prob_have_nan_feature_no_reason_min'] = args.prob_have_nan_feature_no_reason_min
config['prob_have_nan_feature_reason_min'] = args.prob_have_nan_feature_reason_min
config['prob_have_nan_value_min'] = args.prob_have_nan_value_min
config['prob_have_nan_type_min'] = args.prob_have_nan_type_min
config['prob_have_nan_feature_no_reason_max'] = args.prob_have_nan_feature_no_reason_max
config['prob_have_nan_feature_reason_max'] = args.prob_have_nan_feature_reason_max
config['prob_have_nan_value_max'] = args.prob_have_nan_value_max
config['prob_have_nan_type_max'] = args.prob_have_nan_type_max


config['set_value_to_nan'] = args.set_value_to_nan
config['normalize_with_sqrt'] = args.normalize_with_sqrt
config['new_mlp_per_example'] = not args.disable_new_mlp_per_example
config['prior_mlp_scale_weights_sqrt'] = not args.disable_prior_mlp_scale_weights_sqrt
config['batch_size_per_gp_sample'] = None if args.batch_size_per_gp_sample == 0 else args.batch_size_per_gp_sample
config['normalize_ignore_label_too'] = args.normalize_ignore_label_too
config['differentiable_hps_as_style'] = args.differentiable_hps_as_style
config['random_feature_rotation'] = not args.disable_random_feature_rotation
config['rotate_normalized_labels'] = not args.disable_rotate_normalized_labels
config['mix_activations'] = args.mix_activations
config['using_v1_activations'] = args.using_v1_activations
config['emsize'] = args.emsize
config['nhead'] = config['emsize'] // 128
config['canonical_y_encoder'] = args.canonical_y_encoder
config['padding_type'] = args.padding_type
config['padding_fill_type'] = args.padding_fill_type
config['total_dataset'] = args.total_dataset
config['GPU_num'] = args.GPU_num
config['batch_sigle'] = args.batch_sigle
config['update_num'] = args.update_num
config['num_features'] = args.num_features
config['is_causal_p'] = args.is_causal_p
config['num_features_beta_b'] = args.num_features_beta_b
config['num_features_beta_k'] = args.num_features_beta_k
config['num_features_distribution'] = args.num_features_distribution
config['y_balance_max_retry'] = args.y_balance_max_retry
config['root_data_temp'] = args.root_data_temp
config['use_dirichlet'] = not args.disable_use_dirichlet

config['tree_input_scale_str'] = args.tree_input_scale
config['tree_input_scale_min'] = args.tree_input_scale_min
config['tree_input_scale_max'] = args.tree_input_scale_max
config['tree_input_min_num'] = args.tree_input_min_num
config['tree_input_scale_a'] = args.tree_input_scale_a
config['tree_input_scale_b'] = args.tree_input_scale_b
config['tree_output_norm'] = args.tree_output_norm

config['train_mixed_precision'] = not args.disalbe_train_mixed_precision
config['lr'] = args.lr
config['dropout'] = args.dropout
config['weight_decay'] = args.weight_decay
config['warmup_epochs'] = args.warmup_epochs
config['epochs'] = args.epochs
config['bptt'] = args.bptt
config['bptt_min'] = args.bptt_min
config['bptt_use_uniform'] = not args.bptt_use_fixed
config['max_eval_pos_factor'] = args.max_eval_pos_factor
config['min_eval_pos_factor'] = args.min_eval_pos_factor
config['max_eval_pos'] = int(config['bptt']*args.max_eval_pos_factor)
config['min_eval_pos'] = int(config['bptt']*args.min_eval_pos_factor)
config['project_name'] = args.project_name
config['task_ID'] = args.task_ID
config['wandb_api_key'] = args.wandb_api_key
config['base_path'] = args.base_path
config['debug'] = args.debug
config['save_scm'] = args.save_scm
config['path_save_scm'] = args.path_save_scm

config['enable_auto_seq_len'] = not args.disable_auto_seq_len

config['use_new_params'] = args.use_new_params

# V2生成器参数 形参化
config['num_features_used_predefined'] = args.num_features_used_predefined
config['edge_output_dim_scale'] = args.edge_output_dim_scale
config['mlp_sampler_modulo_mean'] = args.mlp_sampler_modulo_mean
config['mlp_sampler_modulo_std'] = args.mlp_sampler_modulo_std
config['mlp_sampler_power_min'] = args.mlp_sampler_power_min
config['mlp_sampler_power_max'] = args.mlp_sampler_power_max
config['rank_operation'] = args.rank_operation
config['rank_balanced'] = args.rank_balanced
config['rank_sampler_num_classes_min'] = args.rank_sampler_num_classes_min
config['rank_sampler_num_classes_max'] = args.rank_sampler_num_classes_max
config['rank_sampler_temp_min'] = args.rank_sampler_temp_min
config['rank_sampler_temp_max'] = args.rank_sampler_temp_max
config['conv_kernel_size'] = args.conv_kernel_size
# config['conv_along_seq'] = args.conv_along_seq
# config['EDGE_LIST_append'] = args.EDGE_LIST_append
config['categorical_sampler_a'] = args.categorical_sampler_a
config['categorical_sampler_b'] = args.categorical_sampler_b
config['sampler_edge_noise_std_mean'] = args.sampler_edge_noise_std_mean
config['sampler_edge_noise_std_std'] = args.sampler_edge_noise_std_std
config['prototypes_fraction'] = args.prototypes_fraction
config['get_gnc_DAG'] = not args.get_gnr_DAG
config['sampler_num_dag_min'] = args.sampler_num_dag_min
config['sampler_num_dag_max'] = args.sampler_num_dag_max
config['sampler_prob_a'] = args.sampler_prob_a
config['sampler_prob_b'] = args.sampler_prob_b
# config['tree_threshold_func_beta_a'] = args.tree_threshold_func_beta_a
# config['tree_threshold_func_beta_b'] = args.tree_threshold_func_beta_b
config['decision_tree_threshold_mode_str'] = args.tree_threshold_mode
# 'beta_3_3', 'beta_1p5_3', 'beta_5_5', 'beta_1p8_8', 'uniform'
if config['decision_tree_threshold_mode_str'] == 'beta_3_3':
    config['decision_tree_threshold_mode'] = 1
elif config['decision_tree_threshold_mode_str'] == 'beta_1p5_3':
    config['decision_tree_threshold_mode'] = 2
elif config['decision_tree_threshold_mode_str'] == 'beta_5_5':
    config['decision_tree_threshold_mode'] = 3
elif config['decision_tree_threshold_mode_str'] == 'beta_1p8_8':
    config['decision_tree_threshold_mode'] = 4
elif config['decision_tree_threshold_mode_str'] == 'uniform':
    config['decision_tree_threshold_mode'] = 0
else:
    raise NotImplementedError
config['parents_aggregation_method'] = args.parents_aggregation_method
config['tree_leaf_prob'] = args.tree_leaf_prob
config['tree_max_num_classes'] = args.tree_max_num_classes
config['tree_max_depth'] = args.tree_max_depth

config['reg_tree_max_depth'] = args.reg_tree_max_depth
config['reg_tree_leaf_prob'] = args.reg_tree_leaf_prob
config['reg_tree_threshold_mode'] = args.reg_tree_threshold_mode
config['reg_tree_input_weight_str'] = args.reg_tree_input_weight
config['reg_tree_input_weight_min'] = args.reg_tree_input_weight_min
config['reg_tree_input_weight_max'] = args.reg_tree_input_weight_max
config['reg_tree_input_weight_a'] = args.reg_tree_input_weight_a
config['reg_tree_input_weight_b'] = args.reg_tree_input_weight_b

config['edge_fixed_gaussian_noise'] = not args.disable_edge_fixed_gaussian_noise
config['scm_init_data_sampling_method'] = args.scm_init_data_sampling_method
config['using_diffrence_sample_prob'] = not args.disable_using_diffrence_sample_prob
config['init_data_uniform_scale'] = args.init_data_uniform_scale

if args.sample_root_prob_x_mean > 0:
    config['sample_root_prob_x_max_mean'] = args.sample_root_prob_x_mean + 0.05
    config['sample_root_prob_x_min_mean'] = max(0, args.sample_root_prob_x_mean - 0.05)
    config['sample_root_prob_x_lower_bound'] = max(0, args.sample_root_prob_x_mean - args.sample_root_prob_x_gap)
    config['sample_root_prob_x_upper_bound'] = args.sample_root_prob_x_mean + args.sample_root_prob_x_gap
else:
    config['sample_root_prob_x_max_mean'] = args.sample_root_prob_x_max_mean
    config['sample_root_prob_x_min_mean'] = args.sample_root_prob_x_min_mean
    config['sample_root_prob_x_lower_bound'] = args.sample_root_prob_x_lower_bound
    config['sample_root_prob_x_upper_bound'] = args.sample_root_prob_x_upper_bound
config['sample_root_prob_x_max_std'] = args.sample_root_prob_x_max_std
config['sample_root_prob_x_min_std'] = args.sample_root_prob_x_min_std
config['auto_adjust_root_num'] = args.auto_adjust_root_num

if args.sample_leaf_prob_x_mean > 0:
    config['sample_leaf_prob_x_max_mean'] = args.sample_leaf_prob_x_mean + 0.05
    config['sample_leaf_prob_x_min_mean'] = max(0, args.sample_leaf_prob_x_mean - 0.05)
    config['sample_leaf_prob_x_lower_bound'] = max(0, args.sample_leaf_prob_x_mean - args.sample_leaf_prob_x_gap)
    config['sample_leaf_prob_x_upper_bound'] = args.sample_leaf_prob_x_mean + args.sample_leaf_prob_x_gap
else:
    config['sample_leaf_prob_x_max_mean']    = args.sample_leaf_prob_x_max_mean
    config['sample_leaf_prob_x_min_mean']    = args.sample_leaf_prob_x_min_mean
    config['sample_leaf_prob_x_lower_bound'] = args.sample_leaf_prob_x_lower_bound
    config['sample_leaf_prob_x_upper_bound'] = args.sample_leaf_prob_x_upper_bound
config['sample_leaf_prob_x_max_std']     = args.sample_leaf_prob_x_max_std
config['sample_leaf_prob_x_min_std']     = args.sample_leaf_prob_x_min_std

config['sampler_num_node_min'] = args.sampler_num_node_min
config['sampler_num_node_max'] = args.sampler_num_node_max

assert config['sample_root_prob_x_upper_bound'] + config['sample_leaf_prob_x_upper_bound'] < 1, f"Expected (sample_root_prob_x_upper_bound + sample_leaf_prob_x_upper_bound < 1), but got {config['sample_root_prob_x_upper_bound']} + {config['sample_leaf_prob_x_upper_bound']} >= 1"

config['skip_func_names'] = [
    'sampler_prototypes_mix',
    'tree_threshold_func',
    'mlp_sampler_modulo',
    'mlp_sampler_power',
    'categorical_sampler_K',
    'sampler_edge_noise_std',
    'sampler_prob',
    'sampler_num_node',
    'sampler_num_dag',
    'rank_sampler_num_classes',
    'rank_sampler_temp',
    'tree_input_scale'
]

# config['sampler_edge_funcs_probability'] = np.array([args.edge_funcs_mlp_p, args.edge_funcs_cate_p, args.edge_funcs_tree_p])  # 只考虑MLP
assert np.sum([args.edge_funcs_mlp_p, args.edge_funcs_cate_p, args.edge_funcs_tree_p, args.edge_funcs_conv1d_p, args.edge_funcs_mlp_2layers_p, args.edge_funcs_reg_tree_p]) == 1, f"Expected (edge_funcs_mlp_p + edge_funcs_cate_p + edge_funcs_tree_p + edge_funcs_conv1d_p + edge_funcs_mlp_2layers_p + edge_funcs_reg_tree_p == 1), but got {args.edge_funcs_mlp_p} + {args.edge_funcs_cate_p} + {args.edge_funcs_tree_p} + {args.edge_funcs_conv1d_p} + {args.edge_funcs_mlp_2layers_p} + {args.edge_funcs_reg_tree_p} != 1"
config['sampler_edge_funcs_probability'] = np.array([args.edge_funcs_mlp_p, args.edge_funcs_cate_p, args.edge_funcs_tree_p, args.edge_funcs_conv1d_p, args.edge_funcs_mlp_2layers_p, args.edge_funcs_reg_tree_p])  

config['sampler_num_dag_node'] = 'uniform'

config['input_warpings_ratio'] = args.input_warpings_ratio
config['input_warping_type'] = args.input_warping_type
config['input_warping_groups'] = args.input_warping_groups
config['input_warping_norm'] = not args.disable_input_warping_norm
config['input_warping_c1_std'] = args.input_warping_c1_std
config['input_warping_c0_std'] = args.input_warping_c0_std

config['save_model_frequency'] = args.save_model_frequency
config['profiler_on'] = args.profiler_on
config['max_resum_times'] = args.max_resum_times

if (args.resume is not None):
    config['resume_weight'] = args.resume
elif not args.disable_auto_resume:
    weight = get_last_ckpt(config['base_path'], config['task_ID'])
    if(weight is not None):
        config['resume_weight'] = weight
    

now = datetime.now()
time_str = now.strftime("%m%d_%H%M")
if host_info not in vid:
    task_name=f"{config['task_ID']}_{time_str}"
else:
    task_name=f"{config['task_ID']}_{vid[host_info]}_{time_str}"
config['task_name'] = task_name

if(args.enable_online_evaluation):
    save_path = os.path.join(config['base_path'], task_name, "results")
    config['evaluate'] = {}
    config['evaluate']['dataset'] = args.dataset
    config['evaluate']['single_eval_pos'] = args.single_eval_pos
    config['evaluate']['num_feats'] = args.num_feats
    config['evaluate']['max_samples'] = args.max_samples
    config['evaluate']['save_each_dataset_result'] = args.save_each_dataset_result
    config['evaluate']['save_path'] = save_path

try:
    assert config['update_num']  % (config['batch_sigle'] * config['GPU_num']) == 0
except AssertionError:
    raise ValueError("config['update_num'] 需要是GPU_num*batch_sigle的整倍数")

# ----- 以下这些参数自动计算 不要手工修改----- 
config['aggregate_k_gradients'] = config['update_num'] // (config['batch_sigle'] * config['GPU_num'])
config['batch_size'] = config['batch_sigle'] * config['aggregate_k_gradients'] 
config['num_steps'] = config['total_dataset'] // config['batch_size'] //config['GPU_num'] * config['batch_sigle']
# --- 以上这些参数自动计算 不要手工修改 ---


# print("config:", config)
# exit(0)

if 'resume_weight' in config and config['resume_weight'] is not None:
    print("====== using resume weight: ", config['resume_weight'], " ======")
else:
    os.makedirs(os.path.join(config['base_path'], task_name), exist_ok=True)
restore_functions(config)
config_sample = evaluate_hypers(config)
train_function(config_sample, 0)
