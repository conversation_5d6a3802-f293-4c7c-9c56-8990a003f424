#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 
# Filename: inference_kaggle.py
# Author: haoyuan
# Created: 2025-04-15
# Description: This is a simple Python script with header information.
# python inference_kaggle.py --model_path 'docker_volume_path/generator_models/gen_703_class6_0414_1801/prior_diff_real_checkpoint_n_0_epoch_730.cpkt' --gpuID 0 --data_dir 'docker_volume_path/datasets/kaggle59/'
import os
import pickle
from xgboost import XGBClassifier
# from tabpfn import TabPFNClassifier
from inference.classifier import TabPFNClassifier
import pandas as pd
from tqdm import tqdm
import numpy as np
from scripts import tabular_metrics
import gc
import torch
import argparse  # 新增参数解析模块


# 在参数解析部分添加新的参数
if __name__ == '__main__':
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='运行TabPFN评估')
    parser.add_argument('--model_path', type=str, required=True,
                      help='输入模型文件路径，例如 /path/to/model.cpkt')
    parser.add_argument('--gpuID', type=int, default=0,
                      help='指定使用的GPU ID，默认为0')
    parser.add_argument('--data_dir', type=str, default='./data',
                      help='指定cc18数据集本地存储目录，默认为./data')
    args = parser.parse_args()

    model_file = args.model_path
    gpu_id = int(args.gpuID) % 8  # 从命令行参数获取GPU ID
    data_root = args.data_dir 

    datasets = [
        'consultant-job-placement-data-2023',
        'guitar-chord-finger-positioning',
        'palmer-penguins-dataset-for-eda',
        'disease-symptoms-and-patient-profile-dataset',
        'loan-status-prediction', 'heart-attack',
        'cirrhosis-patient-survival-prediction', 'job-placement-dataset',
        'earthquake-perception-dataset', 'hcc-survival-data-set',
        'wine-datsets-csv', 'landings-of-meteorite', 'employee-dataset',
        'driver-application-status', 'bangladesh-weather-history',
        'digital-marketing-ecommerce-customer-behavior', 'conflict-Data',
        'tabletpc-priceclassification', 'obesity-prediction',
        'customer-personality-analysis',
        'nfl-combine-performance-data-2009-2019', 'weatherdatabangladesh',
        'customer-churn', 'ad-click-data', 'pashto-translated-corpus',
        'geophone-sensor-dataset', 'loan-application-data',
        'gym-members-exercise-dataset',
        'heart-statlog-cleveland-hungary-final', 'hr-attrition-dataset',
        'hr-analytics', 'loan-approval-systemlas',
        'coffee-machine-peak-frequencies', 'ad-click-prediction-dataset',
        'customer-segmentation', 'click-through-rate-prediction',
        'aug-train', 'spanish-wine-quality-dataset', 'recruitment-scam',
        'earthquake-dataset',
        'predict-conversion-in-digital-marketing-dataset',
        'car-insurance-data', 'heart-failure-prediction-clinical-records',
        'lead-scoring-dataset',
        'amazon-survey-coupon-recommendation-dataset',
        'phishing-website-detector',
        'fatalities-in-the-israeli-palestinian',
        'placement-prediction-dataset', 'fin-env-data',
        'caesarian-section-dataset-for-classification',
        'dataset-aroma-tahu-berfomalin',
        'dataset-predic-terkena-penyakit-paruparu', 'income',
        'machine-learning', 'loan-approval-classification-data',
        'bank-client-attributes-and-marketing-outcomes', 'bankfullcsv',
        'COVID-Flu-Cold-Symptoms',
        'predict-online-gaming-behavior-dataset',
    ]
    houzhui='.pkl'
    file_list = [os.path.join(data_root, f"{dataset}{houzhui}") for dataset in datasets]
    # print(file_list[:4])

    rsts = []
    # model_save = model_file
    model_save = model_file.replace('.cpkt', '-mfp.cpkt')
    model_ours = model_file
    model_default = 'tabpfn-v2-classifier.cpkt'
    try:
        with open(model_ours, 'rb') as f:
            model_m = torch.load(f)
        with open(model_default, 'rb') as f:
            model_d = torch.load(f)
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        print(f"请检查文件是否存在: {model_ours} 和 {model_default}")
        exit(1)
    my_stat = model_m[0]

    new_stat = {}
    for k, v in my_stat.items():
        new_stat[k.replace('module.','')] = v
    for k, v in model_d['state_dict'].items():
        model_d['state_dict'][k]=new_stat[k]

    torch.save(model_d, open(model_save,'wb'))
    # print(model_save, 'model_save, done')

    # 动态生成保存路径
    kaggle_results_path = model_file.replace('.cpkt', '_kaggle_results')
    save_root_dir = model_file.split('/')[-3]
    save_root = kaggle_results_path.replace(save_root_dir, save_root_dir+'_kaggle')
    os.makedirs(save_root, exist_ok=True)
    # 新增检查：如果结果文件存在则提前退出
    result_file = os.path.join(save_root, 'all_rst.csv')
    if os.path.exists(result_file):
        print(f"结果文件已存在，跳过执行: {result_file}")
        exit(0)
    # 跑筛选出的40个数据集
    for file in tqdm(file_list):
    # 跑全部374个数据集
    # for file in tqdm(os.listdir(data_root)):
        if not file.endswith('pkl'):
            continue
        # if 'poker' in file:
        #     continue
        try:
            # 修改为使用 with 语句确保文件关闭
            with open(os.path.join(data_root, file), 'rb') as f:
                data_dict = pickle.load(f, buffers=None)
            
            meta = data_dict['meta_info']
            dataset_name = meta['url'].split('/')[-1]

            # output_filename = os.path.join(save_root, dataset_name + '_pred_pfn.csv')
            # if os.path.exists(output_filename):
            #     print(f'Skipping {file}, output already exists')
            #     continue

            trainX, trainy = data_dict['trainX'], data_dict['trainy']
            
            # 新增类型转换代码
            trainX = np.asarray(trainX, dtype=np.float32)
            trainy = np.asarray(trainy, dtype=np.int64)
            
            # print(trainX.shape, trainy.shape)
            # print(len(trainX), len(np.unique(trainy)))
            # break
            #显存限制
            # data_device = 'cpu' if len(trainX) > 50000 else 'cuda:7'
            data_device = f'cuda:{gpu_id}'
            if len(np.unique(trainy)) > 10 or len(np.unique(trainy)) < 2:
                # print('xxxx', len(np.unique(trainy)))
                continue
            #过滤掉样本数大于50000的数据集
            if len(trainX) > 50000:
                # print('x'*100, len(trainX))
                continue
            testX, testy = data_dict['testX'], data_dict['testy']
            
            # 修改测试数据加载逻辑
            if testX is None or testy is None:
                l = int(len(trainX)*0.5)
                testX = trainX[l:]
                testy = trainy[l:]
                trainX = trainX[:l]
                trainy = trainy[:l]
                
            # 类型转换移动到条件判断之后
            testX = np.asarray(testX, dtype=np.float32)
            testy = np.asarray(testy, dtype=np.int64)
            
            
            rst = {'dataset name':meta['url'].split('/')[-1],
                    'num_data_train':len(trainX),'num_data_test':len(testX),'num_feat':len(trainX[0]), 'num_class':len(np.unique(trainy))}
            rst['domain'] = meta['domain']
            
            # classifier = XGBClassifier(device='cpu')
            classifier = XGBClassifier(device=data_device)
            classifier.fit(trainX, trainy)
            prediction_ = classifier.predict_proba(testX)
            
            roc_xg= tabular_metrics.auc_metric(testy, prediction_)#, tabular_metrics.cross_entropy(testy, prediction_)
            rst['AUC_xgboost'] = float(roc_xg)

            # class_num = prediction_.shape[1]
            # output_df = {'label':testy}
            # for i in range(class_num):
            #     output_df[f'pred_{i}'] = prediction_[:,i]
            # pd.DataFrame(output_df).to_csv(os.path.join(save_root, rst['dataset name']+'_pred_xgboost.csv'), index=False)
            

        except Exception as e:
            print(f"Error processing {file}: {e}")
            # rst['AUC_xgboost'] = -1
            continue
        
        try:
            classifier = TabPFNClassifier(device=data_device, ignore_pretraining_limits=True, model_path=model_save)
            classifier.fit(trainX, trainy)
            prediction_ = classifier.predict_proba(testX)
            class_num = prediction_.shape[1]
            
            roc = tabular_metrics.auc_metric(testy, prediction_)#, tabular_metrics.cross_entropy(testy, prediction_)

            rst['AUC_PFN'] = float(roc)
            
            # output_df = {'label':testy}
            # for i in range(class_num):
            #     output_df[f'pred_{i}'] = prediction_[:,i]
            # # output_df['pred_label'] = np.argmax(prediction_, axis=1)
            # pd.DataFrame(output_df).to_csv(os.path.join(save_root, rst['dataset name']+'_pred_pfn.csv'), index=False)  
            rsts.append(rst)
            # print(rst)  
        except Exception as e:
            print(f"Error processing {file}: {e}")
            # rst['AUC_PFN'] = -1
            # pass
        gc.collect()  # 强制进行垃圾回收
        if 'cuda' in data_device:
            torch.cuda.empty_cache()  # 再次确保显存释放

    rstsdf = pd.DataFrame(rsts)
    rstsdf.to_csv(os.path.join(save_root, 'all_rst.csv'), index=False)
