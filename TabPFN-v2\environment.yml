name: PFN
channels:
  - pytorch
  - nvidia
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/msys2/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/msys2/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/bioconda/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/bioconda/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/menpo/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/menpo/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r/noarch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r/linux-64
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotli-python=1.0.9=py311h6a678d5_7
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2023.08.22=h06a4308_0
  - certifi=2023.11.17=py311h06a4308_0
  - cffi=1.16.0=py311h5eee18b_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - cryptography=41.0.7=py311hdda0065_0
  - cuda-cudart=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.3.101=0
  - cuda-runtime=12.1.0=0
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.13.1=py311h06a4308_0
  - freetype=2.12.1=h4a9f257_0
  - giflib=5.2.1=h5eee18b_3
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py311hc9b5ff0_0
  - gnutls=3.6.15=he1e5248_0
  - idna=3.4=py311h06a4308_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jinja2=3.1.2=py311h06a4308_0
  - jpeg=9e=h5eee18b_1
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libcublas=*********=0
  - libcufft=********=0
  - libcufile=********=0
  - libcurand=**********=0
  - libcusolver=*********=0
  - libcusparse=12.0.2.55=0
  - libdeflate=1.17=h5eee18b_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=12.0.2.50=0
  - libnvjitlink=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp=1.3.2=h11a3e52_0
  - libwebp-base=1.3.2=h5eee18b_0
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_0
  - markupsafe=2.1.1=py311h5eee18b_0
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py311h5eee18b_1
  - mkl_fft=1.3.8=py311h5eee18b_0
  - mkl_random=1.2.4=py311hdb19cb5_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.3.0=py311h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.1=py311h06a4308_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.4.0=h3ad879b_0
  - openssl=3.0.12=h7f8727e_0
  - packaging=24.2=py311h06a4308_0
  - pillow=10.0.1=py311ha6cbd5a_0
  - pip=23.3.1=py311h06a4308_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyopenssl=23.2.0=py311h06a4308_0
  - pysocks=1.7.1=py311h06a4308_0
  - python=3.11.5=h955ad1f_0
  - pytorch=2.1.1=py3.11_cuda12.1_cudnn8.9.2_0
  - pytorch-cuda=12.1=ha16c6d3_5
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.1=py311h5eee18b_0
  - readline=8.2=h5eee18b_0
  - requests=2.31.0=py311h06a4308_0
  - setuptools=68.2.2=py311h06a4308_0
  - sqlite=3.41.2=h5eee18b_0
  - sympy=1.12=py311h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=2.1.1=py311_cu121
  - torchtriton=2.1.0=py311
  - torchvision=0.16.1=py311_cu121
  - urllib3=1.26.18=py311h06a4308_0
  - wheel=0.41.2=py311h06a4308_0
  - xz=5.4.5=h5eee18b_0
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.5=hc292b87_0
  - pip:
      - aiohttp==3.8.6
      - aiosignal==1.3.1
      - annotated-types==0.6.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.8.0
      - appdirs==1.4.4
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - argparse==1.4.0
      - asttokens==2.4.1
      - async-timeout==4.0.3
      - asyncer==0.0.8
      - attrs==23.1.0
      - autogen==0.7.3
      - automat==22.10.0
      - autopep8==2.0.4
      - axial-positional-embedding==0.2.1
      - beautifulsoup4==4.12.2
      - buildtools==1.0.6
      - catboost==1.2.7
      - click==8.1.7
      - cloudpickle==3.1.1
      - comm==0.2.2
      - configspace==1.2.1
      - constantly==23.10.4
      - contourpy==1.1.1
      - cycler==0.11.0
      - cython==3.0.3
      - debugpy==1.8.12
      - decorator==5.1.1
      - deprecated==1.2.14
      - diskcache==5.6.3
      - distro==1.9.0
      - dnspython==2.4.2
      - docker==7.1.0
      - docker-pycreds==0.4.0
      - docopt==0.6.2
      - docutils==0.20.1
      - einops==0.6.1
      - executing==2.0.1
      - fast-depends==2.4.12
      - flaml==2.1.1
      - fonttools==4.42.1
      - frozenlist==1.4.0
      - fsspec==2023.10.0
      - furl==2.1.3
      - future==1.0.0
      - gdown==5.0.0
      - gitdb==4.0.10
      - gitpython==3.1.37
      - gluonts==0.14.2
      - gpytorch==1.14
      - graphviz==0.20.3
      - greenlet==3.0.3
      - h11==0.14.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.20.2
      - hydra-core==1.3.2
      - hyperlink==21.0.0
      - hyperopt==0.2.7
      - importlib-metadata==6.8.0
      - incremental==22.10.0
      - ipdb==0.13.13
      - ipykernel==6.29.5
      - ipython==8.21.0
      - jaraco-classes==3.3.0
      - jaxtyping==0.2.38
      - jedi==0.19.1
      - jeepney==0.8.0
      - jiter==0.8.2
      - joblib==1.3.2
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - keyring==24.2.0
      - kiwisolver==1.4.5
      - liac-arff==2.5.0
      - lightgbm==4.6.0
      - lightning==2.1.2
      - lightning-utilities==0.10.0
      - linear-operator==0.6
      - local-attention==1.4.4
      - markdown-it-py==3.0.0
      - matplotlib==3.7.0
      - matplotlib-inline==0.1.6
      - mdurl==0.1.2
      - minio==7.2.15
      - more-itertools==10.1.0
      - multidict==6.0.4
      - narwhals==1.26.0
      - nest-asyncio==1.6.0
      - nh3==0.2.14
      - ninja==********
      - numpy==1.23.5
      - nvidia-ml-py==12.570.86
      - nvidia-nccl-cu12==2.25.1
      - omegaconf==2.3.0
      - openai==1.61.1
      - openml==0.15.1
      - orderedmultidict==1.0.1
      - pandas==1.5.3
      - parso==0.8.3
      - pathtools==0.1.2
      - patool==1.12
      - pexpect==4.9.0
      - pkginfo==1.9.6
      - platformdirs==4.3.6
      - plotly==6.0.0
      - product-key-memory==0.1.10
      - prompt-toolkit==3.0.43
      - protobuf==4.24.3
      - psutil==5.9.5
      - ptyprocess==0.7.0
      - pure-eval==0.2.2
      - py4j==0.10.9.9
      - pyarrow==19.0.0
      - pyautogen==0.7.3
      - pycodestyle==2.11.1
      - pycryptodome==3.21.0
      - pydantic==2.6.1
      - pydantic-core==2.16.2
      - pygments==2.16.1
      - pymongo==4.5.0
      - pynvml==12.0.0
      - pyparsing==3.1.1
      - python-dateutil==2.8.2
      - python-dotenv==1.0.0
      - pytorch-lightning==2.1.2
      - pytz==2023.3.post1
      - pyzmq==26.2.1
      - readme-renderer==42.0
      - redo==2.0.4
      - reformer-pytorch==1.4.4
      - regex==2023.12.25
      - requests-toolbelt==1.0.0
      - rfc3986==2.0.0
      - rich==13.6.0
      - safetensors==0.4.0
      - schedulefree==1.4
      - scikit-learn==1.2.2
      - scipy==1.10.1
      - seaborn==0.13.2
      - secretstorage==3.3.3
      - sentry-sdk==1.31.0
      - setproctitle==1.3.2
      - simplejson==3.19.2
      - six==1.16.0
      - sktime==0.16.1
      - smmap==5.0.1
      - sniffio==1.3.1
      - soupsieve==2.5
      - sqlalchemy==2.0.27
      - stack-data==0.6.3
      - termcolor==2.3.0
      - threadpoolctl==3.2.0
      - tiktoken==0.8.0
      - timm==0.9.8
      - tokenizers==0.15.0
      - toolz==0.12.0
      - torchmetrics==1.2.0
      - tornado==6.4.2
      - tqdm==4.64.1
      - traitlets==5.14.1
      - transformers==4.36.2
      - twine==4.0.2
      - twisted==23.10.0
      - typing-extensions==4.12.2
      - tzdata==2023.3
      - wadler-lindig==0.1.3
      - wandb==0.16.6
      - wcwidth==0.2.13
      - websockets==14.2
      - wrapt==1.15.0
      - xgboost==2.1.4
      - xmltodict==0.14.2
      - yarl==1.9.2
      - zipp==3.17.0
      - zope-interface==6.2
