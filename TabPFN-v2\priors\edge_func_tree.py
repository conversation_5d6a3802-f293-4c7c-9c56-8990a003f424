import os
import torch
import torch.nn as nn
import numpy as np
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
# from cuml.ensemble import RandomForestClassifier
# import cudf
from .activation_func import get_funcs
# from activation_func import get_funcs
import time
import random
import copy
from numba import njit, obj<PERSON><PERSON>, prange
from numba.extending import register_jitable
from types import FunctionType
from numba.typed import List
from numba import njit, int32, float32
import numba
from numpy.lib.stride_tricks import sliding_window_view

from utils import high_precision_trace
# import multiprocessing
# from joblib import Parallel, delayed

# numba.config.NUMBA_NUM_THREADS = 4
# numba.set_num_threads(4)

# Edge function: Gaussian Noise
def GaussianNoise(x: torch.Tensor, hyperparameters: dict) -> torch.Tensor:
    """
    Args:
        x (torch.Tensor): (seq_len, num_features_used)

    Returns:
        noise (torch.Tensor): (seq_len, num_features_used)
    """
    # std = abs(hyperparameters['sampler_edge_noise_std']())
    std = abs(hyperparameters['noise_std'])
    output = torch.normal(0, std, size=x.shape, device=x.device)
    return output


# Edge function: MLP
def MLP(x: torch.Tensor, hyperparameters: dict, act_func_name: str='') -> torch.Tensor:
    """
    Args:
        x (torch.Tesnor): (seq_len, num_features_used) or (seq_len, num_input)

    Returns:
        output (torch.Tensor): (seq_len, num_features_used * scale)
    """
    num_input = x.shape[-1]
    with torch.no_grad():
        num_features_used = hyperparameters['num_features_used']
        sampler_power = hyperparameters['mlp_sampler_power']
        sampler_modulo = hyperparameters['mlp_sampler_modulo']
        scale = hyperparameters['edge_output_dim_scale']
        # Xavier init
        w = nn.init.xavier_uniform_(torch.randn(num_input, int(num_features_used * scale), device=x.device), gain=1.0)
        b = torch.zeros(int(num_features_used * scale), device=x.device)
        activation = get_funcs(
            sampler_power, 
            sampler_modulo, 
            act_func_name=act_func_name, 
            v1_implementation=hyperparameters['using_v1_activations'],
            rank_operation = hyperparameters['rank_operation'],
            rank_sampler_num_classes = hyperparameters['rank_sampler_num_classes'],
            rank_balanced = hyperparameters['rank_balanced'],
            class_weights = None, 
            rank_sampler_temp = hyperparameters['rank_sampler_temp']
        )[0]
        activation = activation.to(x.device)
        output = activation(torch.matmul(x, w) + b) 
    return output, None


# Edge function: Categorical Discretization
def Categorical(x: torch.Tensor, hyperparameters: dict) -> torch.Tensor:
    """
    Args:
        x (torch.Tesnor): (seq_len, num_features_used)

    Returns:
        output (torch.Tensor): (seq_len, num_features_used * scale)
    """
    with torch.no_grad():
        K = max(2, int(hyperparameters['categorical_sampler_K']()))  # K由gamma分布采样获得，最小为2
        num_features_used = hyperparameters['num_features_used']
        scale = hyperparameters['edge_output_dim_scale']
        continuous2discrete = torch.rand(1, K, x.shape[-1], device=x.device)
        discrete2continuous = torch.rand(K, int(num_features_used * scale), device=x.device)
        x_temp = x.unsqueeze(1)  # (seq_len, 1, num_features_used) or (seq_len, 1, unknown)
        # print(x.device, continuous2discrete.device)
        # 寻找最近cluster的index (离散化)
        dist = torch.abs(x_temp - continuous2discrete)  # (seq_len, 1, num_features_used) or (seq_len, 1, unknown)
        dist_manhattan = dist.sum(dim=2)  # (seq_len, K)
        index = torch.argmin(dist_manhattan, dim=1) # (seq_len)
        
        # 从index中找到对应的cluster (连续化)
        output = discrete2continuous[index,:] 
        return output, None
    

# Edge function: MLP_2layers
def MLP_2layers(x: torch.Tensor, hyperparameters: dict, act_func_name: str='') -> torch.Tensor:
    """
    Args:
        x (torch.Tesnor): (seq_len, num_features_used) or (seq_len, num_input)

    Returns:
        output (torch.Tensor): (seq_len, num_features_used * scale)
    """
    num_input = x.shape[-1]
    with torch.no_grad():
        num_features_used = hyperparameters['num_features_used']
        sampler_power = hyperparameters['mlp_sampler_power']
        sampler_modulo = hyperparameters['mlp_sampler_modulo']
        scale = hyperparameters['edge_output_dim_scale']
        # Xavier init
        # MLP 1
        w1 = nn.init.xavier_uniform_(torch.randn(num_input, int(num_features_used * scale), device=x.device), gain=1.0)
        b1 = torch.zeros(int(num_features_used * scale), device=x.device)
        activation1 = get_funcs(
            sampler_power, 
            sampler_modulo, 
            act_func_name=act_func_name, 
            v1_implementation=hyperparameters['using_v1_activations'],
            rank_operation = hyperparameters['rank_operation'],
            rank_sampler_num_classes = hyperparameters['rank_sampler_num_classes'],
            rank_balanced = hyperparameters['rank_balanced'],
            class_weights = None, 
            rank_sampler_temp = hyperparameters['rank_sampler_temp']
        )[0]
        activation1 = activation1.to(x.device)
        
        # MLP 2
        w2 = nn.init.xavier_uniform_(torch.randn(int(num_features_used * scale), int(num_features_used * scale), device=x.device), gain=1.0)
        b2 = torch.zeros(int(num_features_used * scale), device=x.device)
        
        activation2 = get_funcs(
            sampler_power, 
            sampler_modulo, 
            act_func_name=act_func_name, 
            v1_implementation=hyperparameters['using_v1_activations'],
            rank_operation = hyperparameters['rank_operation'],
            rank_sampler_num_classes = hyperparameters['rank_sampler_num_classes'],
            rank_balanced = hyperparameters['rank_balanced'],
            class_weights = None, 
            rank_sampler_temp = hyperparameters['rank_sampler_temp']
        )[0]
        activation2 = activation2.to(x.device)
        
        output = activation1(torch.matmul(x, w1) + b1) 
        output = activation2(torch.matmul(output, w2) + b2) 
        
    return output, None


# Edge function: Conv1D
def Conv1D(x: torch.Tensor, hyperparameters: dict, act_func_name: str='') -> torch.Tensor:
    """
    Args:
        x (torch.Tesnor): (seq_len, num_features_used) or (seq_len, num_input)

    Returns:
        output (torch.Tensor): (seq_len, num_features_used * scale)
    """
    # t1 = time.time()
    num_input = x.shape[-1]
    with torch.no_grad():
        num_features_used = hyperparameters['num_features_used']
        scale = hyperparameters['edge_output_dim_scale']
        kernel_size = hyperparameters['conv_kernel_size']
        sampler_power = hyperparameters['mlp_sampler_power']
        sampler_modulo = hyperparameters['mlp_sampler_modulo']
        conv = torch.nn.Conv1d(num_input, 
                            int(num_features_used * scale), 
                            kernel_size=kernel_size,
                            padding=kernel_size//2,
                            padding_mode='replicate', 
                            device=x.device)
        
        activation = get_funcs(
            sampler_power, 
            sampler_modulo, 
            act_func_name=act_func_name, 
            v1_implementation=hyperparameters['using_v1_activations'],
            rank_operation = hyperparameters['rank_operation'],
            rank_sampler_num_classes = hyperparameters['rank_sampler_num_classes'],
            rank_balanced = hyperparameters['rank_balanced'],
            class_weights = None, 
            rank_sampler_temp = hyperparameters['rank_sampler_temp']
        )[0]
        activation = activation.to(x.device)
        
        output = conv(x.transpose(0, 1).unsqueeze(0))
        output = activation(output.squeeze(0).transpose(0, 1))

    # t2 = time.time()
    # print(f"Conv1D time:  {(t2 - t1)*1000}ms, x.shape: {x.shape}, output.shape: {output.shape}")
    return output, None

@njit(nogil=True, parallel=True)
def conv1d(x: np.ndarray, output_dim: int, kernel_size: int, conv_along_seq: float) -> np.ndarray:
    H, W = x.shape

    if np.random.random() < conv_along_seq:
        # 沿 seq_len 方向卷积
        kernel = np.random.randn(output_dim, kernel_size, W)
        pad_width = kernel_size // 2
        x_padded = np.zeros((H + 2 * pad_width, W))

        # 边缘填充
        for j in range(W):
            for i in range(pad_width):
                x_padded[i, j] = x[0, j]
                x_padded[H + pad_width + i, j] = x[-1, j]
        for i in range(H):
            for j in range(W):
                x_padded[i + pad_width, j] = x[i, j]

        output = np.zeros((H, output_dim))
        for t in range(H):  # 每个时间步
            for oc in range(output_dim):
                for k in range(kernel_size):
                    for w in range(W):
                        output[t, oc] += x_padded[t + k, w] * kernel[oc, k, w]

    else:
        # 沿 feature 方向卷积
        kernel = np.random.randn(H, H, kernel_size)
        pad_width = kernel_size // 2
        x_padded = np.zeros((H, W + 2 * pad_width))

        # 边缘填充
        for i in range(H):
            for j in range(pad_width):
                x_padded[i, j] = x[i, 0]
                x_padded[i, W + pad_width + j] = x[i, -1]
            for j in range(W):
                x_padded[i, j + pad_width] = x[i, j]

        output = np.zeros((W, H))
        for w in prange(W):
            for h_out in range(H):
                for h_in in range(H):
                    for k in range(kernel_size):
                        output[w, h_out] += x_padded[h_in, w + k] * kernel[h_out, h_in, k]

        output = output.T  # shape (H, W)
        idx = np.random.permutation(W)
        output = output[:, idx[:output_dim]]

    return output

def Conv1D_old(x: torch.Tensor, hyperparameters: dict, act_func_name: str='') -> torch.Tensor:
    """
    Args:
        x (torch.Tesnor): (seq_len, num_features_used) or (seq_len, num_input)

    Returns:
        output (torch.Tensor): (seq_len, num_features_used * scale)
    """
    sampler_power = hyperparameters['mlp_sampler_power']
    sampler_modulo = hyperparameters['mlp_sampler_modulo']
    num_features_used = hyperparameters['num_features_used']
    scale = hyperparameters['edge_output_dim_scale']
    kernel_size = hyperparameters['conv_kernel_size']
    conv_along_seq = hyperparameters['conv_along_seq']
    
    activation = get_funcs(
        sampler_power, 
        sampler_modulo, 
        act_func_name=act_func_name, 
        v1_implementation=hyperparameters['using_v1_activations'],
        rank_operation = hyperparameters['rank_operation'],
        rank_sampler_num_classes = hyperparameters['rank_sampler_num_classes'],
        rank_balanced = hyperparameters['rank_balanced'],
        class_weights = None, 
        rank_sampler_temp = hyperparameters['rank_sampler_temp']
    )[0]
    activation = activation.to(x.device)
    output_dim = int(num_features_used*scale)
    t1 = time.time()
    output = conv1d(x.cpu().numpy(), output_dim, kernel_size, conv_along_seq)
    t2 = time.time()
    print(f"con1d cost : {1000*(t2-t1)}ms, x.shape: {x.shape}, output.shape: {output.shape}")
    output = torch.from_numpy(output)
    output = output.float()
    # print(output.dtype)
    
    with torch.no_grad():
        output = activation(output)   
    return output, None


class DecisionTreeClassifier_numba:
    def __init__(self, max_depth, min_leaf_size, leaf_prob, num_classes, get_threshold_func):
        self.max_depth = max_depth
        self.min_leaf_size = min_leaf_size
        self.leaf_prob = leaf_prob
        self.num_classes = num_classes
        self.get_threshold_func = get_threshold_func
        self.root = None
        assert self.max_depth > 0, f"max_depth must be greater than 0, but got {self.max_depth}"
        assert self.min_leaf_size <= 2**max_depth, f"min_leaf_size must be less than 2**max_depth, but got {self.min_leaf_size}, depth: {self.max_depth}"
        assert self.num_classes > 0, f"num_classes must be greater than 0, but got {self.num_classes}"
        assert callable(self.get_threshold_func), f"get_threshold_func must be callable, but got {self.get_threshold_func}"

    def fit(self, num_features_used):
        leaf_num = 0
        leaf_label = list(range(self.num_classes))
        random.shuffle(leaf_label)
        def get_new_label():
            # nonlocal leaf_num
            if(leaf_num < self.min_leaf_size):
                return leaf_label[leaf_num]
            else:
                return random.randint(0, self.num_classes-1)
            
        class DecisionTreeNode:
            def __init__(self, depth, max_depth, leaf_prob, num_classes, num_features_used, get_threshold_func, parent_threshold_range):
                if (depth == max_depth or random.random() < leaf_prob) and depth > 0:
                    self.is_leaf = True
                    self.class_label = get_new_label()
                    nonlocal leaf_num
                    leaf_num += 1
                else:
                    self.is_leaf = False
                    self.feature_index = random.randint(0, num_features_used-1)
                    self.threshold = get_threshold_func(parent_threshold_range[self.feature_index][0], parent_threshold_range[self.feature_index][1])
                    left_threshold_range = [lst[:] for lst in parent_threshold_range]  # 浅拷贝外层列表
                    left_threshold_range[self.feature_index] = [parent_threshold_range[self.feature_index][0], self.threshold]
        
                    right_threshold_range = parent_threshold_range
                    right_threshold_range[self.feature_index][0] = self.threshold
                    
                    self.left_child = DecisionTreeNode(
                        depth + 1, max_depth, leaf_prob, num_classes,
                        num_features_used, get_threshold_func, left_threshold_range
                    )
                    self.right_child = DecisionTreeNode(
                        depth + 1, max_depth, leaf_prob, num_classes,
                        num_features_used, get_threshold_func, right_threshold_range
                    )

        threshold_range = [[-1, 1] for _ in range(num_features_used)]
        while(leaf_num < self.min_leaf_size):
            leaf_num = 0
            self.root = DecisionTreeNode(
                0, self.max_depth, self.leaf_prob, self.num_classes,
                num_features_used, self.get_threshold_func, threshold_range
            )
    
    def predict2(self, x:torch.Tensor):
        x_ = x.numpy()  # 转换为 NumPy 数组 (seq_len, num_features_used)
        batch_size = x_.shape[0]
        
        # 维护当前所有样本所在的节点
        current_nodes = np.array([self.root] * batch_size, dtype=object)
        
        # 记录哪些样本还在分类过程中
        active_mask = np.ones(batch_size, dtype=bool)

        while np.any(active_mask):  # 只要还有样本未到达叶子节点
            active_indices = np.where(active_mask)[0]  # 取出未完成样本的索引
            for i in active_indices:
                if not hasattr(current_nodes[i], 'feature_index'):
                    print("eror")
            features = np.array([current_nodes[i].feature_index for i in active_indices])  # 当前所有样本对应的特征索引
            thresholds = np.array([current_nodes[i].threshold for i in active_indices])  # 当前所有样本对应的阈值
            samples = x_[active_indices, features]  # 取出所有样本对应特征值
            
            # 进行批量判断
            left_mask = samples < thresholds  # 生成左右子树的掩码
            # right_mask = ~left_mask
            
            # 更新当前节点
            for i, idx in enumerate(active_indices):
                current_nodes[idx] = current_nodes[idx].left_child if left_mask[i] else current_nodes[idx].right_child
                
                # 如果到达叶子节点，则将 active_mask 设为 False
                if current_nodes[idx].is_leaf:
                    active_mask[idx] = False

        # 提取最终分类结果
        y_pred = np.array([node.class_label for node in current_nodes])
        y_pred = torch.tensor(y_pred)
        return y_pred
    
    def _convert_tree_to_array(self):
        """将树结构转换为Numba可处理的数组格式"""
        nodes = []
        self._traverse_tree(self.root, nodes)
        return np.array(nodes, dtype=np.float32)

    def _traverse_tree(self, node, nodes):
        """递归遍历树生成数组结构，同时记录左右子节点序号"""
        current_idx = len(nodes)  # 当前节点的索引
        
        if node.is_leaf:
            nodes.append([1, 0, 0, node.class_label, -1, -1])  # [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
        else:
            # 先添加当前节点，留空左右子节点索引
            nodes.append([0, node.feature_index, node.threshold, 0, -1, -1])
            
            # 递归处理左子树
            left_child_idx = len(nodes)
            self._traverse_tree(node.left_child, nodes)
            
            # 递归处理右子树
            right_child_idx = len(nodes)
            self._traverse_tree(node.right_child, nodes)
            
            # 更新当前节点的左右子节点索引
            nodes[current_idx][4] = left_child_idx
            nodes[current_idx][5] = right_child_idx

    def predict(self, x: torch.Tensor):
        x_ = x.numpy().astype(np.float32)  # 转换为float32类型加速处理
        batch_size = x_.shape[0]
        
        # 将树结构转换为Numba友好格式
        node_info = self._convert_tree_to_array()
        
        # 使用Numba加速预测
        y_pred_np = self._numba_predict(x_, node_info, batch_size)
        return torch.from_numpy(y_pred_np)
        
    @staticmethod
    @njit(nogil=True, parallel=True)
    def _numba_predict(x, node_info, batch_size):
        """Numba加速的预测核心"""
        y_pred = np.empty(batch_size, dtype=np.int32)
        for i in prange(batch_size):
            current_idx = 0  # 根节点总是第一个
            while True:
                node = node_info[current_idx]
                # node: [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
                if node[0] == 1:  # 叶子节点
                    y_pred[i] = node[3]
                    break
                
                feature_idx = int(node[1])
                
                if x[i, feature_idx] < node[2]:
                    current_idx += 1  # 左子节点总是下一个
                else:
                    # 计算右子节点索引：当前节点之后 + 左子树节点数
                    current_idx = int(node[5])
        return y_pred

@register_jitable
def DecisionTreeClassifier(x, max_depth:int, num_classes:int, leaf_prob:float, seed:int, get_threshold_func:FunctionType):
    # leaf_num = 0
    np.random.seed(seed)  # 初始化线程私有 RNG
    leaf_label = np.empty(2**max_depth, dtype=np.int32)
    perm = np.random.permutation(num_classes)
    leaf_label[:num_classes] = perm
    for i in range(num_classes, 2**max_depth):
        leaf_label[i] = np.random.randint(0, num_classes)

    num_features_used = x.shape[1]
    leaf_num = 0
    
    MAX_NODES = 2 ** (max_depth + 1) - 1
    
    while leaf_num < num_classes:
        # 非递归构建树
        node_list = np.full((MAX_NODES, 6), -1.0, dtype=np.float32)
        
        stack_depth = np.zeros(MAX_NODES, dtype=np.int32)
        stack_idx = np.zeros(MAX_NODES, dtype=np.int32)
        stack_range = np.zeros((MAX_NODES, num_features_used, 2), dtype=np.float32)
        stack_top = 0
        
        threshold_range = np.zeros((num_features_used, 2), dtype=np.float32)
        threshold_range[:, 0] = -1.0
        threshold_range[:, 1] = 1.0
        
        stack_depth[stack_top] = 0
        stack_idx[stack_top] = 0
        stack_range[stack_top] = threshold_range
        stack_top += 1
        
        # node_list = List()   # [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
        node_list_count = 1
        leaf_num = 0
        
        while stack_top > 0:
            # depth, parent_range, idx = stack.pop()
            stack_top -= 1
            depth = stack_depth[stack_top]
            idx = stack_idx[stack_top]
            parent_range = stack_range[stack_top]
            
            if (depth == max_depth or np.random.rand() < leaf_prob) and depth > 0:
                # 叶子节点
                # node = np.zeros(6, dtype=np.float32)
                # node[0] = 1.0  # is_leaf
                # node[3] = leaf_label[leaf_num]  # class_label
                # leaf_num += 1
                # node_list[idx] = node
                
                node_list[idx][0] = 1.0  # is_leaf
                node_list[idx][3] = leaf_label[leaf_num]  # class_label
                leaf_num += 1

            else:
                # 非叶子节点
                feature_idx  = np.random.randint(0, num_features_used)
                threshold = get_threshold_func(
                    parent_range[feature_idx ][0],
                    parent_range[feature_idx ][1]
                )
                
                
                left_idx = node_list_count
                right_idx = node_list_count + 1
                node_list_count += 2
                
                # 创建左右子节点的阈值范围
                left_range = parent_range.copy()
                left_range[feature_idx, 1] = threshold

                right_range = parent_range.copy()
                right_range[feature_idx, 0] = threshold
                
                # 创建左右子节点
                # node_list.append(np.zeros(6, dtype=np.float32))
                # node_list.append(np.zeros(6, dtype=np.float32))
                
                node_list[idx][0] = 0.0  # is_leaf
                node_list[idx][1] = feature_idx
                node_list[idx][2] = threshold
                node_list[idx][4] = left_idx
                node_list[idx][5] = right_idx

                # stack.append((depth + 1, right_range, right_idx))
                # stack.append((depth + 1, left_range, left_idx))
                stack_depth[stack_top] = depth + 1
                stack_idx[stack_top] = right_idx
                stack_range[stack_top] = right_range
                stack_top += 1
                
                stack_depth[stack_top] = depth + 1
                stack_idx[stack_top] = left_idx
                stack_range[stack_top] = left_range
                stack_top += 1
                
    # node_array = np.zeros((len(node_list), 6), dtype=np.float32)
    # for i in range(len(node_list)):
    #     node_array[i, :] = node_list[i]
    
    outputs = np.empty((x.shape[0]), dtype=np.int32)
    for i in range(x.shape[0]):
        current_idx = 0  # 根节点总是第一个
        while True:
            node = node_list[current_idx]
            # node: [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
            if node[0] == 1:  # 叶子节点
                outputs[i] = node[3]
                break
            
            feature_idx = int(node[1])
            current_idx = int(node[5] if x[i, feature_idx] >= node[2] else node[4])
    
    return node_list, outputs

@register_jitable
def numba_get_threshold_func_beta1(a, b):
    return np.random.beta(3, 3) * (b - a) + a

@register_jitable
def numba_get_threshold_func_beta2(a, b):
    return np.random.beta(1.5, 3) * (b - a) + a

@register_jitable
def numba_get_threshold_func_beta3(a, b):
    return np.random.beta(5, 5) * (b - a) + a

@register_jitable
def numba_get_threshold_func_beta4(a, b):
    return np.random.beta(1.8, 8) * (b - a) + a

@register_jitable
def numba_get_threshold_func_uniform(a, b):
    return np.random.random() * (b - a) + a

@njit(nogil=True, parallel=True)
def multi_tree(x, output_dim:int, max_depth:int, num_classes:int, leaf_prob:float, sample_mode:int, seed:int):
    MAX_NODES = 2 ** (max_depth + 1) - 1
    results = np.zeros((x.shape[0], output_dim), np.float32)
    trees = np.zeros((output_dim, MAX_NODES, 6), np.float32)
    if(sample_mode == 1):
        for i in prange(output_dim):
            seed_i = seed + i * 9973
            trees[i,:,:], results[:,i] = DecisionTreeClassifier(x, max_depth, num_classes, leaf_prob, seed_i, numba_get_threshold_func_beta1)
    elif(sample_mode == 2):
        for i in prange(output_dim):
            seed_i = seed + i * 9973
            trees[i,:,:], results[:,i] = DecisionTreeClassifier(x, max_depth, num_classes, leaf_prob, seed_i, numba_get_threshold_func_beta2)
    elif(sample_mode == 3):
        for i in prange(output_dim):
            seed_i = seed + i * 9973
            trees[i,:,:], results[:,i] = DecisionTreeClassifier(x, max_depth, num_classes, leaf_prob, seed_i, numba_get_threshold_func_beta3)
    elif(sample_mode == 4):
        for i in prange(output_dim):
            seed_i = seed + i * 9973
            trees[i,:,:], results[:,i] = DecisionTreeClassifier(x, max_depth, num_classes, leaf_prob, seed_i, numba_get_threshold_func_beta4)
    elif(sample_mode == 0):
        for i in prange(output_dim):
            seed_i = seed + i * 9973
            trees[i,:,:], results[:,i] = DecisionTreeClassifier(x, max_depth, num_classes, leaf_prob, seed_i, numba_get_threshold_func_uniform)
    else:
        raise ValueError('sample_mode must be [0, 1, 2, 3, 4]')

    return results, trees

@njit(nogil=True, parallel=True)
def decision_tree_numba_predict(x, node_infos):
    """Numba加速的预测核心"""
    batch_size = x.shape[0]
    num_trees = len(node_infos)
    outputs = np.empty((num_trees, batch_size), dtype=np.int32)
    for tree_idx in prange(num_trees):
        node_info = node_infos[tree_idx]
        for i in range(batch_size):
            current_idx = 0  # 根节点总是第一个
            while True:
                node = node_info[current_idx]
                # node: [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
                if node[0] == 1:  # 叶子节点
                    outputs[tree_idx, i] = node[3]
                    break
                
                feature_idx = int(node[1])
                
                current_idx = int(node[5] if x[i, feature_idx] >= node[2] else node[4])
    return outputs


def DecesionTree(x: torch.Tensor, hyperparameters: dict) -> torch.Tensor:
    """
    Args:
        x (torch.Tesnor): (seq_len, num_features_used)
    Returns:
        output (torch.Tensor): (seq_len, num_features_used)
    """
    max_depth = hyperparameters['tree_max_depth']
    num_classes = random.randint(2, hyperparameters['tree_max_num_classes'])
    # num_classes = hyperparameters['tree_max_num_classes']
    leaf_prob = hyperparameters['tree_leaf_prob']
    # get_threshold_func = hyperparameters['tree_threshold_func']
    # num_feature_used = random.randint(2, hyperparameters['num_features_used'])
    output_dim = int(hyperparameters['edge_output_dim_scale'] * hyperparameters['num_features_used'])

    base_seed = int(time.time() * 1e6) % (2**32 - 1)
    output, trees = multi_tree(x.cpu().numpy(), output_dim, max_depth, num_classes, leaf_prob, hyperparameters['decision_tree_threshold_mode'], base_seed)
    output = torch.as_tensor(output, device=x.device).float()

    if hyperparameters['tree_output_norm']:
        eps = 1e-6
        maxima = num_classes - 1
        minima = 0
        output = (output - minima) / (maxima - minima + eps)
        output = (output - 0.5) * 2
    
    # print("==== tree ====")
    return output, trees

# 推理过程
# @njit(nogil=True, parallel=True)
@njit
def DecisionTreeRegressionPredict(x_:np.array, node_list: np.array, leaf_values: np.array, input_weight: float):
    # n, f = x_.shape
    seq_len, feat_num = x_.shape
    output = np.zeros((seq_len, feat_num), dtype=np.float32)
    for i in range(seq_len):
        node = 0
        while True:
            cur_node = node_list[node]
            if int(cur_node[0]) == 1:
                output[i] = leaf_values[int(cur_node[3])] * (1-input_weight) + x_[i] * input_weight
                # output[i] = leaf_values[int(cur_node[3])] 
                break
            node = int(cur_node[4]) if x_[i, int(cur_node[1])] <= cur_node[2] else int(cur_node[5])
    return output

def DecisionTreeRegression(x: torch.tensor, hyperparameters: dict, base_seed = None) -> torch.tensor:
    max_depth = hyperparameters['reg_tree_max_depth']
    leaf_prob = hyperparameters['reg_tree_leaf_prob']
    # get_threshold_func = hyperparameters['reg_tree_threshold_func']
    # input_weight = hyperparameters['reg_tree_input_weight']
    if callable(hyperparameters['reg_tree_input_weight']):
        input_weight = hyperparameters['reg_tree_input_weight']()
    else:
        input_weight = hyperparameters['reg_tree_input_weight']
    if base_seed is None:
        base_seed = int(time.time() * 1e6) % (2**32 - 1)
    t_fucn_name= hyperparameters['reg_tree_threshold_mode']
    if t_fucn_name == 'beta_3_3':
        get_threshold_func = numba_get_threshold_func_beta1
    elif t_fucn_name == 'beta_1p5_3':
        get_threshold_func = numba_get_threshold_func_beta2
    elif t_fucn_name == 'beta_5_5':
        get_threshold_func = numba_get_threshold_func_beta3
    elif t_fucn_name == 'beta_1p8_8':
        get_threshold_func = numba_get_threshold_func_beta4
    elif t_fucn_name == 'uniform':
        get_threshold_func = numba_get_threshold_func_uniform
    else:
        raise ValueError('reg_tree_threshold_mode must be [beta_3_3, beta_1p5_3, beta_5_5, beta_1p8_8, uniform]')
    
    # t1 = time.time()
    seq_len, feat_num = x.shape
    max_nodes = 2 ** (max_depth + 1) - 1  # 最大可能的节点数量（满二叉树）

    # [is_leaf, feature_idx, threshold, value_index, left_child_idx, right_child_idx]
    node_list = np.full((max_nodes, 6), -1, dtype=np.float32)
    leaf_values = np.zeros((max_nodes, feat_num), dtype=np.float32)

    # 树构建过程
    queue = [(0, 0, np.arange(seq_len))]  # (node_index, depth, sample_indices)
    rng = np.random.default_rng(base_seed)
    next_free = 1  # 下一个可用节点索引
    
    stack_range = np.zeros((max_nodes, feat_num, 2), dtype=np.float32)
    threshold_range = np.zeros((feat_num, 2), dtype=np.float32)
    threshold_range[:, 0] = -1.0
    threshold_range[:, 1] = 1.0
    stack_range[0] = threshold_range
    # stack_top = 1

    x_ = x.cpu().numpy()
    
    # t2 = time.time()
    while queue:
        node_idx, depth, sample_idx = queue.pop(0)
        if depth > 0 and (depth >= max_depth or rng.random() < leaf_prob or len(sample_idx) <= 1):
            node_list[node_idx][0] = 1.0
            node_list[node_idx][3] = node_idx
            leaf_values[node_idx] = np.mean(x_[sample_idx], axis=0)
            continue

        parent_range = stack_range[node_idx]
        feat_idx = rng.integers(0, feat_num)
        feat_values = x_[sample_idx, feat_idx]
        thresh = get_threshold_func(parent_range[feat_idx][0], parent_range[feat_idx][1])
        mask = feat_values <= thresh

        if np.all(mask) or not np.any(mask):
            node_list[node_idx][0] = 1.0
            node_list[node_idx][3] = node_idx
            leaf_values[node_idx] = np.mean(x_[sample_idx], axis=0)
            continue

        # 左右子树分配索引
        li, ri = next_free, next_free + 1
        next_free += 2
        
        left_range = parent_range.copy()
        left_range[feat_idx, 1] = thresh
        right_range = parent_range.copy()
        right_range[feat_idx, 0] = thresh
        stack_range[li] = left_range
        stack_range[ri] = right_range
                
        node_list[node_idx][1] = feat_idx
        node_list[node_idx][2] = thresh
        node_list[node_idx][4] = li
        node_list[node_idx][5] = ri

        queue.append((li, depth + 1, sample_idx[mask]))
        queue.append((ri, depth + 1, sample_idx[~mask]))

    # t3 = time.time()
    output = DecisionTreeRegressionPredict(x_, node_list, leaf_values, input_weight)
    # t4 = time.time()
    # print(f"new prepare: {(t2-t1)*1000:.3f} ms, build: {(t3-t2)*1000:.3f} ms, predict: {(t4-t3)*1000:.3f} ms")
    output = torch.as_tensor(output, device=x.device)
    output_dim = int(hyperparameters['edge_output_dim_scale'] * hyperparameters['num_features_used'])
    if output_dim != feat_num:
        perm = torch.randperm(output_dim)
        output = output[:, perm[:output_dim]]
    
    return torch.as_tensor(output, device=x.device), {'tree': node_list, 'leaf_values': leaf_values, 'x': x}


def visualize_decision_tree(node_array, filename='decision_tree'):
    """
    使用graphviz可视化决策树结构
    
    参数:
        node_array (np.ndarray): 决策树节点数组
        filename (str): 输出文件名（不带扩展名）
        feature_names (list): 可选的特征名称列表
    """
    from graphviz import Digraph
    
    dot = Digraph()
    
    def add_nodes(node_idx):
        node = node_array[node_idx]
        node_id = str(node_idx)
        
        if node[0] == 1:  # 叶子节点
            dot.node(node_id, f"Label: {int(node[3])}", 
                    shape='ellipse', style='filled', fillcolor='lightblue')
        else:  # 非叶子节点 [is_leaf, feature_idx, threshold, class_label, left_child_idx, right_child_idx]
            dot.node(node_id, 
                    f"I: {node[1]}\nT: {node[2]:.3f}",
                    shape='ellipse', style='filled', fillcolor='lightyellow')
            
            # 递归添加左子节点
            if node[4] != -1:
                add_nodes(node[4])
                dot.edge(node_id, str(node[4]), label="True")
            
            # 递归添加右子节点
            if node[5] != -1:
                add_nodes(node[5])
                dot.edge(node_id, str(node[5]), label="False")
    
    # 从根节点开始构建树
    add_nodes(0)
    
    # 渲染并保存为png
    dot.render(filename, format='png', cleanup=True)





if __name__ == '__main__':
    sampler = lambda a, b : lambda : np.random.normal(a, b)
    f = sampler(1 ,2)
    
    def get_threshold_func(a,b):
        return random.random() * (b - a) + a
    def get_threshold_func_beta(a,b):
        return np.random.beta(0.95, 8) * (b - a) + a
    
    # 测试新版决策树
    if True:
        x = torch.rand(1000, 32)
        tree = DecisionTreeClassifier(x, 5, 10, 0.2, get_threshold_func)
        visualize_decision_tree(tree, filename='decision_tree')
    
    
    
    h = {'num_features_used': 32, 
         'K': 5,
        #  'mlp_sampler_modulo': f,
        #  'mlp_sampler_power': f,
        #  'categorical_sampler_K': f,
        #  'sampler_edge_noise_std': f,
         'tree_num_sample_fit': 400,
         'tree_max_num_classes': 8,
         'tree_max_depth': 5,
         'tree_leaf_prob': 0.1,
         'tree_threshold_func': get_threshold_func_beta,
         'edge_fixed_gaussian_noise': False}

    # 测试散度
    if False:
        t = {}
        
        unique_value_all = []
        counts_all = []
        squared_differences = []
        squared_differences2 = []
        ys = []
        y2s = []
        x = torch.rand(1000, 32)
        y = DecesionTree(x,h)
        y = DecesionTree(x,h)
        y = DecesionTree(x,h)
        
        tt = 0
        for i in range(50):
            x = torch.rand(1000, 32)
            t1 = time.time()
            
            y = DecesionTree(x,h)
            
            # DecesionTree_old_lab(x,h)

            t2 = time.time()
            tt = tt + t2 - t1
        
        # counts = [squared_differences, squared_differences2]
        
        # plot_box_pic(unique_value_all, counts, "res_diff_tree.png")
        
        # counts = np.apply_along_axis(lambda x: np.bincount(x, minlength=8), axis=1, arr=ys)
        # counts2= np.apply_along_axis(lambda x: np.bincount(x, minlength=8), axis=1, arr=y2s)
        # # plot_label_stats(unique_value_all, counts_all)
        # plot_box_pic(unique_value_all, counts, "res_our.png")
        # plot_box_pic(unique_value_all, counts2, "res_def.png")
        
        # print(y)
        print("DecesionTree, cost: %.2f ms" % ((tt)*1000/50))
    
    # 测试并发
    if False:
        processes = []
        x = torch.rand(1000, 32)
        
        pool = mp.Pool(processes=100)
        # 存储异步结果对象的列表
        async_results = []
        t1 = time.time()
        # 异步执行函数
        for i in range(100):
            async_result = pool.apply_async(DecesionTree, (x,h))
            async_results.append(async_result)
        # 关闭进程池，不再接受新的任务
        pool.close()
        # 等待所有任务完成
        pool.join()
        # 获取所有返回值
        results = [async_result.get() for async_result in async_results]
        # print("所有函数的返回值:", len(results))
        t2 = time.time()
        print("DecesionTree, cost: %.2f ms" % ((t2-t1)*1000))
        # 打印所有返回值
        # print("所有函数的返回值:", results)
            


        # print("所有进程执行完毕, results:", results)
    
    # for f, func in [('MLP', MLP), 
    #                 ('MLP_Lab', SNN),
    #                 ('GaussianNoise', GaussianNoise),
    #                 ('Categorical', Categorical)]:
    #     t[f] = []
    #     for i in range(1000):
    #         t1 = time.time()
    #         if f == 'MLP_Lab':
    #             o = func(32)(x)
    #         else:
    #             o = func(x, h)
    #         t[f].append(time.time() - t1)
    #     print("%s: %.2f ms" % (f, 1000*np.mean(t[f])))
        
    
    # for f, func in [('DecesionTree', DecesionTree),
    #                 ('DecisionTree_Lab', DecisionTree_Lab)]:
    #     t[f] = []
    #     for i in range(20):
    #         t1 = time.time()
    #         if f == 'DecisionTree_Lab':
    #             o = func(32)(x)
    #         else:
    #             o = func(x, h)
    #         t[f].append(time.time() - t1)
    #     print("%s: %.2f ms" % (f, 1000*np.mean(t[f])))
