import openml
cc18_tasks = openml.tasks.list_tasks(tag='OpenML-CC18')
# 用于存储数据集 ID 的集合
dataset_ids = set()

# 遍历任务列表，提取数据集 ID
for task_id, task_info in cc18_tasks.items():
    dataset_ids.add(task_info['did'])

from inference.classifier import TabPFNClassifier

import time
import torch
import numpy as np
import os
from tqdm import tqdm

from datasets import load_openml_list, open_cc_dids, open_cc_valid_dids, test_dids_classification,valid_large_classification

from scripts import tabular_metrics
import random

import gc
from xgboost import XGBClassifier
import pandas as pd
base_path = '.'### Load datasets

valid_large_classification = list(dataset_ids)
valid_large_classification.remove(40927)
valid_large_classification.remove(1478)
valid_large_classification.remove(40996)


cc_test_datasets_multiclass, cc_test_datasets_multiclass_df = load_openml_list(valid_large_classification, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 10000000, num_feats=10000, return_capped=False)


def get_datasets(selector, task_type, suite='cc'):
    if task_type == 'binary':
        ds = valid_datasets_binary if selector == 'valid' else test_datasets_binary
    else:
        if suite == 'openml':
            ds = valid_datasets_multiclass if selector == 'valid' else test_datasets_multiclass
        elif suite == 'cc':
            ds = cc_valid_datasets_multiclass if selector == 'valid' else cc_test_datasets_multiclass
        else:
            raise Exception("Unknown suite")
    return ds

model_string, longer, task_type = '', 1, 'multiclass'
test_datasets= get_datasets('test', task_type, suite='cc')

rsts = []
save_root  = '测试模型/result/prior_diff_real_checkpoint_n_0_epoch_36'  # 输出路径
version = 'v2'  
os.makedirs(save_root, exist_ok=True)


base_path = './测试模型' 
model_file_name = 'prior_diff_real_checkpoint_n_0_epoch_36.cpkt'
model_save_name = model_file_name.replace('.cpkt', '-mfp.cpkt')
model_ours = os.path.join(base_path, model_file_name)
model_default = os.path.join(base_path, 'tabpfn-v2-03ep-fp32.ckpt')     # 必要，加载模型结构、参数使用
model_save = os.path.join(base_path, model_save_name)

model_m = torch.load(open(model_ours,'rb'))
model_d = torch.load(open(model_default,'rb'))
my_stat = model_m[0]

new_stat = {}
for k, v in my_stat.items():
    new_stat[k.replace('module.','')] = v
for k, v in model_d['state_dict'].items():
    model_d['state_dict'][k]=new_stat[k]

torch.save(model_d, open(model_save,'wb'))
print(model_save, 'model_save, done')


for evaluation_dataset_index in tqdm(range(len(test_datasets))):

    ds = test_datasets[evaluation_dataset_index]
    print(f'Evaluation dataset name: {ds[0]} shape {ds[1].shape}')
    if ds[1].shape[1] > 100:
        continue
    if ds[1].shape[0] > 10000:
        continue
    data_device = 'cpu' if ds[1].shape[0] > 50000 else 'cuda:0'

    rst = {'dataset name':ds[0], 'num_data':ds[1].shape[0],'num_feat':ds[1].shape[1], 'num_class':len(np.unique(test_datasets[0][2]))}
    xs, ys = ds[1].clone(), ds[2].clone()
    eval_position = int(xs.shape[0] *0.5)
    train_xs, train_ys = xs[0:eval_position], ys[0:eval_position]
    test_xs, test_ys = xs[eval_position:], ys[eval_position:]
    train_xs.shape, train_ys.shape, test_xs.shape, test_ys.shape
    
    # ls models_diff
    
    # classifier = XGBClassifier(device='cpu')
    classifier = XGBClassifier(device=data_device)
    classifier.fit(train_xs, train_ys)
    prediction_ = classifier.predict_proba(test_xs)
    
    roc, ce = tabular_metrics.auc_metric(test_ys, prediction_), tabular_metrics.cross_entropy(test_ys, prediction_)
    class_num = prediction_.shape[1]
    output_df = {'label':test_ys.numpy()}
    for i in range(class_num):
        output_df[f'pred_{i}'] = prediction_[:,i]
    pd.DataFrame(output_df).to_csv(os.path.join(save_root, ds[0]+'_pred_xgboost.csv'), index=False)

    rst['AUC_xgboost'] = float(roc)
    # try:
    if True:
        init_start = time.time()
        classifier = TabPFNClassifier(device=data_device, ignore_pretraining_limits=True, model_path=model_save)
        fit_start = time.time()
        classifier.fit(train_xs, train_ys)
        fit_end = time.time()
        print("datasetshape: ", ds[1].shape)
        predict_start = time.time()
        prediction_ = classifier.predict_proba(test_xs)
        predict_end = time.time()
        print(f"init: {fit_start - init_start}s, fit: {fit_end - fit_start}s, predict: {predict_end - predict_start}s")
        roc, ce = tabular_metrics.auc_metric(test_ys, prediction_), tabular_metrics.cross_entropy(test_ys, prediction_)
    
        rst['AUC_PFN'] = float(roc)
        output_df = {'label':test_ys.numpy()}
        for i in range(class_num):
            output_df[f'pred_{i}'] = prediction_[:,i]
        pd.DataFrame(output_df).to_csv(os.path.join(save_root, ds[0]+'_pred_pfc.csv'), index=False)
    # except:
    #     rst['AUC_PFN'] = -1
    rsts.append(rst)

    gc.collect()  # 强制进行垃圾回收
    if 'cuda' in data_device:
        torch.cuda.empty_cache()  # 再次确保显存释放
    print(rst)
rsts = pd.DataFrame(rsts)
rsts.to_csv(os.path.join(save_root, 'all_rst.csv'), index=False)
