# 环境配置
```bash
conda env create -f environment.yml  # 创建一个名字是PFN的虚拟环境；可以打开environment.yml自己改名字
cd PATH-TO TabPFN-v2
```
**切换tabpfn版本时候注意先uninstall旧版本，确保版本切换正确**

# 参数配置
修改ST_train.py

```python
base_path = './trained_models/exp1' # 配置模型权重输出目录
```
新增online inference功能, 主要用于在线评测模型指标, 默认开启，使用CC18数据集，每10个epoch评测一次，结果存储在base_path路径下，涉及ST_train.py中的参数如下，可根据需要进行修改：
```python
# 以时间戳、任务ID、节点信息为基准，生成任务名称
now = datetime.now()
time_str = now.strftime("%m%d_%H%M")
if host_info not in vid:
    task_name=f"{config['task_ID']}_{time_str}"
else:
    task_name=f"{config['task_ID']}_{vid[host_info]}_{time_str}"

config['task_name'] = task_name         # 任务名称

# 在线评测的参数配置
save_path = os.path.join(base_path, task_name, "results")
config['evaluate_config'] = {
    "dataset": "OpenML-CC18",           # 测试数据集
    "save_path": save_path,             # 结果路径，评测结果会保存至该路径下
    "single_eval_pos": 0.5,             # 训练、评测分割位置
    "num_feats": 100,                   # 特征数量  
    "max_samples": 50000,               # 最大样本数量
    "save_each_dataset_result": False   # 是否保存每个数据集的评测结果
}
```
wandb日志记录，项目集成了基于wandb日志记录功能，wandb信息详见：https://wandb.ai/site/
如果使用wandb的在线功能，需要注册wandb账号，并设置wandb的API key，并将key添加至ST_train.py的：
```python
# wandb key
config['wandb_api_key'] = "YOUR API KEY"
```
本地记录模式，在启动后的wandb提示模式时，输入3即可
```python
wandb: (1) Create a W&B account
wandb: (2) Use an existing W&B account
wandb: (3) Don't visualize my results
wandb: Enter your choice: 3
```

其它参数自己explore就可以 

# 进行对比

对于单卡而言，每个epoch看到的数据集数量是 8*1024。
如果是多卡，数据集数量直接乘以卡数量。
目前实验基本都是以8卡为基准，即一个epoch数据集数量是8\*1024\*8
如果增加节点，手动减小num_steps；如果卡比8卡少，手动增加num_steps

*v2版本的模型比较大，batch size需要改小一点*
```python
config['aggregate_k_gradients'] = 8
scale = 1/8
config['batch_size'] = 8*config['aggregate_k_gradients']*scale 
config['num_steps'] = 1024//config['aggregate_k_gradients']/scale
```

# 执行训练

模型训练
```python 
# 激活虚拟环境
conda activate PFN

# 方式1：单卡训练

python ST_train.py
# 方式2：分布式训练，使用run.sh脚本启动，可根据自己需求，修改run.sh中的机器信息
. run.sh
```
恢复训练
```python
# 激活虚拟环境
conda activate PFN

# 如果需要恢复训练，在执行训练脚本时增加如下形参
# 方式1，单卡训练
python ST_train.py --resume PATH-TO-CKPT

# 方式2，分布式训练, 使用run.sh脚本,注意：请严格按照下述格式执行run.sh脚本，否则可能会报错，请根据自己运行环境修run.sh脚本
. run.sh PATH-TO-CKPT
```


# 模型测试
v2版本的模型需要好之后，需要调整模型ckpt的结构，代码在：
v2模型格式修改.ipynb

修改一下参数即可
```python 
model_ours = '训练好的模型路径/prior_diff_real_checkpoint_n_0_epoch_3.cpkt' 
model_default = './tabpfn-v2-classifier.ckpt' # 官方模型路径
model_save = '输出模型路径/prior_diff_real_checkpoint_n_0_epoch_3.cpkt'
```


## cc18数据集相关操作
### 数据集目录调整建议
cc18数据集的目录文件夹 `/openml/`，建议转移至用户主目录下的 `~/.cache/` 路径下。
不放这个它会自己下载，但是非常慢，直接用我们下载好的移动过去就可以

### 数据集测试运行
- **测试cc18数据集（v2版本）**：
```bash
python test_cc18_v2.py
```
### 脚本参数设置
对`test_cc18_v2.py`修改以下参数可实现正常输出：
```python
save_root  = 'output_classifier_v2_default_0225'    # 输出路径，格式：版本+模型类型
base_path = "CKPT-PATH"                             # 模型所在路径名称
model_file_name = 'NAME-OF-CKPT'                    # 模型名称
```



## 测试集可视化操作
### 测试集可视化.ipynb
该文件通过指定 `data_v1_default`、`data_v1_reproduce`、`data_v2_default` 等多个模型结果 `csv` 文件地址来进行可视化操作。
仅供参考 
### 测试集可视化_batch.ipynb
此文件通过批量运行同一个模型的多个 `epoch` 文件来实现可视化。 
仅供参考 


# 修改日志
2025.3.24 v2版本更新
- remove_outliers方法优化, 使用对称方法处理散点, 详见: ./utils.py; 
- 优化数据生成的补零策略, 基于同batch的最大特征数量补零, 详见: ./priors/utils.py:71~74; 
- train、eval分割点优化, 增加batch内的train、eval的label覆盖校验与处理, 详见: ./priors/utils.py:77~99; 
- 同batch补零、train和eval的label校验、标签环偏移动值dataloader处统一处理,详见: ./priors/utils.py:100~122, 
- num_features_used使用beta分布生成数据, 详见: model_configs.py，./priors/flexible_priors.py; 
- train中使用数据生成器校验过的single_eval_pos, 详见: train.py; 
- 常用超参命令行参数化, 详见: ST_train.py

2025.4.18 V2版更新
- 增加PFN V2的数据生成器
- 屏蔽移除空特征编码器，详见：encoders.py:RemoveEmptyFeaturesEncoderStep