import sys
import os 
sys.path.append('/home/<USER>/Project/tabpfn_v2/')

# from priors import scm
import numpy as np
import unittest
import torch
import random
import networkx as nx
from typing import *
import matplotlib.pyplot as plt

PATH_test = '/home/<USER>/Project/tabpfn_v2/priors/test_data_save'

def get_DAG(sampler_num_dag = None,
            sampler_num_node = None,
            sampler_prob = None
            ) -> List:
    
    # 定义re-direction概率
    if isinstance(sampler_prob, float):
        p = sampler_prob
    else:
        p = sampler_prob()
    p = abs(p) % 1   # 确保 p 在 [0, 1] 范围内
    
    assert sampler_num_dag != None and sampler_num_node != None and sampler_prob != None, 'sampler_num_dag, sampler_num_node and sampler_prob must be provided'

    # 定义DAG概率
    if isinstance(sampler_num_dag, int):
        num_dag = sampler_num_dag
    else:
        # DAG总数必须为正整数
        num_dag = int(np.floor(abs(sampler_num_dag())).item())
        
    # 生成图：通过re-direction sampling生成DAG, reverse()获得数据生成阶段的正向DAG
    dags = []
    for _ in range(num_dag):
        # 定义节点总数
        if isinstance(sampler_num_node, int):
            num_node = sampler_num_node
        else:
            # 总节点数必须为正整数
            num_node = int(np.ceil(abs(sampler_num_node())).item())    
        
        dags.append(nx.gnr_graph(num_node, p))
            
    return dags


def save_DAG(dag: nx.Graph, path: str, file_name: str) -> None:
    if not os.path.exists(path):
        os.makedirs(path)
    if '.gml' not in file_name:
        file_name += '.gml'
    path = os.path.join(path, "", file_name)
    nx.write_gml(dag, path)
    return


def load_DAG(path: str, file_name: str) -> nx.Graph:
    if '.gml' not in file_name:
        file_name += '.gml'
    path = os.path.join(path, file_name)
    dag = nx.read_gml(path)
    return dag


def draw_DAG(dag_plt: nx.Graph, 
             X_node: List = None, 
             Y_node: List = None, 
             XY_node: List = None,
             edge_labels: dict = None,
             pos_circle=True) -> None:
    node_colors = []
    if X_node == None:
        X_node = []
    if Y_node == None:
        Y_node = []
    if XY_node == None:
        XY_node = []
    for node in dag_plt.nodes():
        node = int(node)
        # X，Y同时存在于一个node
        if node in XY_node:
            node_colors.append('yellow')
        elif node in X_node:
            node_colors.append('skyblue')
        elif node in Y_node:
            node_colors.append('red')
        else:
            node_colors.append('gainsboro')
    
    if pos_circle:
        # 圆形布局（适合小规模图）
        pos = nx.circular_layout(dag_plt)
    else:
        # 计算层级
        levels = {n: 0 for n in dag_plt.nodes()}
        for node in nx.topological_sort(dag_plt):
            for successor in dag_plt.successors(node):
                levels[successor] = max(levels[successor], levels[node] + 1)
        
        # 将层级信息添加到节点属性中
        nx.set_node_attributes(dag_plt, levels, name='subset')
        
        # 使用正确的subset_key参数
        pos = nx.multipartite_layout(dag_plt, subset_key='subset', align='horizontal', scale=2)
        
        # 调整坐标方向
        pos = {n: (x, -y) for n, (x, y) in pos.items()}
    
    plt.figure()
    if node_colors != []:
        nx.draw(dag_plt, with_labels=True, pos=pos, node_color=node_colors)
    else:
        nx.draw(dag_plt, with_labels=True, pos=pos, node_color='gainsboro')
    if edge_labels != None:
        nx.draw_networkx_edge_labels(
            dag_plt, 
            pos,
            edge_labels=edge_labels,
            font_color='darkred',
            font_size=10
        )
    return
    

def node_match(n1, n2):
    return n1.get('color') == n2.get('color')

def edge_match(e1, e2):
    return e1.get('weight') == e2.get('weight')

def test_DAG_save_load(num_dag=3, num_node=10, p_redirection=0.1, path=PATH_test):
    dags = get_DAG(num_dag, num_node, p_redirection)

    for idx, dag in enumerate(dags):
        if random.random() > 0.5:
            dag_file = f"dag_{idx}"
        else:
            dag_file = f"dag_{idx}.gml"        

        save_DAG(dag, path, dag_file)
        dag_loaded = load_DAG(path, dag_file)
        assert nx.is_isomorphic(dag_loaded, dag, node_match=node_match, edge_match=edge_match), f"Failed to save and load DAG {idx}"
    
    empty_dag = nx.DiGraph()
    save_DAG(empty_dag, path, 'empty_dag')
    dag_loaded = load_DAG(path, dag_file)
    assert nx.is_isomorphic(dag_loaded, dag, node_match=node_match, edge_match=edge_match), f"Failed to save and load DAG {idx}"


if __name__ == "__main__":
    test_DAG_save_load(num_dag=3, num_node=10, p_redirection=0.1, path=PATH_test)
    
    dags = get_DAG(1, 24, 0.1)
    
    for idx, dag in enumerate(dags):
        if random.random() > 0.5:
            dag_file = f"dag_{idx}"
        else:
            dag_file = f"dag_{idx}.gml"        

        save_DAG(dag, PATH_test, dag_file)
        dag_loaded = load_DAG(PATH_test, dag_file)
        
        edge_label = {(u, v): dag_loaded[u][v].get('label', 'mlp') 
                      if random.random() < 0.5 else 'tree'
                        for u, v in dag_loaded.edges() }
        
        # draw_DAG(dag, [2,3,4], [0])
        draw_DAG(dag_loaded, [2,3,4], [0], [1, 2], edge_labels=edge_label)
        draw_DAG(dag_loaded, [2,3,4], [0], [1, 2], edge_labels=edge_label, pos_circle=False)
        
    #%%
    # d1 = 1
    # d2 = 1
    # s = f'batch_{d1}_sample_{d2}'
    
    # dag1 = load_DAG('/home/<USER>/Project/tabpfn_v2/Data_SCM/', 'epoch_1_'+s+'_dag_0')
    
    # dag2 = load_DAG('/home/<USER>/Project/tabpfn_v2/Data_SCM/', 'epoch_1_'+s+'_dag_1')
    
    # dag3 = load_DAG('/home/<USER>/Project/tabpfn_v2/Data_SCM/', 'epoch_1_'+s+'_dag_2')
    
    # filename = 'epoch_1_'+s+'_info.pckl'
    # import pickle
    # # 读取文件
    # f = open('/home/<USER>/Project/tabpfn_v2/Data_SCM/' + filename, 'rb')
    # var = pickle.load(f)
    # f.close()
    
    # draw_DAG(dag1, 
    #          var['node_sampled'][0]['x'], 
    #          var['node_sampled'][0]['y'],
    #          var['node_sampled'][0]['xy'],
    #          var['edge_type'][0])
    
    # draw_DAG(dag2, 
    #          var['node_sampled'][1]['x'], 
    #          var['node_sampled'][1]['y'],
    #          var['node_sampled'][1]['xy'],
    #          var['edge_type'][1])
    
    # draw_DAG(dag3, 
    #          var['node_sampled'][2]['x'], 
    #          var['node_sampled'][2]['y'],
    #          var['node_sampled'][2]['xy'],
    #          var['edge_type'][2])
    
# %%


