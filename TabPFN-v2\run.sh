conda activate PFN
export NCCL_SHM_DISABLE=0
export NCCL_SOCKET_IFNAME=lo
export OMP_NUM_THREADS=24
export TORCH_CPP_LOG_LEVEL=ERROR

if [ $# -gt 0 ]; then
    echo "Running with resume: $1"
    CUDA_VISIBLE_DEVICES=0,1,2,3 WANDB_MODE=disabled torchrun --nproc_per_node=4 ST_train.py --resume $1
else
    CUDA_VISIBLE_DEVICES=0,1,2,3 WANDB_MODE=disabled torchrun --nproc_per_node=4 ST_train.py
fi

#WANDB_MODE=disabled python ST_train.py
#torchrun --nproc_per_node=8 ST_train.py
