{"cells": [{"cell_type": "code", "execution_count": 3, "id": "1a07549a-b5e7-4ac3-8c56-8d38d1fab8f0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline\n", "import seaborn as sns\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 4, "id": "fe4c28cf-3088-4e00-89d3-b891124c3d3e", "metadata": {}, "outputs": [], "source": ["import matplotlib.font_manager as fm\n", "\n", "# 定义字体文件的路径，这里假设字体文件名为 myfont.ttf，且与代码文件在同一目录下\n", "font_path = 'TabPFN-tabpfn_v1/tabpfn/simhei.ttf'\n", "\n", "# 加载字体文件\n", "font_prop = fm.FontProperties(fname=font_path)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "bc5a320b-a25c-4e79-a046-2027a61bb1f3", "metadata": {"scrolled": true}, "outputs": [], "source": ["base_dir = '/home/<USER>/workspace/TabPFN/output_cmp'\n", "data_v1_default = pd.read_csv(f'/{base_dir}/output_classifier_v1_default_0225/all_rst.csv').rename(columns={'AUC_PFN':'AUC_PFN_v1_default'})\n", "data_v1_reproduce = pd.read_csv(f'{base_dir}/output_classifier_v1_reproduce_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v1_reproduce'})\n", "data_v2_default = pd.read_csv(f'{base_dir}/output_classifier_v2_default_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v2_default'})\n", "data_v1_large = pd.read_csv(f'{base_dir}/output_classifier_v1_large2_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v1_large'})"]}, {"cell_type": "code", "execution_count": 7, "id": "7e7aa001-8d6f-4b3e-900c-930fa2b24fa6", "metadata": {}, "outputs": [], "source": ["data_all = data_v1_default.merge(data_v1_reproduce).merge(data_v1_large).merge(data_v2_default)\n", "# data_all = data_all[data_all.AUC_PFN_v1_large != -1]\n", "data_all.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fb21fff6-21ba-4fbb-9bfd-0a695d9420d2", "metadata": {}, "outputs": [], "source": ["data_all.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a59d0554-a63a-49a2-862c-0b23ed72f7a0", "metadata": {}, "outputs": [], "source": ["data_all[data_all['dataset name']=='adult']"]}, {"cell_type": "code", "execution_count": 10, "id": "1fece888-5dd5-4cb2-a1cb-3a70b7093516", "metadata": {}, "outputs": [{"data": {"text/plain": ["(52, 9)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data_all.shape"]}, {"cell_type": "code", "execution_count": null, "id": "a7d92c56-0463-4966-9aaa-a0a07065db48", "metadata": {}, "outputs": [], "source": ["plt.hist(data_all['num_data'], bins=50)\n", "plt.title('data num')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d2c111a7-ce50-481b-b6dd-1cc44bd83b5f", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_all\n", "auc_cols = df.filter(regex='^AUC').columns\n", "cutoff = 5000\n", "df['num_data_s'] = df['num_data']>cutoff\n", "print(np.sum(df['num_data_s']))\n", "# df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(x='num_data_s', y='AUC', hue='model', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "plt.xlabel('num_data')\n", "plt.ylabel('AUC')\n", "plt.title(f'num_data>{cutoff}')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "270da715-33e9-4b4f-a435-bea353d2ef78", "metadata": {}, "outputs": [], "source": ["cmap = plt.get_cmap('inferno')\n", "\n", "# 创建散点图，使用 data_all.num 作为颜色映射的依据\n", "sc = plt.scatter(data_all.AUC_PFN_v1_large, data_all.AUC_xgboost, c=data_all.num_data, cmap=cmap, label='xgboost', s=3, alpha=1, marker='o')\n", "plt.plot([0,1],[0,1], '--', linewidth=0.5)\n", "# 添加颜色条，用于显示颜色和数值的对应关系\n", "plt.colorbar(sc)\n", "plt.xlim(0.49,1)\n", "plt.ylim(0.49,1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "aaf1025a-60a8-40d4-9435-fab4ed69bc9b", "metadata": {}, "outputs": [], "source": ["cmap = plt.get_cmap('inferno')\n", "\n", "# 创建散点图，使用 data_all.num 作为颜色映射的依据\n", "sc = plt.scatter(data_all.AUC_xgboost, data_all.AUC_PFN_v1_large, c=data_all.num_data, cmap=cmap, label='xgboost', s=3, alpha=1, marker='o')\n", "plt.plot([0,1],[0,1], '--', linewidth=0.5)\n", "# 添加颜色条，用于显示颜色和数值的对应关系\n", "plt.colorbar(sc)\n", "plt.xlim(0.49,1)\n", "plt.ylim(0.49,1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b43dab5f-bd55-4d70-a86c-218293e62eb9", "metadata": {}, "outputs": [], "source": ["cmap = plt.get_cmap('inferno')\n", "\n", "# 创建散点图，使用 data_all.num 作为颜色映射的依据\n", "sc = plt.scatter(data_all.AUC_PFN_v1_default, data_all.AUC_PFN_v2_default, c=data_all.num_data, cmap=cmap, label='xgboost', s=3, alpha=1, marker='o')\n", "plt.plot([0,1],[0,1], '--', linewidth=0.5)\n", "# 添加颜色条，用于显示颜色和数值的对应关系\n", "plt.colorbar(sc)\n", "plt.xlim(0.49,1)\n", "plt.ylim(0.49,1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "bbf4eb7f-f6b1-4410-8011-ddfe094626c3", "metadata": {}, "outputs": [], "source": ["data_all.head()"]}, {"cell_type": "code", "execution_count": null, "id": "aa423643-1c7a-4aa2-b722-c075cc8504a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ebbb06fc-6efb-4154-ab53-2cac7b059e9d", "metadata": {}, "source": ["# Kaggle"]}, {"cell_type": "code", "execution_count": 5, "id": "f07bad12-92ec-4eda-926b-a0afe6abee83", "metadata": {}, "outputs": [], "source": ["base_dir = '/home/<USER>/workspace/TabPFN/output_cmp'\n", "data_v1_default = pd.read_csv(f'/{base_dir}/output_classifier_v1_default_kaggle_0225/all_rst.csv').rename(columns={'AUC_PFN':'AUC_PFN_v1_default'})\n", "data_v1_reproduce = pd.read_csv(f'{base_dir}/output_classifier_v1_reproduce_kaggle_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v1_reproduce'})\n", "data_v2_default = pd.read_csv(f'{base_dir}/output_classifier_v2_default_kaggle_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v2_default'})\n", "data_v1_large = pd.read_csv(f'{base_dir}/output_classifier_v1_large_kaggle_0225/all_rst.csv')[['dataset name','AUC_PFN']].rename(columns={'AUC_PFN':'AUC_PFN_v1_large'})"]}, {"cell_type": "code", "execution_count": 6, "id": "21f2d6bd-b6e7-4d09-bb62-b747303586e4", "metadata": {}, "outputs": [], "source": ["data_all = data_v1_default.merge(data_v1_reproduce).merge(data_v1_large).merge(data_v2_default)\n", "# data_all = data_all[data_all.AUC_PFN_v1_large != -1]\n", "data_all.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 7, "id": "6faba17d-297c-42e9-896b-e64af91b7d2e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(271, 11)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data_all.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5e4f2c52-9bc3-47c5-a9c5-77ed2012f146", "metadata": {}, "outputs": [], "source": ["data_all"]}, {"cell_type": "code", "execution_count": null, "id": "120f0eb3-f33c-4b40-bb6b-929e2a9a4021", "metadata": {}, "outputs": [], "source": ["plt.hist(data_all['num_data_train'], bins=50)\n", "plt.title('data num')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0e9f1479-61ea-40a7-adf0-b9f1b23a4e58", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_all\n", "auc_cols = df.filter(regex='^AUC').columns\n", "cutoff = 5000\n", "df['num_data_s'] = df['num_data_train']>cutoff\n", "# df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(x='num_data_s', y='AUC', hue='model', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "plt.xlabel('num_data')\n", "plt.ylabel('AUC')\n", "plt.title(f'num_data>{cutoff}')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "fa56cc9c", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_all\n", "auc_cols = df.filter(regex='^AUC').columns\n", "bins = [0, 500, 1000, 3000, 5000, 10000, np.inf]\n", "labels = ['<500', '500-1000', '1000-3000', '3000-5000', '5000-10000', '10000+']\n", "\n", "# 添加样本数统计\n", "df['num_data_s'] = pd.cut(df['num_data_train'], \n", "                         bins=bins,\n", "                         labels=labels,\n", "                         right=False)\n", "\n", "# 计算每个分组的样本数\n", "counts = df['num_data_s'].value_counts().sort_index()\n", "tick_labels = [f'{label} (n={counts[label]})' for label in labels]\n", "\n", "# df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "\n", "# 绘制箱型图后修改x轴标签\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(x='num_data_s', y='AUC', hue='model', data=df_melt)\n", "plt.xticks(ticks=plt.gca().get_xticks(),  # 保持原有刻度位置\n", "           labels=tick_labels,  # 添加样本数的新标签\n", "           fontproperties=font_prop)\n", "plt.xlabel('Training Data Size')\n", "plt.ylabel('AUC')\n", "plt.title('AUC Distribution by Training Data Size')  # 更新标题\n", "# plt.savefig('/home/<USER>/workspace/TabPFN/6.png')\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "dec382ae", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0b42b71e-2c43-493d-8d45-9f71218f74d9", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_all\n", "auc_cols = df.filter(regex='^AUC').columns\n", "# cutoff = 100\n", "# df['num_data_s'] = df['num_data_train']>cutoff\n", "df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(x='num_data_s', y='AUC', hue='model', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "plt.xlabel('num_data')\n", "plt.ylabel('AUC')\n", "plt.title(f'Domain')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d0bb02f1-c710-4cda-aaf7-f1b707fe98c8", "metadata": {}, "outputs": [], "source": ["# 选择AUC开头的列\n", "df = data_all\n", "auc_cols = df.filter(regex='^AUC').columns\n", "\n", "# 中文到英文的映射\n", "chinese_to_english = {\n", "    '社会人文': 'Social_Humanities',\n", "    '商业经济': 'Business_Economy',\n", "    '生活娱乐': 'Life_Entertainment',\n", "    '医疗健康': 'Medical_Health',\n", "    '科学技术': 'Science_Technology',\n", "    '交通运输': 'Transportation',\n", "    '农业食品': 'Agriculture_Food',\n", "    '环境地理': 'Environmental_Geography'\n", "}\n", "\n", "# cutoff = 100\n", "# df['num_data_s'] = df['num_data_train']>cutoff\n", "df['num_data_s'] = df['domain']\n", "# 重塑数据，从宽格式转换为长格式\n", "df_melt = df.melt(id_vars=['num_data_s'], value_vars=auc_cols, var_name='model', value_name='AUC')\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(x='num_data_s', y='AUC', hue='model', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "\n", "# # 修改横坐标标签\n", "# plt.xticks(ticks=range(len(chinese_to_english)), labels=list(chinese_to_english.values()))\n", "# 修改横坐标标签\n", "plt.xticks(ticks=range(len(chinese_to_english)), \n", "           labels=list(chinese_to_english.values()), \n", "           rotation=-45,  # 设置旋转角度为-45度，斜向下\n", "           ha='left',  # 水平对齐方式设为左对齐\n", "           rotation_mode='anchor')  # 旋转模式，保证标签以左下角为锚点旋转\n", "\n", "plt.xlabel('num_data')\n", "plt.ylabel('AUC')\n", "plt.title(f'Domain')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "cd4e0a16", "metadata": {}, "outputs": [], "source": ["# ... existing code ...\n", "\n", "# 过滤AUC_xgboost < 0.85的数据\n", "df_filtered = df[df['AUC_xgboost'] < 0.85]\n", "\n", "# 重塑过滤后的数据\n", "df_melt = df_filtered.melt(\n", "    id_vars=['num_data_s'], \n", "    value_vars=auc_cols, \n", "    var_name='model', \n", "    value_name='AUC'\n", ")\n", "\n", "# 绘制箱型图\n", "plt.figure(figsize=(6, 12))\n", "sns.boxplot(y='AUC', hue='model', data=df_melt)\n", "plt.xticks(fontproperties=font_prop)\n", "plt.xlabel(f'num_data:{len(df_filtered)}')\n", "plt.ylabel('AUC')\n", "plt.title('Model Comparison (AUC_xgboost < 0.85)')  # 更新标题\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 34, "id": "e8981897", "metadata": {}, "outputs": [{"data": {"text/plain": ["107"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_filtered)"]}, {"cell_type": "code", "execution_count": null, "id": "cb199098", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义数据\n", "models = ['xgboost', 'Default', 'Finetuned']\n", "values = [0.736910213734097, 0.737954550355536, 0.7376669419883644]\n", "\n", "# 设置图片清晰度\n", "plt.rcParams['figure.dpi'] = 100\n", "\n", "# 设置全局字体样式\n", "plt.rcParams['font.family'] = 'Arial'\n", "\n", "# 设置图片大小为竖图\n", "plt.figure(figsize=(6, 12))\n", "\n", "# 让柱子没有间隔，将柱子宽度设置为 1\n", "bar_width = 1\n", "index = np.arange(len(models))\n", "# 使用与第二张图类似的色系\n", "colors = ['#0072BD', '#EDB120', '#D95319']\n", "bars = plt.bar(index, values, bar_width, color=colors)\n", "\n", "# 添加数据标签\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.annotate(f'{height:.4f}',\n", "                 xy=(bar.get_x() + bar.get_width() / 2, height),\n", "                 xytext=(0, 3),  \n", "                 textcoords=\"offset points\",\n", "                 ha='center', va='bottom',\n", "                 fontsize=12)\n", "\n", "# 设置图表标题和坐标轴标签\n", "plt.title('Model Performance Comparison', fontsize=16, fontweight='bold')\n", "plt.xlabel('Models', fontsize=14)\n", "plt.ylabel('Values', fontsize=14)\n", "\n", "# 设置 x 轴刻度标签\n", "plt.xticks(index, models, fontsize=12)\n", "\n", "# 设置 y 轴范围，从 0.73 开始\n", "plt.ylim(0.73, max(values) + 0.001)\n", "\n", "# 添加网格线\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 调整图表边距\n", "plt.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)\n", "\n", "# 显示图表\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f32216f4", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义数据\n", "models = ['xgboost', 'Default', 'Finetuned']\n", "values = [0.736910213734097, 0.737954550355536, 0.7376669419883644]\n", "\n", "# 设置图片清晰度\n", "plt.rcParams['figure.dpi'] = 100\n", "# 设置全局字体样式\n", "plt.rcParams['font.family'] = 'Arial'\n", "\n", "# 设置图片大小为竖图\n", "plt.figure(figsize=(12, 4))\n", "\n", "# 绘制水平条形图\n", "bar_height = 0.8\n", "bars = plt.barh(np.arange(len(models)), values, bar_height, color=['#0072BD', '#EDB120', '#D95319'])\n", "\n", "# 在柱子上添加数据标签（黑体加粗）\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    plt.annotate(f'{width:.5f}',\n", "                 xy=(width, bar.get_y() + bar.get_height() / 2),\n", "                 xytext=(3, 0), \n", "                 textcoords=\"offset points\",\n", "                 ha='left', va='center',\n", "                 fontsize=12,\n", "                 fontweight='bold')\n", "\n", "# 设置图表标题和坐标轴标签\n", "plt.title('Model Performance Comparison', fontsize=16, fontweight='bold')\n", "plt.xlabel('Values', fontsize=14)\n", "plt.ylabel('Models', fontsize=14)\n", "\n", "# 设置 y 轴刻度标签\n", "plt.yticks(np.arange(len(models)), models, fontsize=12)\n", "\n", "# 设置 x 轴范围，从 0.73 开始\n", "plt.xlim(0.736, max(values) + 0.0005)\n", "\n", "# 添加网格线\n", "plt.grid(axis='x', linestyle='--', alpha=0.7)\n", "\n", "# 调整图表边距\n", "plt.subplots_adjust(left=0.2, right=0.9, top=0.9, bottom=0.1)\n", "\n", "# 显示图表\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "3e07d7ed", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义数据\n", "models = ['xgboost', 'Default', 'Finetuned']\n", "values = [0.736910213734097, 0.737954550355536, 0.7369432822258029]\n", "\n", "# 设置图片清晰度\n", "plt.rcParams['figure.dpi'] = 100\n", "# 设置全局字体样式\n", "plt.rcParams['font.family'] = 'Arial'\n", "\n", "# 设置图片大小为竖图\n", "plt.figure(figsize=(10, 4))\n", "\n", "# 绘制水平条形图\n", "bar_height = 0.8\n", "bars = plt.barh(np.arange(len(models)), values, bar_height, color=['#0072BD', '#EDB120', '#D95319'])\n", "\n", "# 在柱子上添加数据标签（黑体加粗）\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    plt.annotate(f'{width:.5f}',\n", "                 xy=(width, bar.get_y() + bar.get_height() / 2),\n", "                 xytext=(3, 0), \n", "                 textcoords=\"offset points\",\n", "                 ha='left', va='center',\n", "                 fontsize=12,\n", "                 fontweight='bold')\n", "\n", "# 设置图表标题和坐标轴标签\n", "plt.title('Model Performance Comparison', fontsize=16, fontweight='bold')\n", "plt.xlabel('Values', fontsize=14)\n", "plt.ylabel('Models', fontsize=14)\n", "\n", "# 设置 y 轴刻度标签\n", "plt.yticks(np.arange(len(models)), models, fontsize=12)\n", "\n", "# 设置 x 轴范围，从 0.73 开始\n", "plt.xlim(0.736, max(values) + 0.0005)\n", "\n", "# 添加网格线\n", "plt.grid(axis='x', linestyle='--', alpha=0.7)\n", "\n", "# 调整图表边距\n", "plt.subplots_adjust(left=0.2, right=0.9, top=0.9, bottom=0.1)\n", "\n", "# 显示图表\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "128068d9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PFN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}