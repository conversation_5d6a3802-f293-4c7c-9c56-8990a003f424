import pandas as pd
import torch
import numpy as np
import openml
import warnings


def get_openml_classification(did, max_samples, multiclass=True, shuffled=True):
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=FutureWarning)
        dataset = openml.datasets.get_dataset(did)
        X, y, categorical_indicator, attribute_names = dataset.get_data(
            dataset_format="array", target=dataset.default_target_attribute
        )
    

    if not multiclass:
        X = X[y < 2]
        y = y[y < 2]

    if multiclass and not shuffled:
        raise NotImplementedError("This combination of multiclass and shuffling isn't implemented")

    if not isinstance(X, np.ndarray) or not isinstance(y, np.ndarray):
        # print('Not a NP Array, skipping')
        return None, None, None, None

    if not shuffled:
        sort = np.argsort(y) if y.mean() < 0.5 else np.argsort(-y)
        pos = int(y.sum()) if y.mean() < 0.5 else int((1 - y).sum())
        X, y = X[sort][-pos * 2:], y[sort][-pos * 2:]
        y = torch.tensor(y).reshape(2, -1).transpose(0, 1).reshape(-1).flip([0]).float()
        X = torch.tensor(X).reshape(2, -1, X.shape[1]).transpose(0, 1).reshape(-1, X.shape[1]).flip([0]).float()
    else:
        order = np.arange(y.shape[0])
        np.random.seed(13)
        np.random.shuffle(order)
        X, y = torch.tensor(X[order]), torch.tensor(y[order])
    if max_samples:
        X, y = X[:max_samples], y[:max_samples]

    return X, y, list(np.where(categorical_indicator)[0]), attribute_names

def load_openml_list(dids, filter_for_nan=False
                     , num_feats=100
                     , min_samples = 100
                     , max_samples=400
                     , multiclass=True
                     , max_num_classes=10
                     , shuffled=True
                     , return_capped = False): 
    
    datasets = []
    # openml_list = openml.datasets.list_datasets(dids)
    openml_list = {3: {'did': 3, 'name': 'kr-vs-kp', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1669.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 1527.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 37.0, 'NumberOfInstances': 3196.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 37.0}, 6: {'did': 6, 'name': 'letter', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 813.0, 'MaxNominalAttDistinctValues': 26.0, 'MinorityClassSize': 734.0, 'NumberOfClasses': 26.0, 'NumberOfFeatures': 17.0, 'NumberOfInstances': 20000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 16.0, 'NumberOfSymbolicFeatures': 1.0}, 11: {'did': 11, 'name': 'balance-scale', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 288.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 49.0, 'NumberOfClasses': 3.0, 'NumberOfFeatures': 5.0, 'NumberOfInstances': 625.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 4.0, 'NumberOfSymbolicFeatures': 1.0}, 12: {'did': 12, 'name': 'mfeat-factors', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 200.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 200.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 217.0, 'NumberOfInstances': 2000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 216.0, 'NumberOfSymbolicFeatures': 1.0}, 14: {'did': 14, 'name': 'mfeat-fourier', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 200.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 200.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 77.0, 'NumberOfInstances': 2000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 76.0, 'NumberOfSymbolicFeatures': 1.0}, 15: {'did': 15, 'name': 'breast-w', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 458.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 241.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 10.0, 'NumberOfInstances': 699.0, 'NumberOfInstancesWithMissingValues': 16.0, 'NumberOfMissingValues': 16.0, 'NumberOfNumericFeatures': 9.0, 'NumberOfSymbolicFeatures': 1.0}, 16: {'did': 16, 'name': 'mfeat-karhunen', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 200.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 200.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 65.0, 'NumberOfInstances': 2000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 64.0, 'NumberOfSymbolicFeatures': 1.0}, 18: {'did': 18, 'name': 'mfeat-morphological', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 200.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 200.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 7.0, 'NumberOfInstances': 2000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 6.0, 'NumberOfSymbolicFeatures': 1.0}, 22: {'did': 22, 'name': 'mfeat-zernike', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 200.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 200.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 48.0, 'NumberOfInstances': 2000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 47.0, 'NumberOfSymbolicFeatures': 1.0}, 23: {'did': 23, 'name': 'cmc', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 629.0, 'MaxNominalAttDistinctValues': 4.0, 'MinorityClassSize': 333.0, 'NumberOfClasses': 3.0, 'NumberOfFeatures': 10.0, 'NumberOfInstances': 1473.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 2.0, 'NumberOfSymbolicFeatures': 8.0}, 28: {'did': 28, 'name': 'optdigits', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 572.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 554.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 65.0, 'NumberOfInstances': 5620.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 64.0, 'NumberOfSymbolicFeatures': 1.0}, 29: {'did': 29, 'name': 'credit-approval', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 383.0, 'MaxNominalAttDistinctValues': 14.0, 'MinorityClassSize': 307.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 16.0, 'NumberOfInstances': 690.0, 'NumberOfInstancesWithMissingValues': 37.0, 'NumberOfMissingValues': 67.0, 'NumberOfNumericFeatures': 6.0, 'NumberOfSymbolicFeatures': 10.0}, 31: {'did': 31, 'name': 'credit-g', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 700.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 300.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 21.0, 'NumberOfInstances': 1000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 7.0, 'NumberOfSymbolicFeatures': 14.0}, 32: {'did': 32, 'name': 'pendigits', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1144.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 1055.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 17.0, 'NumberOfInstances': 10992.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 16.0, 'NumberOfSymbolicFeatures': 1.0}, 37: {'did': 37, 'name': 'diabetes', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 500.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 268.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 9.0, 'NumberOfInstances': 768.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 8.0, 'NumberOfSymbolicFeatures': 1.0}, 38: {'did': 38, 'name': 'sick', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 3541.0, 'MaxNominalAttDistinctValues': 5.0, 'MinorityClassSize': 231.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 30.0, 'NumberOfInstances': 3772.0, 'NumberOfInstancesWithMissingValues': 3772.0, 'NumberOfMissingValues': 6064.0, 'NumberOfNumericFeatures': 7.0, 'NumberOfSymbolicFeatures': 23.0}, 44: {'did': 44, 'name': 'spambase', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2788.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 1813.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 58.0, 'NumberOfInstances': 4601.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 57.0, 'NumberOfSymbolicFeatures': 1.0}, 46: {'did': 46, 'name': 'splice', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1655.0, 'MaxNominalAttDistinctValues': 6.0, 'MinorityClassSize': 767.0, 'NumberOfClasses': 3.0, 'NumberOfFeatures': 61.0, 'NumberOfInstances': 3190.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 61.0}, 50: {'did': 50, 'name': 'tic-tac-toe', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 626.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 332.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 10.0, 'NumberOfInstances': 958.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 10.0}, 54: {'did': 54, 'name': 'vehicle', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 218.0, 'MaxNominalAttDistinctValues': 4.0, 'MinorityClassSize': 199.0, 'NumberOfClasses': 4.0, 'NumberOfFeatures': 19.0, 'NumberOfInstances': 846.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 18.0, 'NumberOfSymbolicFeatures': 1.0}, 151: {'did': 151, 'name': 'electricity', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 26075.0, 'MaxNominalAttDistinctValues': 7.0, 'MinorityClassSize': 19237.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 9.0, 'NumberOfInstances': 45312.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 7.0, 'NumberOfSymbolicFeatures': 2.0}, 182: {'did': 182, 'name': 'satimage', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1531.0, 'MaxNominalAttDistinctValues': 6.0, 'MinorityClassSize': 625.0, 'NumberOfClasses': 6.0, 'NumberOfFeatures': 37.0, 'NumberOfInstances': 6430.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 36.0, 'NumberOfSymbolicFeatures': 1.0}, 188: {'did': 188, 'name': 'eucalyptus', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 214.0, 'MaxNominalAttDistinctValues': 27.0, 'MinorityClassSize': 105.0, 'NumberOfClasses': 5.0, 'NumberOfFeatures': 20.0, 'NumberOfInstances': 736.0, 'NumberOfInstancesWithMissingValues': 95.0, 'NumberOfMissingValues': 448.0, 'NumberOfNumericFeatures': 14.0, 'NumberOfSymbolicFeatures': 6.0}, 300: {'did': 300, 'name': 'isolet', 'version': 1, 'uploader': '94', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 300.0, 'MaxNominalAttDistinctValues': 26.0, 'MinorityClassSize': 298.0, 'NumberOfClasses': 26.0, 'NumberOfFeatures': 618.0, 'NumberOfInstances': 7797.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 617.0, 'NumberOfSymbolicFeatures': 1.0}, 307: {'did': 307, 'name': 'vowel', 'version': 2, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 90.0, 'MaxNominalAttDistinctValues': 15.0, 'MinorityClassSize': 90.0, 'NumberOfClasses': 11.0, 'NumberOfFeatures': 13.0, 'NumberOfInstances': 990.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 10.0, 'NumberOfSymbolicFeatures': 3.0}, 458: {'did': 458, 'name': 'analcatdata_authorship', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 317.0, 'MaxNominalAttDistinctValues': 4.0, 'MinorityClassSize': 55.0, 'NumberOfClasses': 4.0, 'NumberOfFeatures': 71.0, 'NumberOfInstances': 841.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 70.0, 'NumberOfSymbolicFeatures': 1.0}, 469: {'did': 469, 'name': 'analcatdata_dmft', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 155.0, 'MaxNominalAttDistinctValues': 9.0, 'MinorityClassSize': 123.0, 'NumberOfClasses': 6.0, 'NumberOfFeatures': 5.0, 'NumberOfInstances': 797.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 5.0}, 554: {'did': 554, 'name': 'mnist_784', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 7877.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 6313.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 785.0, 'NumberOfInstances': 70000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 784.0, 'NumberOfSymbolicFeatures': 1.0}, 1049: {'did': 1049, 'name': 'pc4', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1280.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 178.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 38.0, 'NumberOfInstances': 1458.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 37.0, 'NumberOfSymbolicFeatures': 1.0}, 1050: {'did': 1050, 'name': 'pc3', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1403.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 160.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 38.0, 'NumberOfInstances': 1563.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 37.0, 'NumberOfSymbolicFeatures': 1.0}, 1053: {'did': 1053, 'name': 'jm1', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 8779.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 2106.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 22.0, 'NumberOfInstances': 10885.0, 'NumberOfInstancesWithMissingValues': 5.0, 'NumberOfMissingValues': 25.0, 'NumberOfNumericFeatures': 21.0, 'NumberOfSymbolicFeatures': 1.0}, 1063: {'did': 1063, 'name': 'kc2', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 415.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 107.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 22.0, 'NumberOfInstances': 522.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 21.0, 'NumberOfSymbolicFeatures': 1.0}, 1067: {'did': 1067, 'name': 'kc1', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1783.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 326.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 22.0, 'NumberOfInstances': 2109.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 21.0, 'NumberOfSymbolicFeatures': 1.0}, 1068: {'did': 1068, 'name': 'pc1', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1032.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 77.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 22.0, 'NumberOfInstances': 1109.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 21.0, 'NumberOfSymbolicFeatures': 1.0}, 1461: {'did': 1461, 'name': 'bank-marketing', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 39922.0, 'MaxNominalAttDistinctValues': 12.0, 'MinorityClassSize': 5289.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 17.0, 'NumberOfInstances': 45211.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 7.0, 'NumberOfSymbolicFeatures': 10.0}, 1462: {'did': 1462, 'name': 'banknote-authentication', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 762.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 610.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 5.0, 'NumberOfInstances': 1372.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 4.0, 'NumberOfSymbolicFeatures': 1.0}, 1464: {'did': 1464, 'name': 'blood-transfusion-service-center', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 570.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 178.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 5.0, 'NumberOfInstances': 748.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 4.0, 'NumberOfSymbolicFeatures': 1.0}, 1468: {'did': 1468, 'name': 'cnae-9', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 120.0, 'MaxNominalAttDistinctValues': 9.0, 'MinorityClassSize': 120.0, 'NumberOfClasses': 9.0, 'NumberOfFeatures': 857.0, 'NumberOfInstances': 1080.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 856.0, 'NumberOfSymbolicFeatures': 1.0}, 1475: {'did': 1475, 'name': 'first-order-theorem-proving', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2554.0, 'MaxNominalAttDistinctValues': 6.0, 'MinorityClassSize': 486.0, 'NumberOfClasses': 6.0, 'NumberOfFeatures': 52.0, 'NumberOfInstances': 6118.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 51.0, 'NumberOfSymbolicFeatures': 1.0}, 1480: {'did': 1480, 'name': 'ilpd', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 416.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 167.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 11.0, 'NumberOfInstances': 583.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 9.0, 'NumberOfSymbolicFeatures': 2.0}, 1485: {'did': 1485, 'name': 'madelon', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1300.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 1300.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 501.0, 'NumberOfInstances': 2600.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 500.0, 'NumberOfSymbolicFeatures': 1.0}, 1486: {'did': 1486, 'name': 'nomao', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 24621.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 9844.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 119.0, 'NumberOfInstances': 34465.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 89.0, 'NumberOfSymbolicFeatures': 30.0}, 1487: {'did': 1487, 'name': 'ozone-level-8hr', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2374.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 160.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 73.0, 'NumberOfInstances': 2534.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 72.0, 'NumberOfSymbolicFeatures': 1.0}, 1489: {'did': 1489, 'name': 'phoneme', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 3818.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 1586.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 6.0, 'NumberOfInstances': 5404.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 5.0, 'NumberOfSymbolicFeatures': 1.0}, 1494: {'did': 1494, 'name': 'qsar-biodeg', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 699.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 356.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 42.0, 'NumberOfInstances': 1055.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 41.0, 'NumberOfSymbolicFeatures': 1.0}, 1497: {'did': 1497, 'name': 'wall-robot-navigation', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2205.0, 'MaxNominalAttDistinctValues': 4.0, 'MinorityClassSize': 328.0, 'NumberOfClasses': 4.0, 'NumberOfFeatures': 25.0, 'NumberOfInstances': 5456.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 24.0, 'NumberOfSymbolicFeatures': 1.0}, 1501: {'did': 1501, 'name': 'semeion', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 162.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 155.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 257.0, 'NumberOfInstances': 1593.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 256.0, 'NumberOfSymbolicFeatures': 1.0}, 1510: {'did': 1510, 'name': 'wdbc', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 357.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 212.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 31.0, 'NumberOfInstances': 569.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 30.0, 'NumberOfSymbolicFeatures': 1.0}, 1590: {'did': 1590, 'name': 'adult', 'version': 2, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 37155.0, 'MaxNominalAttDistinctValues': 41.0, 'MinorityClassSize': 11687.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 15.0, 'NumberOfInstances': 48842.0, 'NumberOfInstancesWithMissingValues': 3620.0, 'NumberOfMissingValues': 6465.0, 'NumberOfNumericFeatures': 6.0, 'NumberOfSymbolicFeatures': 9.0}, 4134: {'did': 4134, 'name': 'Bioresponse', 'version': 1, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2034.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 1717.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 1777.0, 'NumberOfInstances': 3751.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 1776.0, 'NumberOfSymbolicFeatures': 1.0}, 4534: {'did': 4534, 'name': 'PhishingWebsites', 'version': 1, 'uploader': '874', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 6157.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 4898.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 31.0, 'NumberOfInstances': 11055.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 31.0}, 4538: {'did': 4538, 'name': 'GesturePhaseSegmentationProcessed', 'version': 1, 'uploader': '874', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2950.0, 'MaxNominalAttDistinctValues': 5.0, 'MinorityClassSize': 998.0, 'NumberOfClasses': 5.0, 'NumberOfFeatures': 33.0, 'NumberOfInstances': 9873.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 32.0, 'NumberOfSymbolicFeatures': 1.0}, 6332: {'did': 6332, 'name': 'cylinder-bands', 'version': 2, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 312.0, 'MaxNominalAttDistinctValues': 71.0, 'MinorityClassSize': 228.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 40.0, 'NumberOfInstances': 540.0, 'NumberOfInstancesWithMissingValues': 263.0, 'NumberOfMissingValues': 999.0, 'NumberOfNumericFeatures': 18.0, 'NumberOfSymbolicFeatures': 22.0}, 23381: {'did': 23381, 'name': 'dresses-sales', 'version': 2, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 290.0, 'MaxNominalAttDistinctValues': 24.0, 'MinorityClassSize': 210.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 13.0, 'NumberOfInstances': 500.0, 'NumberOfInstancesWithMissingValues': 401.0, 'NumberOfMissingValues': 835.0, 'NumberOfNumericFeatures': 1.0, 'NumberOfSymbolicFeatures': 12.0}, 23517: {'did': 23517, 'name': 'numerai28.6', 'version': 2, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 48658.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 47662.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 22.0, 'NumberOfInstances': 96320.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 21.0, 'NumberOfSymbolicFeatures': 1.0}, 40499: {'did': 40499, 'name': 'texture', 'version': 1, 'uploader': '64', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 500.0, 'MaxNominalAttDistinctValues': 11.0, 'MinorityClassSize': 500.0, 'NumberOfClasses': 11.0, 'NumberOfFeatures': 41.0, 'NumberOfInstances': 5500.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 40.0, 'NumberOfSymbolicFeatures': 1.0}, 40668: {'did': 40668, 'name': 'connect-4', 'version': 2, 'uploader': '869', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 44473.0, 'MinorityClassSize': 6449.0, 'NumberOfClasses': 3.0, 'NumberOfFeatures': 43.0, 'NumberOfInstances': 67557.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 43.0}, 40670: {'did': 40670, 'name': 'dna', 'version': 1, 'uploader': '869', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1654.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 765.0, 'NumberOfClasses': 3.0, 'NumberOfFeatures': 181.0, 'NumberOfInstances': 3186.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 181.0}, 40701: {'did': 40701, 'name': 'churn', 'version': 1, 'uploader': '869', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 4293.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 707.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 21.0, 'NumberOfInstances': 5000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 16.0, 'NumberOfSymbolicFeatures': 5.0}, 40923: {'did': 40923, 'name': 'Devnagari-Script', 'version': 1, 'uploader': '3948', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2000.0, 'MaxNominalAttDistinctValues': 46.0, 'MinorityClassSize': 2000.0, 'NumberOfClasses': 46.0, 'NumberOfFeatures': 1025.0, 'NumberOfInstances': 92000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 1024.0, 'NumberOfSymbolicFeatures': 1.0}, 40966: {'did': 40966, 'name': 'MiceProtein', 'version': 4, 'uploader': '2', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 150.0, 'MaxNominalAttDistinctValues': 8.0, 'MinorityClassSize': 105.0, 'NumberOfClasses': 8.0, 'NumberOfFeatures': 82.0, 'NumberOfInstances': 1080.0, 'NumberOfInstancesWithMissingValues': 528.0, 'NumberOfMissingValues': 1396.0, 'NumberOfNumericFeatures': 77.0, 'NumberOfSymbolicFeatures': 5.0}, 40975: {'did': 40975, 'name': 'car', 'version': 3, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 1210.0, 'MaxNominalAttDistinctValues': 4.0, 'MinorityClassSize': 65.0, 'NumberOfClasses': 4.0, 'NumberOfFeatures': 7.0, 'NumberOfInstances': 1728.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 0.0, 'NumberOfSymbolicFeatures': 7.0}, 40978: {'did': 40978, 'name': 'Internet-Advertisements', 'version': 2, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 2820.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 459.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 1559.0, 'NumberOfInstances': 3279.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 3.0, 'NumberOfSymbolicFeatures': 1556.0}, 40979: {'did': 40979, 'name': 'mfeat-pixel', 'version': 3, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 200.0, 'MaxNominalAttDistinctValues': 10.0, 'MinorityClassSize': 200.0, 'NumberOfClasses': 10.0, 'NumberOfFeatures': 241.0, 'NumberOfInstances': 2000.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 240.0, 'NumberOfSymbolicFeatures': 1.0}, 40982: {'did': 40982, 'name': 'steel-plates-fault', 'version': 3, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 673.0, 'MaxNominalAttDistinctValues': 7.0, 'MinorityClassSize': 55.0, 'NumberOfClasses': 7.0, 'NumberOfFeatures': 28.0, 'NumberOfInstances': 1941.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 27.0, 'NumberOfSymbolicFeatures': 1.0}, 40983: {'did': 40983, 'name': 'wilt', 'version': 2, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 4578.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 261.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 6.0, 'NumberOfInstances': 4839.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 5.0, 'NumberOfSymbolicFeatures': 1.0}, 40984: {'did': 40984, 'name': 'segment', 'version': 3, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 330.0, 'MaxNominalAttDistinctValues': 7.0, 'MinorityClassSize': 330.0, 'NumberOfClasses': 7.0, 'NumberOfFeatures': 20.0, 'NumberOfInstances': 2310.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 19.0, 'NumberOfSymbolicFeatures': 1.0}, 40994: {'did': 40994, 'name': 'climate-model-simulation-crashes', 'version': 4, 'uploader': '4265', 'status': 'active', 'format': 'ARFF', 'MajorityClassSize': 494.0, 'MaxNominalAttDistinctValues': 2.0, 'MinorityClassSize': 46.0, 'NumberOfClasses': 2.0, 'NumberOfFeatures': 21.0, 'NumberOfInstances': 540.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 20.0, 'NumberOfSymbolicFeatures': 1.0}, 41027: {'did': 41027, 'name': 'jungle_chess_2pcs_raw_endgame_complete', 'version': 1, 'uploader': '1', 'status': 'active', 'format': 'arff', 'MajorityClassSize': 23062.0, 'MaxNominalAttDistinctValues': 3.0, 'MinorityClassSize': 4335.0, 'NumberOfClasses': 3.0, 'NumberOfFeatures': 7.0, 'NumberOfInstances': 44819.0, 'NumberOfInstancesWithMissingValues': 0.0, 'NumberOfMissingValues': 0.0, 'NumberOfNumericFeatures': 6.0, 'NumberOfSymbolicFeatures': 1.0}}
    # print(f'Number of datasets: {len(openml_list)}')

    datalist = pd.DataFrame.from_dict(openml_list, orient="index")
    if filter_for_nan:
        datalist = datalist[datalist['NumberOfInstancesWithMissingValues'] == 0]
        # print(f'Number of datasets after Nan and feature number filtering: {len(datalist)}')

    for ds in datalist.index:
        modifications = {'samples_capped': False, 'classes_capped': False, 'feats_capped': False}
        entry = datalist.loc[ds]

        # print('Loading', entry['name'], entry.did, 'from cache:', openml.config.get_cache_directory())

        if entry['NumberOfClasses'] == 0.0:
            raise Exception("Regression not supported")
            #X, y, categorical_feats, attribute_names = get_openml_regression(int(entry.did), max_samples)
        else:
            X, y, categorical_feats, attribute_names = get_openml_classification(int(entry.did), max_samples
                                                                , multiclass=multiclass, shuffled=shuffled)
            # print(X.shape, 'raw')
        if X is None:
            continue

        if X.shape[1] > num_feats:
            if return_capped:
                X = X[:, 0:num_feats]
                categorical_feats = [c for c in categorical_feats if c < num_feats]
                modifications['feats_capped'] = True
            else:
                # print('Too many features')
                continue
        if X.shape[0] == max_samples:
            modifications['samples_capped'] = True

        if X.shape[0] < min_samples:
            # print(f'Too few samples left')
            continue

        if len(np.unique(y)) > max_num_classes:
            if return_capped:
                X = X[y < np.unique(y)[10]]
                y = y[y < np.unique(y)[10]]
                modifications['classes_capped'] = True
            else:
                # print(f'Too many classes')
                continue

        datasets += [[entry['name'], X, y, categorical_feats, attribute_names, modifications]]

    return datasets, datalist


# Classification
valid_dids_classification = [13, 59, 4, 15, 40710, 43, 1498]
test_dids_classification = [973, 1596, 40981, 1468, 40984, 40975, 41163, 41147, 1111, 41164, 1169, 1486, 41143, 1461, 41167, 40668, 41146, 41169, 41027, 23517, 41165, 41161, 41159, 41138, 1590, 41166, 1464, 41168, 41150, 1489, 41142, 3, 12, 31, 54, 1067]
valid_large_classification = [  943, 23512,    49,   838,  1131,   767,  1142,   748,  1112,
        1541,   384,   912,  1503,   796,    20,    30,   903,  4541,
         961,   805,  1000,  4135,  1442,   816,  1130,   906,  1511,
         184,   181,   137,  1452,  1481,   949,   449,    50,   913,
        1071,   831,   843,     9,   896,  1532,   311,    39,   451,
         463,   382,   778,   474,   737,  1162,  1538,   820,   188,
         452,  1156,    37,   957,   911,  1508,  1054,   745,  1220,
         763,   900,    25,   387,    38,   757,  1507,   396,  4153,
         806,   779,   746,  1037,   871,   717,  1480,  1010,  1016,
         981,  1547,  1002,  1126,  1459,   846,   837,  1042,   273,
        1524,   375,  1018,  1531,  1458,  6332,  1546,  1129,   679,
         389]

open_cc_dids = [11,
 14,
 15,
 16,
 18,
 22,
 23,
 29,
 31,
 37,
 50,
 54,
 188,
 458,
 469,
 1049,
 1050,
 1063,
 1068,
 1510,
 1494,
 1480,
 1462,
 1464,
 6332,
 23381,
 40966,
 40982,
 40994,
 40975]
# Filtered by N_samples < 2000, N feats < 100, N classes < 10

open_cc_valid_dids = [13,25,35,40,41,43,48,49,51,53,55,56,59,61,187,285,329,333,334,335,336,337,338,377,446,450,451,452,460,463,464,466,470,475,481,679,694,717,721,724,733,738,745,747,748,750,753,756,757,764,765,767,774,778,786,788,795,796,798,801,802,810,811,814,820,825,826,827,831,839,840,841,844,852,853,854,860,880,886,895,900,906,907,908,909,915,925,930,931,934,939,940,941,949,966,968,984,987,996,1048,1054,1071,1073,1100,1115,1412,1442,1443,1444,1446,1447,1448,1451,1453,1488,1490,1495,1498,1499,1506,1508,1511,1512,1520,1523,4153,23499,40496,40646,40663,40669,40680,40682,40686,40690,40693,40705,40706,40981,41430,41538,41919,41976,42172,42261,42544,42585,42638]

grinzstjan_categorical_regression = [44054,
 44055,
 44056,
 44057,
 44059,
 44061,
 44062,
 44063,
 44064,
 44065,
 44066,
 44068,
 44069]

grinzstjan_numerical_classification = [44089,
 44090,
 44091,
 44120,
 44121,
 44122,
 44123,
 44124,
 44125,
 44126,
 44127,
 44128,
 44129,
 44130,
 44131]

grinzstjan_categorical_classification = [44156, 44157, 44159, 44160, 44161, 44162, 44186]
