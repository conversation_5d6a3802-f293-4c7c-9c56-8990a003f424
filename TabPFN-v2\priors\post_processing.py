import os
import torch
import torch.nn as nn
import numpy as np
from typing import *

def min_max_norm(x):
    eps = 1e-5
    maxima = torch.max(x, dim=-1, keepdim=True)[0]
    minima = torch.min(x, dim=-1, keepdim=True)[0]
    x_normed = (x - minima) / (maxima - minima + eps)
    return x_normed

def <PERSON><PERSON><PERSON>_warping(x: torch.Tensor, 
                        c1: Union[float, torch.Tensor], 
                        c0: Union[float, torch.Tensor]) -> torch.Tensor:
    # min-max norm避免在<PERSON><PERSON><PERSON> warping时输入超出 [0,1]范围导致报错
    x_normed = min_max_norm(x)
    c1 = c1 if torch.is_tensor(c1) else torch.tensor(c1)
    c0 = c0 if torch.is_tensor(c0) else torch.tensor(c0)
    
    # c1和c0不能小于0
    c1 = (torch.randn(x.shape[1:], device=x.device) * c1).exp()
    c0 = (torch.randn(x.shape[1:], device=x.device) * c0).exp()
    kumara = torch.distributions.<PERSON><PERSON><PERSON>(concentration1=c1, concentration0=c0)
    x_transformed = kumara.cdf(x_normed)
    return x_transformed
    

if __name__ == '__main__':
   pass

