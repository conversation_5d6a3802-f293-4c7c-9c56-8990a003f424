{"cells": [{"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import time\n", "import warnings\n", "from datetime import datetime\n", "\n", "import torch\n", "\n", "import numpy as np\n", "\n", "import matplotlib.pyplot as plt\n", "from scripts.differentiable_pfn_evaluation import eval_model_range\n", "from scripts.model_builder import get_model, get_default_spec, save_model, load_model\n", "from scripts.transformer_prediction_interface import transformer_predict, get_params_from_config, load_model_workflow\n", "\n", "from scripts.model_configs import *\n", "\n", "from datasets import load_openml_list, open_cc_dids, open_cc_valid_dids\n", "from priors.utils import plot_prior, plot_features\n", "from priors.utils import uniform_int_sampler_f\n", "\n", "from scripts.tabular_metrics import calculate_score_per_method, calculate_score\n", "from scripts.tabular_evaluation import evaluate\n", "\n", "from priors.differentiable_prior import DifferentiableHyperparameterList, draw_random_style, merge_style_with_info\n", "from scripts import tabular_metrics\n", "from notebook_utils import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["large_datasets = True\n", "max_samples = 10000 if large_datasets else 5000\n", "bptt = 10000 if large_datasets else 3000\n", "suite='cc'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device = 'cpu'\n", "base_path = '.'\n", "max_features = 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_models(model_string):\n", "    print(model_string)\n", "\n", "    for i in range(80):\n", "        for e in range(50):\n", "            exists = Path(os.path.join(base_path, f'models_diff/prior_diff_real_checkpoint{model_string}_n_{i}_epoch_{e}.cpkt')).is_file()\n", "            if exists:\n", "                print(os.path.join(base_path, f'models_diff/prior_diff_real_checkpoint{model_string}_n_{i}_epoch_{e}.cpkt'))\n", "        print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_function(config_sample, i, add_name=''):\n", "    start_time = time.time()\n", "    N_epochs_to_save = 50\n", "    \n", "    def save_callback(model, epoch):\n", "        if not hasattr(model, 'last_saved_epoch'):\n", "            model.last_saved_epoch = 0\n", "        if ((time.time() - start_time) / (maximum_runtime * 60 / N_epochs_to_save)) > model.last_saved_epoch:\n", "            print('Saving model..')\n", "            config_sample['epoch_in_training'] = epoch\n", "            save_model(model, base_path, f'models_diff/prior_diff_real_checkpoint{add_name}_n_{i}_epoch_{model.last_saved_epoch}.cpkt',\n", "                           config_sample)\n", "            model.last_saved_epoch = model.last_saved_epoch + 1 # TODO: Rename to checkpoint\n", "    \n", "    model = get_model(config_sample\n", "                      , device\n", "                      , should_train=True\n", "                      , verbose=1\n", "                      , epoch_callback = save_callback)\n", "    \n", "    return"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Define prior settings"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"scrolled": true}, "outputs": [], "source": ["def reload_config(config_type='causal', task_type='multiclass', longer=0):\n", "    config = get_prior_config(config_type=config_type)\n", "    \n", "    config['prior_type'], config['differentiable'], config['flexible'] = 'prior_bag', True, True\n", "    \n", "    model_string = ''\n", "    \n", "    config['epochs'] = 12000\n", "    config['recompute_attn'] = True\n", "\n", "    config['max_num_classes'] = 10\n", "    config['num_classes'] = uniform_int_sampler_f(2, config['max_num_classes'])\n", "    config['balanced'] = False\n", "    model_string = model_string + '_multiclass'\n", "    \n", "    model_string = model_string + '_'+datetime.now().strftime(\"%m_%d_%Y_%H_%M_%S\")\n", "    \n", "    return config, model_string"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Visualize Prior samples"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"tags": []}, "outputs": [], "source": ["config, model_string = reload_config(longer=1)\n", "\n", "config['bptt_extra_samples'] = None\n", "\n", "# diff\n", "config['output_multiclass_ordered_p'] = 0.\n", "del config['differentiable_hyperparameters']['output_multiclass_ordered_p']\n", "\n", "config['multiclass_type'] = 'rank'\n", "del config['differentiable_hyperparameters']['multiclass_type']\n", "\n", "config['sampling'] = 'normal' # vielleicht schlecht?\n", "del config['differentiable_hyperparameters']['sampling']\n", "\n", "config['pre_sample_causes'] = True\n", "# end diff\n", "\n", "config['multiclass_loss_type'] = 'nono' # 'compatible'\n", "config['normalize_to_ranking'] = False # False\n", "\n", "config['categorical_feature_p'] = .2 # diff: .0\n", "\n", "# turn this back on in a random search!?\n", "config['nan_prob_no_reason'] = .0\n", "config['nan_prob_unknown_reason'] = .0 # diff: .0\n", "config['set_value_to_nan'] = .1 # diff: 1.\n", "\n", "config['normalize_with_sqrt'] = False\n", "\n", "config['new_mlp_per_example'] = True\n", "config['prior_mlp_scale_weights_sqrt'] = True\n", "config['batch_size_per_gp_sample'] = None\n", "\n", "config['normalize_ignore_label_too'] = False\n", "\n", "config['differentiable_hps_as_style'] = False\n", "config['max_eval_pos'] = 1000\n", "\n", "config['random_feature_rotation'] = True\n", "config['rotate_normalized_labels'] = True\n", "\n", "config[\"mix_activations\"] = False # False heisst eig True\n", "\n", "config['emsize'] = 512\n", "config['nhead'] = config['emsize'] // 128\n", "config['bptt'] = 1024+128\n", "config['canonical_y_encoder'] = False\n", "\n", "    \n", "config['aggregate_k_gradients'] = 8\n", "config['batch_size'] = 8*config['aggregate_k_gradients']\n", "config['num_steps'] = 1024//config['aggregate_k_gradients']\n", "config['epochs'] = 400\n", "config['total_available_time_in_s'] = None #60*60*22 # 22 hours for some safety...\n", "\n", "config['train_mixed_precision'] = True\n", "config['efficient_eval_masking'] = True\n", "\n", "config_sample = evaluate_hypers(config)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using style prior: True\n", "MODEL BUILDER <module 'priors.differentiable_prior' from '/home/<USER>/TabPFN/priors/differentiable_prior.py'> <function get_model.<locals>.make_get_batch.<locals>.new_get_batch at 0x7f24bd339af0>\n", "Using cpu:0 device\n", "init dist\n", "Not using distributed\n", "DataLoader.__dict__ {'num_steps': 33554432, 'get_batch_kwargs': {'batch_size': 1, 'eval_pos_seq_len_sampler': <function train.<locals>.eval_pos_seq_len_sampler at 0x7f24bd493ee0>, 'seq_len_maximum': 1152, 'device': 'cpu:0', 'num_features': 100, 'hyperparameters': {'lr': 0.00011555441385381896, 'dropout': 0.0, 'emsize': 512, 'batch_size': 1, 'nlayers': 12, 'num_features': 100, 'nhead': 4, 'nhid_factor': 2, 'bptt': 1152, 'eval_positions': [1094], 'seq_len_used': 50, 'sampling': 'normal', 'epochs': 400, 'num_steps': 33554432, 'verbose': True, 'mix_activations': False, 'pre_sample_causes': True, 'multiclass_type': 'rank', 'nan_prob_unknown_reason_reason_prior': 0.5, 'categorical_feature_p': 0.2, 'nan_prob_no_reason': 0.0, 'nan_prob_unknown_reason': 0.0, 'nan_prob_a_reason': 0.0, 'max_num_classes': 10, 'num_classes': <function <lambda>.<locals>.<lambda> at 0x7f24c2d03ee0>, 'noise_type': 'Gaussian', 'balanced': False, 'normalize_to_ranking': False, 'set_value_to_nan': 0.1, 'normalize_by_used_features': True, 'num_features_used': <function <lambda>.<locals>.<lambda> at 0x7f24c2d03e50>, 'num_categorical_features_sampler_a': -1.0, 'differentiable_hyperparameters': {'distribution': 'uniform', 'min': 2.0, 'max': 10.0}, 'prior_type': 'prior_bag', 'differentiable': True, 'flexible': True, 'recompute_attn': True, 'bptt_extra_samples': None, 'output_multiclass_ordered_p': 0.0, 'multiclass_loss_type': 'nono', 'normalize_with_sqrt': False, 'new_mlp_per_example': True, 'prior_mlp_scale_weights_sqrt': True, 'batch_size_per_gp_sample': None, 'normalize_ignore_label_too': False, 'differentiable_hps_as_style': False, 'max_eval_pos': 1000, 'random_feature_rotation': True, 'rotate_normalized_labels': True, 'canonical_y_encoder': False, 'aggregate_k_gradients': 8, 'total_available_time_in_s': None, 'train_mixed_precision': True, 'efficient_eval_masking': True, 'prior_bag_get_batch': (<function get_model.<locals>.make_get_batch.<locals>.new_get_batch at 0x7f24bf3e8550>, <function get_model.<locals>.make_get_batch.<locals>.new_get_batch at 0x7f24bd339e50>), 'prior_bag_exp_weights_1': 2.0, 'normalize_labels': True, 'check_is_compatible': True}, 'batch_size_per_gp_sample': None, 'get_batch': <function get_model.<locals>.make_get_batch.<locals>.new_get_batch at 0x7f24bd339af0>, 'differentiable_hyperparameters': {'prior_bag_exp_weights_1': {'distribution': 'uniform', 'min': 2.0, 'max': 10.0}, 'num_layers': {'distribution': 'meta_gamma', 'max_alpha': 2, 'max_scale': 3, 'round': True, 'lower_bound': 2}, 'prior_mlp_hidden_dim': {'distribution': 'meta_gamma', 'max_alpha': 3, 'max_scale': 100, 'round': True, 'lower_bound': 4}, 'prior_mlp_dropout_prob': {'distribution': 'meta_beta', 'scale': 0.6, 'min': 0.1, 'max': 5.0}, 'noise_std': {'distribution': 'meta_trunc_norm_log_scaled', 'max_mean': 0.3, 'min_mean': 0.0001, 'round': False, 'lower_bound': 0.0}, 'init_std': {'distribution': 'meta_trunc_norm_log_scaled', 'max_mean': 10.0, 'min_mean': 0.01, 'round': False, 'lower_bound': 0.0}, 'num_causes': {'distribution': 'meta_gamma', 'max_alpha': 3, 'max_scale': 7, 'round': True, 'lower_bound': 2}, 'is_causal': {'distribution': 'meta_choice', 'choice_values': [True, False]}, 'pre_sample_weights': {'distribution': 'meta_choice', 'choice_values': [True, False]}, 'y_is_effect': {'distribution': 'meta_choice', 'choice_values': [True, False]}, 'prior_mlp_activations': {'distribution': 'meta_choice_mixed', 'choice_values': [<class 'torch.nn.modules.activation.Tanh'>, <class 'torch.nn.modules.linear.Identity'>, <class 'torch.nn.modules.activation.ReLU'>]}, 'block_wise_dropout': {'distribution': 'meta_choice', 'choice_values': [True, False]}, 'sort_features': {'distribution': 'meta_choice', 'choice_values': [True, False]}, 'in_clique': {'distribution': 'meta_choice', 'choice_values': [True, False]}, 'outputscale': {'distribution': 'meta_trunc_norm_log_scaled', 'max_mean': 10.0, 'min_mean': 1e-05, 'round': False, 'lower_bound': 0}, 'lengthscale': {'distribution': 'meta_trunc_norm_log_scaled', 'max_mean': 10.0, 'min_mean': 1e-05, 'round': False, 'lower_bound': 0}, 'noise': {'distribution': 'meta_choice', 'choice_values': [1e-05, 0.0001, 0.01]}}}, 'num_features': 100, 'epoch_count': 0}\n", "PRIOR_BAG: tensor([1.0000, 2.3162]) [1]\n", "{'is_causal': False, 'num_causes': 4, 'prior_mlp_hidden_dim': 6, 'num_layers': 2, 'noise_std': 0.0021951181710037487, 'y_is_effect': True, 'pre_sample_weights': True, 'prior_mlp_dropout_prob': 0.11217365522242403, 'pre_sample_causes': True}\n", "H<PERSON>ams dict_keys(['prior_bag_exp_weights_1', 'num_layers_alpha', 'num_layers_scale', 'prior_mlp_hidden_dim_alpha', 'prior_mlp_hidden_dim_scale', 'prior_mlp_dropout_prob_b', 'prior_mlp_dropout_prob_k', 'noise_std_log_mean', 'noise_std_log_std', 'init_std_log_mean', 'init_std_log_std', 'num_causes_alpha', 'num_causes_scale', 'is_causal_choice_1_weight', 'pre_sample_weights_choice_1_weight', 'y_is_effect_choice_1_weight', 'prior_mlp_activations_choice_1_weight', 'prior_mlp_activations_choice_2_weight', 'block_wise_dropout_choice_1_weight', 'sort_features_choice_1_weight', 'in_clique_choice_1_weight', 'outputscale_log_mean', 'outputscale_log_std', 'lengthscale_log_mean', 'lengthscale_log_std', 'noise_choice_1_weight', 'noise_choice_2_weight'])\n", "Style definition of first 3 examples: None\n", "Using a Transformer with 25.82 M parameters\n", "PRIOR_BAG: tensor([1.0000, 7.0192]) [1]\n", "{'is_causal': True, 'num_causes': 2, 'prior_mlp_hidden_dim': 10, 'num_layers': 2, 'noise_std': 0.0031679113358953426, 'y_is_effect': False, 'pre_sample_weights': True, 'prior_mlp_dropout_prob': 0.009754962364049987, 'pre_sample_causes': True}\n", "H<PERSON>ams dict_keys(['prior_bag_exp_weights_1', 'num_layers_alpha', 'num_layers_scale', 'prior_mlp_hidden_dim_alpha', 'prior_mlp_hidden_dim_scale', 'prior_mlp_dropout_prob_b', 'prior_mlp_dropout_prob_k', 'noise_std_log_mean', 'noise_std_log_std', 'init_std_log_mean', 'init_std_log_std', 'num_causes_alpha', 'num_causes_scale', 'is_causal_choice_1_weight', 'pre_sample_weights_choice_1_weight', 'y_is_effect_choice_1_weight', 'prior_mlp_activations_choice_1_weight', 'prior_mlp_activations_choice_2_weight', 'block_wise_dropout_choice_1_weight', 'sort_features_choice_1_weight', 'in_clique_choice_1_weight', 'outputscale_log_mean', 'outputscale_log_std', 'lengthscale_log_mean', 'lengthscale_log_std', 'noise_choice_1_weight', 'noise_choice_2_weight'])\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAdAAAAHECAYAAACJGnuNAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/MnkTPAAAACXBIWXMAAAsTAAALEwEAmpwYAACbOElEQVR4nOzddXhc15n48e+9w8ySRizbsszMdsBh5obTNsV0S7/iFrew293ilnELaZumDTbcMNuJY7ZjtixZzBrmuff3x9hjy5It2RbY0vk8T54nvnPhaO7MvPfQeyRVVREEQRAE4eTIY10AQRAEQTgbiQAqCIIgCKdABFBBEARBOAUigAqCIAjCKRABVBAEQRBOgfZkdvZ6vWplZeUIFUUYThs3buxSVdV37HZxD88e4h6e/cQ9PPsd7x7CSQbQyspKNmzYMDylEkaUJEkHB9ou7uHZQ9zDs5+4h2e/491DOMkAKowf0UAP8eb9pLub0Dj9GPxTiKgm9jT0EkukqS5zMqXUiUYztFb+5s4Iext6MRu0BCJJUukskwuMlOp6SAV7Qc2gMxhJBzrImj20SgVEMWGQVRraI5QW2tDo9PSEkpgMWtp7Y9jMOgpcZupagmhkiUl+K24pzL7OLN1RlckVXtq7YwQjCaaUuWjtjhKNp6nyO9BqJBLpLIFwklA0RaHLiFmTpdgOSDK6tnfRFlTRonipbQky2SNTIneS7m5FshfQrimkrldiWoUbo1HD9v3dxBMZygptlBRYScTTaGKdWCKNZJMxsq4KMs4yKopdJ3UfOnpj7D3YS1cwwaQSO9VlLkwG8bU8WdlokERrLemuJnSeYgzFU9BanGNdrEFlwj0kW/aT7m1H7yvDUDwZjck2bOdXUgmSrbWk2uvQWN0kHZWooTZ02ShKsBONzY3W7iXZvAeNyYbePwmjf0qfczS1h+nuDdPQGsSgheoSOwlFwppoRxdoQGe2ETSVsqUNirxWTHoNB9vClPosdPXGSCUSTCt3UKLpIdVWi6TRoveVoUoaMr1taO1uUl2tZPQWeg0l9Kg2SnwW2rpjNHeEKXKZSKUzdPVEKC6woTcaqGsJ4zDJTPVJeKQQ6e4mkGSy3sls6TbS3ZtgcpmTGVWeIX2fWroi7DnYSySWYkqpkyllTnRazaDHiW/qBJRJJQm+9RiJTU8f2TbjPB6JLuKFzZ0AyBJ86yPLmTe1YNDzNXdE+Npv13D+gjJe3dRIVyABwO3nFGLreQw1EcFSvYjud57MH+Momc1buotJakw0tkfY++x+3nd5DZKs4WcPbsnvV1pgZX5NAU++cQCjXsMHrp7Jrx7ZyRUrKnn5se0cbAtz2yU1/O/9mwhEkgBoZInP3LaAh17ey8HWcP5cd1w6jRc3dHD3uV5CxmLq62P89Jm3mOS3MHt6C71bHs/va6lcxL7USv7wxA4+eM0sfv/4dhQVtBqJD14zi2p7As0rPyYc6ckdIMmkVn+a3ekZTKtwD+k+dAfjfP+vG9hzsDe/7eM3zeWy5ZVDOl7IUdJJetc8Qmj9kc+zbd5FuC9+Pxq9aQxLdmLZeISu5/9AbPfb+W3Oc27GtfJGJM3w/DRHd79F55O/AEB74ceh7jlsTjvdr96f30fnKcVUNYfeV+9H5ynBd82nMBbngmh9S5CG1gD/+4+tZJVc0h2v08h/X20n/vSPSJPbJtt9GKa8n+/9eRfvv2omyXSWnz6wlUAkiSxL/PrOQtqf/iGoCgAaiwPPpR9GY7HT/sB/58uid/ppKb+DSKyEXz68lfk1PrYoKht3d+T3uXRZBfsaAxxoDlLkMfOFpQm0b98LgKTV4175aX7ybDsA99wwhytXVp3wPWrtivKN371NW3c0dw4Jvnb3UpbMLBr0/RWDiA5p74mxZlsLwUM/wuNZtK2RxKZn+mxL7HyNhX4l/29Fhfuf2008kR70fLvqu+kNJdHIUj54AiwryZBu3Yd1+jJCm57rc4zavJ3F/jSvbGhk4bRckO4KJnnijQN99mvqiGA363NlTGXZVd+D32vBaTNwsC2MyaAlkcrmgydAVlF57u16XDZjn3M9teYALreDpt4UqtXLva/nHhaunmOFbU/1LV/9BlZW5M711vZWZk7yApDJqmzc3Y4z1oByOHgCqArmXU+zd38rQ83uVd8S6hM8Ae59agcdvbEhHS/kpLtbCK3v+3kOb3mRTFfzGJVoaFKdjX2CJ0BgzSOke1uH5fyZUDfdL96b/7dscWKSU4Q29H2v0t1NaMz2Q//fTKqtLv9aXWuQ59Y15oMnwPxKK+l1DwBHtimhTiYbc5/llzc04rIZ8t/JhVNcqFufygdPyLUYJFv39yuzEmhlTpHEm1ubyWQVqsucfYInwAvvNLBkRiEAbd0xGtXC/GtqJoW7dS1Vxbm/5y/P7KSj58Tfp/2NvfngCaCq8KendhCOpk54HIgaKABvbW/l+3/dQCarYDXp+M97VjCl1DnWxRoxSjrJ0R/+w7T0DZY9oSTprMJgz/CRWBqtRiaVyfbZrlPTpABkDWqm/4dRq6ZRVPIBx2TUERrgQ5tVjnzxQtEUVpMO5dAX2qDXDBjke8NJKv32PtvCsTRmo5ZkKoGCRCSWO04nZUHJ9juHRs29HowmKfVZ89tjiQxqMtL/jYgFULNpFEVFo5H6v36MRKr/NWPJDOm0MsDeZ6ZgJEldS5BQNEWx10plsR3tEJv9BxKKJmnvjqHXayj2WobUjKZkBv485z7no6O9O0o8lcXnNGEx6YZ0jJpJ9N+oZFHSg/9wD+n82TRK4lDwkGTUTArZYCEb6//ZVY/6/CvpeP7/48ksvZG+5XGaNajdoX7n0GbiyHLuO3z0M6TLokEJB/rtryRjSFL/z4pWKxGK5u5dNjvAfVX6bktkJJDkI7XbWC92k/ZQ+TOksyf+PsWSmX7bAuFkv9+zgUz4Gmhje5jv/3UDk0sc/OdHl2MyavnhfRvIDvKmn82MXj8aT0mfbRqbm4PRvqHyypVV2C2GQc83tcJFKpPFZtYjHRU3mlN2JK2eVFsdxrIZfY6RDGZaM3ZKC6x0Hqq1BsJxzlvQt1xajdTnR3RmlYcDzUE0GhmdViYQTlLosfQr0+qFZew40N1n24rZfnbWdVPsNSFn4lwwxwPAtnbQFE7us69sttOUyAXNJTOK2Lq/60gZJnnIeiYDfYNkuno1vqKCIfcblxZaMej6BohVc0vwuozHOeLMEowk+d1j2/n6b9/iB/dt5LM/fY13drb12y8bCxOv30743TdINO87boBoaAvx9d+s5bM/fZ1P/ehV/v78nvwP6YnoXEVo3cV9tmntPrSuwZvgTlcyleHFdw7yqf99lU/+8BW+9fu3aWgLD34goHMXI5v7PuTp/dXonIXHOeLkaGwerLPPy/1DVZB0RhLtDVinL++7o6w90mQsa9H7KvIvlfmsXLiw73v72q4Q+lkX9z2HJNOjLySTVVgxx9+nxvr23iD6WRf0K5++sApVPeZ3VtbQFlZZND33HiTTWZzWvr9BlX47rd25BwNZglJTrE/tNlKxkl0HAwAsn12M13Hi71NVsQP5mOfdK1dW4bYP/j2c0AFUVVV+9chWTAYNX/vAUuZNLeAj182muTPKi+sbx7p4I8bkcOO66jPop65ANtnQTlqE49ovUlVdSaXfhttu5H1XzuDc+SWDnwyoLnPyHx9Yyu76bj507Syqiu24bAZaEkZc13+RTCKGacp8LLPOQzbZ0JXPJnrOp9jTo+X68yaxaXc7lywuYfksPz6HiUuXVeC0Gpha5uQzty1g/c42Ct1m7rlhNtNLDEwrs/P65iY+dfM8aipcbNrdzsdvmktZYa7sd142jUq/jdsvncbUchcOq56Ll5QzrcLJe88vxmLUoX33aa6eLnPDeZN4Z3+Irlm3YZqZK5++aj6RVZ/gX9vCfPCamcyc5MZpNeB1GnnPhdXMmeLlYMaN5tJPo/OVo7G6kBa9h0TpQmZN9gz5PlQU2fn2R5czc5IHu0XP1auquOvyaRh0Z0fDUH1riNc3H2kmVVX49cPb6AocqcFkE1F6Xv0brX/7Jp2P/4SWe79EdNfafufKZLI89NI+DrTkajaKovLQS/vY3xgYtBxai5OiGz+PZcZKZJMNS81SCm/+Ejr70O/FqTrQEuSnD2whlsjVYnbV9/Dnp3eQSPWv1RxL5yzEf+vXME1ZiGyyYZ2zmoJrPoHGZB302KGQtTpcq27CsfQaZLMdee+r6JfciLZ4KrZ5F6GxONAXV+O7+uNEdr2F3j+Zwhs+i6Fkav4c1eUuJpe6ef/lU/E4jJQVWHj/FTX0Fi1CXnA9GqsTXUEl6oWf5tFtGW5cXU1FkZ21W1v48HWzKCu0odXItFtrcJ1/OxqbG523FM/lH0XnLiHZVo998VVoLA50RZNJr/40G1p0zK32cuvFU1m/s53bL61h0TQfdoue8+YVc+tFU9m2v5PJJXa+dvtMKlwSWlcRWrsP20UfYmPQi9Gg5YoVlbz/yhkY9Cf+Pk0ucfDNDy9ncqkDp83AbZdM5dJllUjS4K1I0smsxrJo0SJ1PA29fre2iy//ag0fvX42V62aBOSC6ud++jqJVIZffuGCIb2JZyJJkjaqqrro2O1H38NMKkk8FERntmI0mwGIxFJksgpO28nXgiLxFOmMgk4rk84ouGxGMpEAKhCNJUFvwapJEVO0hFMyOq2ENhMni4zJaiEcSaPV5mqWoWgKo14mlVZQVRWtpOA2SiQzadJZiSR6zEZd7tyJDGajBgmZSCyFJIFGklBUhawqodVI6EmhVTOkNWa0soKRDEa7HVXKjRo2aiX0mQjZbJaEZEKRdUgS+fchHEsRiaewm/VYTLk+2UA4gV5NomYzZPXWIdXWBxJPpImnsjisBjRHPQoP5R6OpTe3NvO9v/Qvx6++eAFlhbmRpPHGXbT+5Wt9Xpf0Jko/9CN0riM1rZ5gnE/88BXCsb7N8R+8ZhbXnde3deB4lEwKJR5BNlmRtfqT/XNOyUsbGvjJ3zf32SZJ8LsvX0SRxzKke6ikkyiJKBqTHUk7/A9PqqqQjQaRdAY0BjOxaBwlGcGgAdloRdYbSPd2IGn16OwDD4CLxFJEowmMWgXzoRbq3oSEkSQGk4lAQgYJDFoNJpOWcDQNqoqSSUE2jaw3YTTq0CV6UDNpMlozWhm0Wg2y2Y4SC6HKOsJpDRaTHoNeQzSeJhRNYjFo0GTixDKgM5pJpbOoqoJZzmCxWVCScdRMCkmnRzLa6QnFURUVj9Pc5/s0mGg8TSqdxWkz9PndP949hAneB/roq/tztZOlR5osJEnikqUV/PLhrdQ2B8d1X6hWb8Dm7TvK1mo+9R8eq6n/sVqrEwDnUQ/V9kP/5RxpfrWajgSg4wXwgUKUy370/59c4JfgqKYaA9rjXMNm1mM75r3JlfH0m1tNRh0m49D6zc4kxV4rsiz16ZOaPdmDx3nkPVHiA/S3peIoqb4DOywmPTUVLjbs6jtgxO81D7k8slaPbBvaCOjh4rL2/7SU+qyYT+J+yjoDsu7UHr6GQpJktNYj06vMFhNY+nbX6N0nbu62mvX9fhsKjvpOFx7Ti2JwHg4tx9w/s3/A88uHynf03bOYdEf1J5vo31Fz6NhjHpZ8zqF/Zo7W93pDM2GbcLuDcTbuaueyZZUD9EMVo9XIvLqxaYxKJwhnvooiG199/5L8A8icKV4+esMczIYjP0JaVxGSpu+Pkq6gkqTO0WebQa/hzsum47QdCSTnzS9hatnJzasdbZNKHJwz70gfoU4rc88Nc7BbRqcGLIytCVsDfWVjE4oKFywu6/ea1axnbrWX9Tvb+NC1s8agdIJw5tNoZJbMLGJKmZN4IoPbbuhXk9Z7Sym8+ct0PfNbMsF2NMXTaK2+nm/+agOfv3MR0yuP1Dkmlzr50afOpaUrglGvpazQdtI1gtHmtBm554Y5XL68ikg8RbHPSnnh8CVCEM5sEzaAvrapiemVboq9A3fYL6gp4P92v0tbd5SiAUZ5CoKQ47Ybj26T70OSJMyT5mK64T/YuP0g21tSvPlYM4oK/3v/Rn7wyXP6NNcXuM0UuE+tCW6s2C0GZk8ZuSZY4cw1IZtw23ti1LeGWDFn4PZ4gAWHJvdv2tNx3H0EYaJJZxR21nVz37928vDLe6ltDg7puP3dKr98vo3X3+3hcJdpW3eMntD4T1wijF8Tsgb6zo7cXLUlM47fcV7is+J1mti2r4srVpw4FZQgTBQ76rr5j9+uzU+Uf+CFvXz346uYPMhgO88Ac/EcVj02y5ndRCsIJzIha6Dv7GyjtMBKse/4860kSWJGlZtd9T1DTs0mCONZKpPl4Zf29ckyk0hlh9RKU15o472XT8//W6eV+fQt8095xKQgnAkmXA00lkjzbm0X15wz+Nyy6ZVuXt/cTGdv/KzrlxGE4aYoKtF4/yxC0fjg+ZKNBi3XnDuJeTUFhCJJCtxmSk7wACsIZ4MJVwPdvr+LTPZIqqgTmXZohODO+p5B9hSE8c+o13LdeX2XupIkWDxjaKnnDHot1WVOFk4vpKzQhjzIJPdsVmF/Y4BXNjSybkdrnwxHgnAmmHA10G21Xei1MtMqB59fVuW3Y9Rr2F3fw/kLSkehdIJwZls4rYDP3b6Ax16rxWTQ8p6LpjK1/PTnaiqK2i+gbtnXybf/sC6fqGFapZt/v2sRXueZu0SZMLFMuAC6fX8X0yrdQ1rlQaORmVruYpeogQoTSH1riK37Ookl0syt9lFd5kKnzTVWWc16zl9YxrJZfmRJQq8f/Ht0IvsbAzy37iDNHWEuWVrB/JoCHFYDoWguUf3RWY521/ewvykgAqhwxphQATQcS1HfGuKOS6cN+ZjplW4eemkviWQG4xBWNheEs1l9S5Av/fJNooeSo//9+T1888PLWVDTN+XjqXwXVFUhG+4FrRat2UFDa4iv/HoN8UPLSW2v7c7nvk2ksnT09G+yjcZO3N8aCCfYfbCXupYQFUU2plW6cNtFwBVGxoSKCO/WdqGqMGuyd8jHTK1woahQ2xxk5qSRX91BEMbS1v1d+eAJuRVWHnhhDzOq3BgHWdXiRDKhbkKbnie44Rk0RgvuC9/LgVhZPnge9uCLezhnXjFum5ELFpby2uZmtFqZaDyNJOWWgDueZCrDAy/s5ak1RxaEPm9+CR+7ce4Zn9FIODtNsADajV6nOak+m+oyJwB7G3pFABXGvdgAi5On0lnURIR0PI3W6uyX2/ZEwrEUHT0xpEAbml1voSZjZJIxOh79EVz8/X77y7KEBGi1Mpcur6TAbSYQSVLksVBaYGFSiaPfMemMQjCSJBJP8ezb9X1ee21zM1efM4maitFNMi9MDBMqgO4+2MPUcme+P2coXDYjPpeJvQ29I1gyQTgzzK328ffn9+TnehZ7THz5YjNdf/s6mWAn1tnn4VxxPbpBFqtWVZW6lhC/eGgz+xqD6LQyt626g4XWF6Aht/xXmTmBzazrs4TZ7ZdOw+0w0dge5j//sI5AJJepSJLgPz64FJ1WQySWoieUxGrWEU9mePSVfbyxpYXSAit3XzWTh17alz8OIJk+ZtFmQRgmEyaAptJZDjQHufbcoa0teLSpZS72DmFhX0E4m7V0RohEU3zxzkU8+eYBIrE0X72mgMij3wI1F4TCW14k4Z5CmwN6wgn8HgvVpc4+SeSbOsI0dUR47u169jXmUv2lMwp/ebWNiqtX4z0UQLNd9dxzw1zqW0O0dEY5d0EJsyd7yWSybK/t6hMEVRXue3Y3XqeJn/5jM/ubgqyaW0wynWX9znYA9jUGONga4sYLqvn783sAKHSbKPGJXNbCyJgwAfRAS5BMVqWm4uSH3E8td7JmWwvBSBLHAOv/CcLZrrE9zNd/u5buYAKNLLGgxseHrp2FLbCNhHpUDW7ycv6xy8DrO9blN33w6plcfe5kQpEkr29p5vHXa7lsWSXb93f3u05HQocXCdnupTbh4VdPbuTzdyzgvVfMAKCzN84DL+wZsIyhaIpXNjaxvykXlEt8Vh54cW+ffVIZBYtJh8dhZPZkLzeunoLHIQYRCSNjwiRS2HMw1wR7KnPWqg8ds0/UQoVxauu+TrqDCQCyisr6XR3c/9xuJH3fDFy9hYt4fUff7oy//GsXrV0R1m5v4fePv0tnb5zdB3uo8PdfosXlccL136F51ZcJaVxcvrySF945mH/9re0tPP76AZw2I8fmWbhqZRUvr2/M/zuZzmIx9q8DlBVY+elnz+fTt8yjsrh/n6kgDJcJE0D3HuzF6zSd0tPolFInsoToBxXGre5Aot+2tu4Ysq8Cnb86vy2e6f+Tkc4oRGJpnnj9QH7bxt0dnDe/pE+AO2deCT6PnV8918x379vCfc/uZtv+Ti5eUsmm3e30hOK8cmgR+xfWHeQDV8+iusyJ32PhI9fNZtH0QlKZbP58b2xp5qpVk/qUZc4UD1XFDhxWA9oB5non01kyWdEnKgyPCdOEu7uh95SabwFMBi2lhTYRQIVxa85ULw+/sq/PtsuWV2J2F7Bt5l1YJ7WgVxLYfKWUFKSYX2GmJ5pl3e5eKotsuDURXDYDLV1RIJdZ6P7ndvOpW+bTFYwjSxLzyw2s336QPU3h/DWaO6Psa+zlre2tLJ/lp7rcyf6mAC1dUe59egfzpxZw0wXVrJhTDMDHbpjD//59E6oK3cEERr2G/7pnBS2dEZw2I9VlDlz2/iu/BMIJ1u9s55m19fg9Zq45bzLTxMhc4TRNiADaG07Q0RPjqpWnvizZ1DIX63a0oaoqknTiHJ6CcLaZVuHms7cv4M9P7ySezHDduZNZNTcXtEJY+M6TMQBuW9XBN5YFSG9/GMwubrv1WjJZhcjfvsiN5/07u+qPrPep1co0tIf527O70WpkfnNXIXva+0+T2VXfS7HPyuNvHOC//20la7e1EIykyGRVmrsifZqCV84tprTARmt3FJfNQKXfgdWsY26174R/38sbGvnTUzsB2N8UYN2ONn7wqXMHnBYjCEM1IQLo3kP9n6daA4XcQKIX1zfQ3hOjyCNG9Qnji8mgZfXCMuZP9ZHJqngcxvyD4txqHzdfWI3VpGNJdgPxNx/OHRTshLYf4TnvVnqzaXyb/sB33vtJagNaTAYtNpOOH92/6dAVVDKpJHOKdby5o++1F9V4eeatBiCXQP4HnzyHg21hNLJEpd+Oz3WkH1an1TClzMmUQ/Ozh6I7FOeRV/b32ZbKKBxoDogAKpyWCRFA9zUGkGVp0EV/TyQ/kKghIAKoMG45bf2bPxPJDFv2dbKw3EC67rm+L6oKaioBkowSaMW7+5/MuvlLADz95gFuWD0FFZAlyNp1VHduYPXsEl7Znhuhu3iaF1mjIRBJ4rQZKPZaKXCb8XuPZByKJdKk0lkcVgPdwQSqquJ1mobcEqSRZPS6/n23Ws2EGQIijJAJEUBrm4OUFVgx6E498XWl345OK7O3sZdz5pcMY+kE4cwVS6T57WPvsrchwJQCH5LBCvFwn30kjZbDmRcs01fkt5cX2fjPP76TT9e3tsjG125ayK3eZi6dVYZkdpJUdfzwgXdZOrOI2y6pocBtJpNVqG8N0dUbAyQefGkvXYE45y8oJauokAhz2VSwywkM3hIilhJ21AXYcaCbqeVO5kzx9Vm/12kzcNfl0/nx3zfnt9ktejwOIwdbQ5QUWEUwFU7JhAigB5qDzK0eev7bgWg1MpNLHGIgkTChBMJJtu/vAuDlbT1ceOX1aF/7Vf51jc2DqmSQNFocy67FVDUHyA0ienlDU59ct/VtYd44oHDJsuWEOiNoNTLlbjM//7wPq1GXX9llw652/ufed7jz8un87dnduaAJ/PO1Wq49p4rLbTtJ/utfdANy+Vwe01zOSxtbAHh+ncTKuX7uuWEuNrM+f+1ls4v5ptXIOzvacNr0aGSZb/1hHZmMwifeM5fzF5aJICqctHEfQAPhJD2hxLD0dVSXu3h+3UGyWQWN+LIJE4DZqKXIY6atO0YileU361TuOuczlNCGyeHGUFKDpNFgm3shWoePVBY0qkpGUTjYFup3PpfdyLd+/3b+QfS8+aXcfdWMfPDsDsb59SPbUFRIprL54HnY8+80svpcK4cbbyOly3jpqVzwvGaRh5VFMYzhbaT2xclMmonWlstfbTZoWTitgNICC1/51Ro6eo+s9PKLh7ZSXeYacN6qIJzIuI8CB5pzWUuGI4BOLXOSTGVpaA8PvrMgjANOm5F/u3Fuvna2ryXKa81mjAuvxjb3AvTeEnSuIroVGw+/UssXf/4G//fYdtq6oly8tKLPuYo8Zg60BPu04ry2uYlt+7vYWdfNKxsbqWsOUlWcC2QD1QjtFj1S8khgzqi5fRZOcXCRdgPGtb+F7c8QfOqndL9wL9lEtM/xPaFEn+AJucQRPaH+82AFYTDjvgZa2xwAYNIwZCQ5nMVob0OAKpHhRJgg5lb7+MlnzqOlK4LNrKfCb+/TPJpIZbj3qR2s2dYK5B5a3363lW9+ZDm3XjyVf75ai8mg4e6rZvK3Z3f3O/+22i7efb6Ltu7cVJnLllewfHYRiVQmX/s97LaLJiOvuZ/DqRAcPTuYWTGXC6r1qOve6HPe6K61OJZeg6bkSCIIl82Ixajts2SbViOLRbqFUzLuA+iB5iAFbjPWo77wp8rvtWAx6djX2MulyyoGP0AQxgFZlqjw24/bxNnWHc0Hz8M6Awm6euPcenEN82sKONAcRFEU5lb7+rXguG1Geg6lEbxoSfmhNH4Ser2GWy+uIZnKkkhlkGWJVza34F/2ISxbHiDb24IhG+Pj108n1dU0YNnUbKrPv4s8Fr5w1yJ+8NcNRBMZjHoNn751PsW+468zKgjHMyEC6ORhmuslSRJTy5xiIJEgHEWWJWQJjumuRKOR2dsY4Ku/XptPn/e+K6dTUWTjYFsuiC6ZUUgoliKVUVg4rYBAOMmL7zTkz7Fyjp9Ll1Xgd1voCMa5/7k9fL1O4eJ5t3LeKg9Fk8owW81k3CZaN1aSbq/PH6t1F6NzF/cr79xiDb/98GQSshnV7KHQYxbJUYRTMq4DaCyRpqUryupFZcN2zupyFw+/vI9EKoNRP67fPkEYkmKPhatWTeKJN47kwp1S6qC8yMbv/rm9T+7Zvz6zi8/dsRCPw4hGlrFZ9Hzjd28BuUQn9z/XdyWWNdtaWb2wjL+/uIcl04v40afPpbM3hs2ip6zAhtGQ+w5qLXYKr/sMoY3PEtu/CVPlbOxLrkRr7Zs8JV6/nY7Hfkw2GkQ2WvFd/QnwLAREABVO3riOAHUtucEGw5ltZGqZE0VROdAcZEaVZ9jOKwjDobUrQm1zkExGodJvH5XVSLRaDTddUM20Sjeb9nRQXeZk/tQCLCYdXYG+A3YUFdp7YrjsRg42B4nE03zg6hn889VaFEXF5zSh1ci0dh8Z/LO/OciB5hBVfgd+n4UF0woHLIfeW4rn4rtxnXMLstGMJPed950OdNL+6I9QDs1jVRIROv75v5R88AfovaXD/K4IE8G4DqCHR+AOVxMuHMlItLchIAKocEZpag/z9d+tpevQyioGnYbvfGwFNaOQNN1lN3LOvBLOmdc3ycgVK6vYffBIl8fi6YV09cb5yjNrALCZddxzwxyuPqcKh9VIbzhJIpXl8hWVuaZcKbdGaH1riDXbWlhcqScdTKFzFAxYDknWoDHbBnwtG+nOB8/D1EyKg7X1KFHzqLxPwvgyrqex1LUEcVj1uAdYneFUue1GvE4Tuw/2DNs5BWE4bNnbmQ+ekFu669FX95PJDN/yXdl4mFRnI5lI33EAnb0x1m5r4ak3D7C9tovEoQQKC2sK+NiNc/C5TBR7LaxeVMYzb9XnjwvH0jzyyn5SaYVv/t/bPPf2QV7b1MSfntrB9edP5tx5JbyyIdcnuvtgL+HabTT//guE330DJdM/Mf2JyGY7ks7Qd6Mk0xzW8JVfraG2KXDS74cwsY3rGmhtc5BJxY5hHyAwZ4qX9TvbURQV+dhVfwVhjLT1RPtta+6IkM5m0WpP/1k50bKfzqd+SbqzAY3di++qj6MvnEy8p439B0P89Y0umjpzU04+ftNcLlteid1q4IoVVaycU4wsS7yzo63feUPRJAeag336SlUVXt3URCar5gcnFblN6KNNKIkInY//BJ37uxiLq/ud73h0Lj/eKz5G55M/ByULkoy65DYe3RwllVHYc7D3tPJlCxPPuA2g6YxCQ1uIa8+dPOznnjfVx8sbGjnQHDypVSEEYSQtqCng8aMWtQa4dFklJoPutM+difTS/sgPyIZyaf2yoS7aH/wfXKvvpOeFP1Kk0fH5+dfzt7oC1u8Lce9TO1hQU4DTaqA7lKC5M4JGlij2WZGkfOpcIDe/OpHK9rtmOqOg1cpUFNk4Z14JbotMIG3EWTobmraT7mk7qQAqSRLW6cuRPaXU7qkjqJh4ZFOUurbcg4d4GBZO1rgNoI3tYTJZlcklzmE/97xDaw9u3tshAqhwxphW6eaTN8/jz0/vJJnOct25k1kxxz8s584EO/PB8zA1k0KJ5cYZqNk00oYHuXrVp1m/D2LJDM1dETbubucfL+zNZ/qp9Nv50nsX8/2/biCrqBS6zcyo9KCoar/Aeu25k/E6DOxrCvKbR7fnt79nxWWcU5xBYzl+6r3mjggHWoJkFYUqvyM/h1XSaLH4q+hp0eaXWnNY9ayaU0JNuXM43iphAhm3AfTwAKKqkuHPb+myG6n029myt5P3XDh12M8vCEOVDnSQjQXRWlyYHV4uWVrBoukFZLMqHodp2GpVstGKpNWjZvomJpA0fWu3tnQ3sqxhyfRCXlrfgCxJfdLk1beG6A4m+NnnzycSTdMZjPPTf2ym0G3mA1fPZMveTlLpLNeeO5nZ1T56Q4n8QtiHPbS2nYW33oCk1aNk0sjavmVobAuxbmc7yXSuVtveHSOTzTK59MiUlqUzi/j6B5ZS2xxAq5F56s063t7Ryu2XTGPFHP+wJF4Rxr9xG0DrWoPodZo+6woOp3lTfTz1Zh2JZCY/F00QRouqqsRrN9HxxM9Q4hE0Fge+a/8f5qo5uO3Dk5ZOSSVINO8lXr8NnacU98V30/2v3+Zfty+8jFjt5j7HpPQOLlvmobTAxoZd7YSiqWNPy/6mXq4+ZxIA8WSG//m3lfSGkxS4zFyxohJZlvN5cBvjaZIDNO+Gkiqtf/kaRbd+DfPk+X1ea+yI8OCLe/MrwVhMOnwuE1XFzvwDhcmoY8nMIlKZLN/7y4b8sT9/aAs2i57ls4en5i6Mb+N2FG59S4hKvw3NCPVrLJ5RSCarsHFPx4icXxBOJN3TemhOYwSAbDRIx6M/JB0Yvs9jdM862u7/FsG1/6TryZ8T2fU2/ju/TcENn6Porv9CP+Nc0l2N+f1NNUspnT6TD14zk017Og7Nle4/NWTmUdO/TAYtNRVuls3yM6nEgV6n7ZNE3ucw4jsmT61Bp6HAmtund80jKOlkn9ff2dnWZxm1aDzNrroeskr/0cjPrzvYb9vLGxv7bROEgYzLAKqqKnUtwRFN+D6zyoPNrOetY3KACsJoSAY6UY8JHEoiSqynfVjOn4kE6Hn5r32vWb+VbDSIbvISnqvV8dHf7WfnrE8QWv4xLNd/jYIrPobdV4hep+X68ycTiiZR1VxzqSTlBulcuLiM9p4YiVTmOFeGYCRJT2+YZFsdpnADn7+xmhJvboFst93Il26eik8OIxutqKkEqnL06F21X/IGgEAkiU6r6be92Gvpt83v6b9NEAYyLtseuwIJwrH0iAZQjUZm2awi1mxrIZ3JDvjlFISRktJYQNbkpmMcImn1xDEzLJ96JYuS6h+I1GyaxvYwf3xqBwC/fiE3LcVmTvHjz0ylMBfnmFbh5gefPIfdB3vxOIysXlhGU0eYt7a3Ut8S4qYLq8lEAkgaLRpTrpslGk+zbkcrf3t2N8tnF1NZaEKTyTCzIM2XF/eScFZizERQX/suiSnzMdcsxVQ5G43hSA1VkiQuWlzO1n19BzydN3/gTEMXLq7os/C3yaDlvPklA+4rCMcalwG0rvXQAKLikV0gd8WcYl54p4Gt+7pYNH3g9GKCMBISJi/q0juQ3r4PVAUkmezSOwlpnBQNw/k1NheOJVcTePOh/DZJo0NXNJlkWMFk0PZpJg3H0oSiaQrd0NoV5em1dby2sYmSAivnzivh149uJRjJ9Yf+6MNzSG56io53nkI2WHCvvhPTpHnsrOvhx3/fzFWrqthe28Xjr+e+xz+7owiHLou8/q9IWh3WpVcT2f027vPvwFDcf5ragmkFfPT62fzjhT1IksTtl9Ywd6pvwL+zuszJ9z+xitqmIEgwpdQpFtYWhmx8BtCW3BevcoS/CHOrvViMWt7Y0iwCqDCqCjw2npam4V76WSxqlIhkZXeXgdsXD8+gOUmSsS+4BNloIbz5BXS+CoJzb+N3L7fT0BbmqlVVdPTGeW1Tbhkxj8OI224glcny9+d388rG3PZAJMn+xgAfunYWL29o4MbV1fiCG+l5+T4AspEA7Q9/D/+d3+b1zQlkCWxmfX4UvUGvwZbpJfDmw/my9XQcxH3BXWjtHjTG/s2tdouBq1ZNYuWc3EosrkEykVUWO0YlZ7Aw/ozTABqiyGPGbDz9CeQnotNqWD67mLXbW0ils+h1ohlXGB06rYbViyvZc9DBvpYgpT4rNy10Y7MYBj94iLQ2N86lV2ObewHNvRm+/vM387XOPQ29XLmyiiKPmWQqyxfuXITHYaKlM5IPqocl01ksJh3/9bGVaNJxmv/0bL9rJRp3Ul40F51OQyp9pFl6bpWTxI4n++2f6m7GseSqE5Z/sMAJkE3ESPc0o2Yz6NzFaC0ikApDNz4DaPPIDiA62rnzS3hxfQMbdrWzYk7/tQcFYaQ4rAaWzCxiyczhaLQ9Po3RQn1bU58mW4CX1jfwnY+txOMw4nHk+iG1WhmTUUc03jdPrcmgQa/VoChaNDYXmd6+g+80ZgeLJhXy2Gv7qTyq6yWSzKJY+3+XMwYnDZ0xKopO3Mqkqgqhrm7CSQWH3YLNfqSGngl10/3ivUR3rQVAV1BB4fWfFSuzCEM27kbhJpIZWrujoxZA50zx4rQaeG1z0+A7C8JZaqCEDBqNjNt+JHgCFLjM3H3VjD77TS1z5r+Pst6I+5ybQTry06OxOjGWz6Cq2MH3PnEO2/Z1cctFU7GYdOxuCJCZdnFuwNQhkt5EvW4yawcZAZ9NxNi1s55v/mUbH/vx2/zH799hz4Ejo5TjDTvzwRMg3XGQ8OYXUNXhS74vjG/jrgZa3xZCVUd+ANFhGo3MqrnFPLfuILFEesSbjQVhLEwqduC0GQiEj0ydufOyaXid/ZM2nDu/FL/HQm1zEI/DyLQKd58gayyfQfH7/5tkay2y3oTBPwW9Nzfy1eswUdcSZOPuDi5aXIZBr+XBbUFuvuLL0LaXrKSlTVfKL1/oYtW8EzfRtrd18T8P7iUQyZV5f3OY//7rFn74yVX43BaSzXv6HRM7sAVn6hY0BvMpvU/CxDLuAujhRbRHqwYKcN6CUp5aU8fb77ZywaLyUbuuIIyWYp+V/7pnBe/saKOpI8Ly2X5mThp4PVyTQUtpoQ2zSYvdbKDA3TcYSbIGY3H1gIngjQYtN66u5nt/3ZBPjC/LEucvWsyPX29BUVQSqdzUmeWzT9xl0t4TywfPw3pCCVo6gvjcFgwlNbDhX31eN0+ej6wfvuUPhfFtHAbQIBajlgLX8KQzG4qaChcFbjOvbW4WAVQYtyqK7P36HBOpDNmsisV0pOVlV103P7hvI52BOFaTjk/dMo8lM/1Dzgq2YFoBX37/Yp584wB2s56rz51MeYGV9105g4df2ofLLnPnZdMGzHJ0NKvF2C9BvSxBTyRNU0eYovIZWGasIrrzTQB0hVXY5l2EJI27ni1hhIy/ANocpHIE1gA9EUmSOHdeCY++up9gJInDOnwjIQXhTJTJKrxb28Xfn99DKJri2nMns3y2n0xW4fv3bcgv7B2Jp/n+Xzfwk8+eP+iAn8PMRh0rZhezZEYRkiTlA29+XVFJwmYZPNl7aaGdm1aV8NAbzflt71k9icdfr2PJzDi3XzoN7xUfxbn0apRsBp3HT1IyI4nEKMIQjasAqigq9a0hLloy+rXAc+eX8PDL+1izrYUrVlSN+vUFYTTtbejlP373Vr5298uHtwJQXe7MB8/DMlmVjp7BR8we6+icuIcN9eG0J5Qgns7VXqdXuukKpbBaDLy6pZ3a5iBarcwtF01FYzCjKZ5CVyDGG++08Py6g5T4LNx0wVSmVZ64hisI4yqAtvVESaSyo9r/eVil305ZoY3XNzeLACqMeztqu/s0jQI89lot//HBpZiNWmKJvlNenLaRbZVJpjOEIimMeg0763r41SPb6AklWD67iHlTC/j1430HDK2Y7UdzKEBnFZUn36zj0Vf2A9DUEWHL3i5++KlzRIIF4YTGVWP/kQFEo5+KS5Ikzptfwo4D3XT29s8hKgjjidnUf7S5zazDZTfyqZvn9+nvfN8V0ykrtI1IOTKRXnr3b2Pflq386oH1fOP/3qa9J0Y6k0vG8Nb2NhLJDOfMO5LfduG0gj5ztrsDcZ5840Cf8ybTWQ62hUekzML4Ma5qoHXNQWRZovwkm4qGyznzS7jv2d28saWJG1b3H2EoCOPFrEkerCYdkUMJEyQJbr2kBpNBy7JZRfzks+fT0RPDZTNQVmTDqB/+n5pkx0HaH/oemUA7ZuC90y/g6egc7nt2N1efM4kHX9wLwJ+e2slv/v0Cblw9BVVV8XutfQY9aTQSJoOW9DGLheu046p+IYyA8RVAW0KU+KwYxiilXrHXSnWZk9c2N4sAKoxrFX47//PxVWzf30U0nmLOFB/V5U4gNze60m8f0VzUSiZF4I2HyASOJEZQd73M+Sun8dyWDEcPIfQ4jJiMOkoKBq4Fexwm3nflDH7+4Jb8tiK3iUklovlWOLHxFUBbg0wf447/8xaU8vvH36WpI0zpcb6wgjAejHSQPBElHiF+cHu/7cZkFwa9JT8ASZbgYzfOwT1IXtxVc4vxOU1s299FgcvEnCk+isS6oMIgxk0ADUVTdPbGuXLF2D41rppbzB+eeJfXNzdz+6XTxrQsgjBeySYrpsrZRHe91Wd73OBl8XQ7s6Z4+IJvIcU+65CCvNmoY35NAfNrCkaqyMI4NG4CaG1TAMit5zeWPA4Tsyd7eX1zE7ddUjOq81EFYaKQtXqcq95DsvVAvhnXNPcS1NKpfGShD5dNZBMSRt64CaD7DwXQyaVj32+xal4Jv3p4K43t4TEb0CQI452hoILi932HdE8rss6IzlMs0vAJo2pcBVC/x4LVPHiGkpG2cFquGWjTng4RQAXhFKQDHSQO7iDZVouxbAbG8ulora5++2mtrgG3C8JoGEcBNEhN+ZnxRSpwmSkrtLFxVwfXnTdlrIsjCGeVbCxE51O/InFokFBow7+wLbgUz0XvQ9aJNJnCmWNcTHQKRVN09MSYcgY03x62cFoB7x7oJnHMIsSCIJxYqqspHzwPC296nnRv2xiVSBAGNi5qoEf6P53Del41kya0+QUi218l1d2CxmzDPHUJzuXXDdpstHBaAY+9Vsu22i6WzCga1nIJwnimZtMDbUXNiodR4cwyLmqg+xsDwPAG0HRvG833fpnu5/8ASNjmrkZfUElow79o+t1niNdtO+HxMyd5MOg1bNrdMWxlEoSJQOcpRess7LPNWDEbnUs8iApnlnFTA/V7LVgHyM95KpLt9bT+7RugQuFNX8Q8dUl+Okqqq4n2R39I6wPfoejmr2CeNHfAc+i0GuZM8bJxdzuqqorpLIIwRDq7h8L3fInQpudIHNyBpWYJ1jmr0RhFYgPhzDIuaqD7GgNUD1PtM9XVROv930LS6im5+7tYapb2CX56bynFd/0nek8JHY/+kFRX03HPtXBaIW3dMVq7osNSNkGYKAwF5Xgv+QDF7/8fXOfdht7tH+siCUI/Z30A7QrE6QrEqak8/RG46d42Wv/2LSRJxn/HN9Ed50urMdkovPlLSFod7Q9/HyWdHHC/w9NZNuxuH/B1QRCOT5I1aAwm0XojnLHO+gC6q74H4LRz4KaDHbTe9w3UbBr/7d9A7yk54f46RwG+az9NuruZnlfvH3CfIo+FEp+FjaIfVBDOOGomQ6qrmVR3ixigJJySs74PdHd9D3qd5rQW0c6Eumj927dQkjH8d3wLfUH5kI4zV83FvuhyQu88hXX6coyl/XPfLpxWyLNv1ZNMZ8dslRhBEPrKhLrpXfsI4U0vgCThWHIljiXXoLWdGXPJhbPDuKiBTi135ldfOFnpQActf/062ViIotu+jsE/6aSOd6++E43NQ9ezv0dVsv1eXzitkFRGYfv+rlMqnyAIw0dVVbqDcXpam4lsfw1UBZQswbefIF5/4pH1gnCsszqAJlIZDjSf+hJmydZaWv7yNZREFP/t38BYMvWkzyHrjXguvptUex2hjc/1e33WZA96nYaNoh9UEMZUVyDO/c/t5uM/eIWvPNxOw+LPIhUeyRQW3bNuDEsnnI3O6gC6ryFAVlGZdpIBVFUVguufpuUvX0OSJPx3fhtj8amn3LNMW4apag69r/2dbCzc5zW97vB0FtEPKghj6dWNjfzjhb1E42nae2L88MkmeqZem3/d4BdpN4WTc1YH0K37OpElmFnlGdL+qpIl8u4bNP/+C3Q//0eM5TMp+cD3MRRWnlY5JEnCc/HdKKkEgbWP9Ht94bQCWruitHRFTus6giCcPDWbIRhO8q+36vu9tq9XRjaY0TqLsNQsGf3CCWe1s3oQ0ZZ9nVSXu7AMkkBBSSUIb32Z4LonyAQ70XlKKLj2/2GZuWrYhsjrfeVYZ59PcMO/sC+6Ap3zyMK8Cw5NZ9m4q4Pic6zDcj1BEE4sHewitncdkXdfRzfzQrxOEx298T77uD1uCmZ+Fr2vDK3DO0YlFc5WZ20NNBpPs6+hl3nVvuPuo6QSBN56jIZffozu5/+AxpbLcFL60Z9gnXXOsM8vc593K5Ik0/va3/tsL/Za8Xstoh9UEEaJkkkTWPMw3c//kWTLfiIv/Z73LLSikY985wtcJmbVFGOeMl8ET+GUnLU10O21XSgqzJ06cACN12+n86lfkQl2YJo0D9eq92As6z/NZDhp7R7si68g+NbjOJZeg6GoKv/awmkFPP/2QTGdRRBGQSbQQXjLS0c2KFm8G37Hdz/4JRqDYNRrmFzmpNgrWoSEU3fW1kC37O3EoNcwraLvvC1VVQmsezKXUUijwX/Xt/Hf9vURD56HOVfcgGyy0PPKfX22L55eRCqjsGWPGEwkCCNOlkDq+/OmhDoo13Zz8dIKzplfKoKncNrOygCqqirrdrQxd4oPnbZvba73tb/T8+K9mGuWUPLBH2IqnzmqZdMYLThX3kj8wBZidVvz22dP8WI16XhzW8uolkcQJiKdsxDHsmv6bNO6/Oh9Q0uSIghDcVY24e5vCtAViHPHpX1rlYG3HiOw5hFs8y7Ce8VHkaSxeT6wL7yM0Ppn6Hnpr5g+OBtJktFpZZbP9vPm1hZS6Sx60YwrCCNGkjU4Fl+JvqCC+P6N6AurMFcvFH2dwrA6K2uga7a2IMsSS2YeWR8wtn8TPS/fh2XGSryXf2TMgieArNXjPv8OUu11RN59I7991dwS4skMm0UzriCMOK3ViW3mKgqu/TTOZdcMmt9aEE7WWRdAs1mFVzY2sqCmALtFD+TS8XU88VP0BeX4rvo4kjz2tTvLzJXoiybR++r9KJkUAHOqvdjMel7ZePwl0ARBEISzw1kXQDfu6aAnlOTiJbm+DCWTov2RH6IqCoU3fgFZZxjjEuZIkoznwveSCXURWv8MAFqNzIWLy3j73VZ6QokxLqEgCIJwOs66APr4a7W4bAYWz8g133Y//ydSbbUUXP3J467fOVZMlbMxTV5AYM0j+RR/ly+vJKuoPL/u4BiXThAEQTgdZ1UA3VnXzbb9Xdywego6rUx42yuENz+PY/l1Z2waLs+Fd6GkEvS+/g8Ain1W5k318exb9aQzyhiXThAEQThVZ00AzWQVfvPoNtx2A5ctqyTZdoCuf/0OY8VM3OffPtbFOy69rxz7ossIbXyORMt+AK47bzLdwQQvrm8Y49IJgiAIp+qsCKCqqvL7x9+lriXEPTfMQZeN0/7wD5BNVgqu++wZMWjoRNzn3YbG6qTrX79FVbIsqClgWoWLfzy/h1giPdbFEwRBEE7BGR9AW7oifP+vG3h6TR3Xnz+FZdN9tP/zR2QiPRTe+EW0VudYF3FQssGcWzO07QCBtx5DkiQ+eM0sesMJ/vbs7rEuniAIgnAKzqhECllF5d39Xeys76G2KcD+pgDdwQQaWeKuy6dz0+pJdDz+ExL12/Fd80mMJdVjXeQhs0xfgWXPOnpf+wemytlMq5zKFSuqeOKNA8yc5GHFnOKxLuKISiQzNHeEMWbCWNLdGM0mdJ5SNKaTS6fW1h0hEejBlOhEb9AR1HhQdBZKCq0YdLmPc28wSqanBWK9mN0FmAvLiHa2k+ppQqvVIusNGJ0FqJJMqreN9oydtjDYbCbKCix0B5N0BuJYdCoGrUQwFMXtsmF3WGlu7UVS0hTaJEzZGFopi8FXRndST0t3hGg8TYnPSnmRvU/icuHMl0xnaG6PEIgkKXCbKfFZh33BiWNlswpNHRG6g3HcDhOlBVai8TSNHWFURaW0wIbLbhzy+YKRJE0dEWQZkikFWYLSAisOg0KouZ5ELIpqK0Q1uekMxglFUzhtBlRFJZHOYjXpULIqiXQGvU5LOJbCatKRzmRRVDAZNBh0GjJZlUgsTSqj4HMZc8eksngdJrpCceLJLBajFrNRSyKZIZ7MklVUrCYdGUVBAjSyjM2ip8RnobM3TmcgjpJJU2mKQDxI1uSmNWVF0mgpK7ThtB2ZYZHJKjR3RghHU0TiKdx2I9F4Br1Ok/t7raMzG+OMCKCqqvLa5mb+/NQOuoIJJCm3gsmsSV6qy52smluMyyTR8fD3ie3fiPui92Gbff5YF/ukSJKE9/KPkmzaQ8djP6HkA9/ng9fMZH9TgB//fRNuh5FpFSe3MPjZIhpP88gr+6ixRSnY9DuSkR4AzDXL8F76AbS2oa3nWtsUoKfhAL7NfyQaaCUK6Mtn8bb9MrbYvVyxsoqeQIzknjWob/4JlCxhWYPrsnuIbHiKdEdu5LNl+grUmqUE1v6Txprb+NHTB0hlFCQJPn/HQh5/vZauQIKrVlVx/*************************************************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********************************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\n", "text/plain": ["<Figure size 576x576 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["config_sample['batch_size'] = 4\n", "model = get_model(config_sample, device, should_train=False, verbose=2) # , state_dict=model[2].state_dict()\n", "(hp_embedding, data, _), targets, single_eval_pos = next(iter(model[3]))\n", "\n", "from utils import normalize_data\n", "fig = plt.figure(figsize=(8, 8))\n", "N = 100\n", "plot_features(data[0:N, 0, 0:4], targets[0:N, 0], fig=fig)\n", "\n", "d = np.concatenate([data[:, 0, :].T, np.expand_dims(targets[:, 0], -1).T])\n", "d[np.isnan(d)] = 0\n", "c = np.corrcoef(d)\n", "plt.matshow(np.abs(c), vmin=0, vmax=1)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(config_sample, device, should_train=True, verbose=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 4}