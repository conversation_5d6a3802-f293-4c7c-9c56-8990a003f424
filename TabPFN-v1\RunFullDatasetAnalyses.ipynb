{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "from scripts import tabular_baselines\n", "\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "from datasets import load_openml_list, valid_dids_classification, test_dids_classification, open_cc_dids\n", "from scripts.tabular_baselines import *\n", "from scripts.tabular_evaluation import evaluate\n", "from scripts.tabular_metrics import calculate_score, make_ranks_and_wins_table, make_metric_matrix\n", "from scripts import tabular_metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from notebook_utils import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["cc_test_datasets_multiclass, cc_test_datasets_multiclass_df = load_openml_list(open_cc_dids, multiclass=True, shuffled=True, filter_for_nan=False, max_samples = 10000, num_feats=100, return_capped=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_datasets(selector, task_type, suite='openml'):\n", "    if task_type == 'binary':\n", "        ds = valid_datasets_binary if selector == 'valid' else test_datasets_binary\n", "    else:\n", "        if suite == 'openml':\n", "            ds = valid_datasets_multiclass if selector == 'valid' else test_datasets_multiclass\n", "        elif suite == 'cc':\n", "            ds = valid_datasets_multiclass if selector == 'valid' else cc_test_datasets_multiclass\n", "        else:\n", "            raise Exception(\"Unknown suite\")\n", "    return ds"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Setting params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_positions = [1000]\n", "max_features = 100\n", "bptt = 2000\n", "selector = 'test'\n", "base_path = os.path.join('.')\n", "overwrite=False\n", "max_times = [0.5, 1, 15, 30, 60, 60*5, 60*15, 60*60]\n", "metric_used = tabular_metrics.auc_metric\n", "methods = ['transformer', 'logistic', 'gp', 'knn', 'catboost', 'xgb', 'autosklearn2', 'autogluon']\n", "task_type = 'multiclass'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["suite = 'cc'\n", "test_datasets = get_datasets('test',task_type, suite=suite)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["clf_dict= {'gp': gp_metric\n", "                , 'knn': knn_metric\n", "                , 'catboost': catboost_metric\n", "                , 'xgb': xgb_metric\n", "           , 'transformer': transformer_metric\n", "                , 'logistic': logistic_metric\n", "           , 'autosklearn': autosklearn_metric\n", "             , 'autosklearn2': autosklearn2_metric\n", "            , 'autogluon': autogluon_metric}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device = 'cpu'\n", "\n", "def eval_method(task_type, method, dids, selector, eval_positions, max_time, metric_used, split_number, append_metric=True, fetch_only=False, verbose=False):\n", "    \n", "    dids = dids if type(dids) is list else [dids]\n", "    \n", "    for did in dids:\n", "\n", "        ds = get_datasets(selector, task_type, suite=suite)\n", "\n", "        ds = ds if did is None else ds[did:did+1]\n", "\n", "        clf = clf_dict[method]\n", "\n", "        time_string = '_time_'+str(max_time) if max_time else ''\n", "        metric_used_string = '_'+tabular_baselines.get_scoring_string(metric_used, usage='') if append_metric else ''\n", "\n", "        result = evaluate(datasets=ds\n", "                          , model=clf\n", "                          , method=method+time_string+metric_used_string\n", "                          , bptt=bptt, base_path=base_path\n", "                          , eval_positions=eval_positions\n", "                          , device=device, max_splits=1\n", "                          , overwrite=overwrite\n", "                          , save=True\n", "                          , metric_used=metric_used\n", "                          , path_interfix=task_type\n", "                          , fetch_only=fetch_only\n", "                          , split_number=split_number\n", "                          , verbose=verbose\n", "                          , max_time=max_time)\n", "    \n", "    return result"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Baseline Evaluation\n", "This section runs baselines and saves results locally."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!mkdir {base_path}/results\n", "!mkdir {base_path}/results/tabular/\n", "!mkdir {base_path}/results/tabular/multiclass/"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# RUN ONE METHOD ON ONE DATASET AND SPLIT\n", "overwrite=True\n", "dataset_id = 0\n", "split_number = 1\n", "maximum_runtime = 30\n", "r = eval_method(task_type, 'transformer', dataset_id, 'test', eval_positions, maximum_runtime, metric_used, split_number)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# RUN ALL METHODS, SPLITS AND DATASETS\n", "test_datasets = get_datasets('test',task_type, suite=suite)\n", "\n", "overwrite=True\n", "jobs = [\n", "    eval_method(task_type, m, did, selector, eval_positions, max_time, metric_used, split_number)\n", "    for did in range(0, len(test_datasets))\n", "    for selector in ['test']\n", "    for m in methods\n", "    for max_time in max_times\n", "    for split_number in [1, 2, 3, 4, 5]\n", "]"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["pos = str(eval_positions[0])\n", "\n", "global_results = {}\n", "overwrite=False\n", "\n", "for method in baseline_methods:\n", "    for max_time in max_times:\n", "        for split_number in range(1,5+1):\n", "            global_results[method+'_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='')+'_split_'+str(split_number)] = eval_method(task_type, method,  None, selector, \n", "                                                                                                                                    eval_positions, fetch_only=True, \n", "                                                                                                                                    verbose=False, max_time=max_time,\n", "                                                                                                                                    metric_used=metric_used, split_number=split_number)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path_ = 'prior_tuning_result.pkl'\n", "\n", "try:\n", "    output = open(path_, 'rb')\n", "    _, metrics, _, _, _, _ = CustomUnpickler(output).load()\n", "except:\n", "    output = open(path_, 'rb')\n", "    _, metrics, _, _, _ = CustomUnpickler(output).load()\n", "if isinstance(metrics, list):\n", "    for i in range(1, len(metrics[1])+1):\n", "        global_results['transformer_split_'+str(i)] = metrics[2][i-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify integrity of results\n", "for bl in set(global_results.keys()):\n", "    if 'split_1' in bl:\n", "        for ds in test_datasets:\n", "            if f'{ds[0]}_ys_at_1000' not in global_results[bl]:\n", "                continue\n", "            match = (global_results[bl][f'{ds[0]}_ys_at_1000'] == global_results['transformer_split_1'][f'{ds[0]}_ys_at_1000']).float().mean()\n", "            if not match:\n", "                raise Exception(\"Not the same labels used\")\n", "            "]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["limit_to = ''\n", "calculate_score(tabular_metrics.auc_metric, 'roc', global_results, test_datasets, eval_positions + [-1], limit_to=limit_to)\n", "calculate_score(tabular_metrics.cross_entropy, 'cross_entropy', global_results, test_datasets, eval_positions + [-1], limit_to=limit_to)\n", "calculate_score(tabular_metrics.accuracy_metric, 'acc', global_results, test_datasets, eval_positions + [-1])\n", "calculate_score(tabular_metrics.time_metric, 'time', global_results, test_datasets, eval_positions + [-1], aggregator='sum', limit_to=limit_to)\n", "calculate_score(tabular_metrics.time_metric, 'time', global_results, test_datasets, eval_positions + [-1], aggregator='mean', limit_to=limit_to)\n", "calculate_score(tabular_metrics.count_metric, 'count', global_results, test_datasets, eval_positions + [-1], aggregator='sum', limit_to=limit_to)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### ROC and AUC plots from TabPFN Paper"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_ranks_and_wins_table(global_results_filtered, metric_key, max_time, split_number, time_matrix):\n", "    global_results_filtered_split = {**global_results_filtered}\n", "    global_results_filtered_split = {k: global_results_filtered_split[k] for k in global_results_filtered_split.keys() if '_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='')+'_split_'+str(split_number) in k or 'transformer_split_'+str(split_number) in k}\n", "\n", "    matrix, matrix_stds = make_metric_matrix(global_results_filtered_split, methods, pos, metric_key, test_datasets)\n", "    for method in methods:\n", "        if time_matrix[method] > max_time * 2:\n", "            matrix[method] = np.nan\n", "        # = np.nan\n", "\n", "    if metric_key == 'cross_entropy':\n", "        matrix = -(matrix.fillna(-100))\n", "    else:\n", "        matrix = matrix.fillna(-1)\n", "    return make_ranks_and_wins_table(matrix.copy())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "df_ = []\n", "metric_keys = ['roc', 'cross_entropy', 'time']\n", "\n", "for max_time in max_times:\n", "    global_results_filtered = {**global_results}\n", "    global_results_filtered = {k: global_results_filtered[k] for k in global_results_filtered.keys() if '_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='')+'_' in k or 'transformer' in k}\n", "    \n", "    time_matrix, _ = make_metric_matrix(global_results_filtered, methods, pos, 'time', test_datasets)\n", "    time_matrix = time_matrix.mean()\n", "    \n", "    if len(global_results_filtered) == 0:\n", "        continue\n", "        \n", "    # Calculate ranks and wins per split\n", "    for metric_key in metric_keys:\n", "        for split_number in range(1,6):\n", "            ranks, wins = generate_ranks_and_wins_table(global_results_filtered, metric_key, max_time, split_number, time_matrix)\n", "\n", "            for method in methods:\n", "                method_ = method+'_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='') if method != 'transformer' else method\n", "                global_results[method_+'_split_'+str(split_number)]['mean_rank_'+metric_key+f'_at_{pos}'] = ranks[method]\n", "                global_results[method_+'_split_'+str(split_number)]['mean_wins_'+metric_key+f'_at_{pos}'] = wins[method]\n", "    \n", "    #for method in global_results.keys():\n", "    #    global_results[method]['mean_rank_'+metric_key+f'_at_{pos}'] = ranks[]\n", "    \n", "    avg_times = {}\n", "    for method_ in methods:\n", "        avg_times[method_] = []\n", "        for split_number in range(1,6):\n", "            if method_ != 'transformer':\n", "                method = method_+'_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='')+'_split_'+str(split_number)\n", "            else:\n", "                method = method_+'_split_'+str(split_number)\n", "            avg_times[method_] += [global_results[method][f'mean_time_at_{pos}']]\n", "    avg_times = pd.DataFrame(avg_times).mean()\n", "    \n", "    for metric_key in metric_keys:\n", "        for ranking in ['', 'rank_', 'wins_']:\n", "            for method_ in methods:\n", "                for split_number in range(1,6):\n", "                    method = method_\n", "                    if method_ != 'transformer':\n", "                        method = method_+'_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='')+'_split_'+str(split_number)\n", "                    else:\n", "                        method = method_+'_split_'+str(split_number)\n", "\n", "                    if global_results[method][f'sum_count_at_{pos}'] <= 29:\n", "                        print('Warning not all datasets generated for '+method+' '+ str(global_results[method][f'sum_count_at_{pos}']))\n", "                        \n", "                    time = global_results[method]['mean_time'] if ranking == '' else max_time\n", "                    time = max_time # Todo: This is not the real time\n", "                    df_ += [{'metric'+ranking+metric_key: global_results[method]['mean_'+ranking+metric_key+f'_at_{pos}'], 'real_time': avg_times[method_], 'time': time, 'method': method_, 'split_number': split_number}]\n", "                    # For Roc AUC Plots\n", "                    #if 'transformer' in method:\n", "                    #    df_ += [{'metric'+ranking+metric_key: global_results[method]['mean_'+ranking+metric_key+f'_at_{pos}'], 'real_time': avg_times[method_], 'time': time, 'method': method_, 'split_number': split_number}]\n", "                    #    df_ += [{'metric'+ranking+metric_key: global_results[method]['mean_'+ranking+metric_key+f'_at_{pos}'], 'real_time': max(avg_times), 'time': max(max_times), 'method': method_, 'split_number': split_number}]\n", "                            \n", "            \n", "df_ = pd.DataFrame(df_)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metric_renamer = {'roc': 'ROC AUC', 'cross_entropy': 'Cross entropy'\n", "                  , 'rank_roc': 'Mean ROC AUC Rank', 'rank_cross_entropy': 'Mean Cross entropy Rank'\n", "                  , 'wins_roc': 'Mean ROC AUC Wins', 'wins_cross_entropy': 'Mean Cross entropy Wins'\n", "                  , 'time': 'actual time taken'}\n", "max_times_renamer = {0.5: \"0.5s\", 1: \"1s\", 5: \"5s\", 15: \"15s\", 30: \"30s\", 60: \"1min\", 300: \"5min\", 900: \"15min\", 3600: \"1h\", 14400: \"4h\"}\n", "\n", "def make_tabular_results_plot(metric_key, exclude, max_times, df_, grouping=True):\n", "    f, ax = plt.subplots(figsize=(7, 7))\n", "    #ax.set(xscale=\"log\")\n", "    \n", "    df_.loc[:, 'time_log'] = np.log10(df_.time)\n", "    df_.loc[:, 'real_time_log'] = np.log10(df_.real_time)\n", "    time_column = 'time_log' if grouping else 'real_time_log'\n", "\n", "    sns.set_palette(\"tab10\")\n", "    for method in methods:\n", "        if method in exclude or method=='transformer':\n", "            continue\n", "        df_method = df_[df_.method==method].copy()\n", "        ax = sns.lineplot(time_column, 'metric'+metric_key, data=df_method, marker='o', label=method, ax=ax)\n", "    #sns.scatterplot(data=df_, x='time', y='metric', hue='method', ax=ax, style='method') #\n", "    df_trans = df_[df_.method=='transformer']\n", "    if time_column == 'real_time_log':\n", "        # Removing dots for line for transformers\n", "        df_trans = df_trans[np.logical_or(df_trans.real_time == df_trans.real_time.min(), df_trans.real_time == df_trans.real_time.max())]\n", "        df_trans.loc[:, 'metric'+metric_key] = df_trans['metric'+metric_key].mean()\n", "        df_trans.loc[:, time_column] = np.log(1) # Hacky code to get the right time from our measurements\n", "    ax = sns.lineplot(time_column, 'metric'+metric_key, data=df_trans, linestyle='--', marker='o', ci=\"sd\", ax=ax)\n", "    \n", "    #ax = sns.scatterplot(data = df_trans, x=time_column, y='metric'+metric_key, s=800, marker='*', color='grey') #\n", "    #ax = plt.scatter(df_trans[time_column], df_trans['metric'+metric_key], s=600, marker=['*']) #\n", "    \n", "    if grouping:\n", "        ax.set_xlabel(\"Time (s, requested, not actual)\")\n", "    else:\n", "        ax.set_xlabel(\"Time taken\")\n", "    ax.set_ylabel(metric_renamer[metric_key])\n", "\n", "    #ax.legend()\n", "    \n", "    times = np.log10(max_times)\n", "    ax.set_xticks(times)\n", "    ax.set_xticklabels([max_times_renamer[t] for t in max_times])\n", "    \n", "    #ax.legend([],[], frameon=False)\n", "    \n", "    return ax"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_absolute = df_.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_absolute = df_.copy()\n", "df_absolute = df_absolute[np.logical_or(df_.method != 'autogluon', df_.time >= 30)] # Autogluon did not yield any useful results before 30s\n", "\n", "knn_extend = df_absolute[np.logical_and(df_absolute.method=='knn', df_absolute.time == 3600)].copy()\n", "knn_extend['real_time'] = 14400\n", "knn_extend['time'] = 14400\n", "df_absolute = df_absolute.append(knn_extend, ignore_index=True).reindex()\n", "\n", "knn_extend = df_absolute[np.logical_and(df_absolute.method=='logistic', df_absolute.time == 3600)].copy()\n", "knn_extend['real_time'] = 14400\n", "knn_extend['time'] = 14400\n", "\n", "df_absolute = df_absolute.append(knn_extend, ignore_index=True).reindex()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["exclude=['']\n", "#ax = make_tabular_results_plot('time', exclude=exclude)\n", "ax = make_tabular_results_plot('roc', df_=df_absolute, exclude=exclude, grouping=False, max_times=[1, 5, 30, 60*5, 60*60])\n", "ax.set_ylim([0.84, 0.9])\n", "ax.set_xlim([np.log10(0.7), np.log10(3600)])\n", "ax.legend([],[], frameon=False)\n", "\n", "#tikzplotlib.save(f'roc_over_time.tex', axis_height='5cm', axis_width='6cm', strict=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ax = make_tabular_results_plot('rank_roc', df_=df_[df_.time >= 1].copy(), exclude=['tabnet'], max_times=[1, 5, 30, 60*5, 60*60])\n", "ax.invert_yaxis()\n", "ax.set_xlim([np.log10(1.0), np.log10(3600)])\n", "ax.legend([],[], frameon=False)\n", "tikzplotlib.save(f'roc_raks_tabular.tex', axis_height='5cm', axis_width='6cm', strict=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ax = make_tabular_results_plot('wins_roc', df_=df_[df_.time >= 1].copy(), exclude=exclude, max_times=[1, 5, 30, 60*5, 60*60])\n", "ax.set_xlim([np.log10(1.0), np.log10(3600)])\n", "ax.legend([],[], frameon=False)\n", "tikzplotlib.save(f'roc_wins_tabular.tex', axis_height='5cm', axis_width='6cm', strict=True)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Big Table metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_time = '3600'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["global_results_filtered = {**global_results}\n", "global_results_filtered = {k: global_results_filtered[k] for k in global_results_filtered.keys() if '_time_'+str(max_time)+tabular_baselines.get_scoring_string(metric_used, usage='')+'_' in k or 'transformer' in k}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["roc_matrix, roc_matrix_stds = make_metric_matrix(global_results_filtered, methods, pos, 'roc', test_datasets_multiclass_filtered)\n", "acc_matrix, acc_matrix_stds = make_metric_matrix(global_results_filtered, methods, pos, 'acc', test_datasets_multiclass_filtered)\n", "cross_entropy_matrix, cross_entropy_matrix_stds = make_metric_matrix(global_results_filtered, methods, pos, 'cross_entropy', test_datasets_multiclass_filtered)\n", "time_matrix, time_matrix_stds = make_metric_matrix(global_results_filtered, methods, pos, 'time', test_datasets_multiclass_filtered)\n", "\n", "roc_rank, rocs_wins = make_ranks_and_wins_table(roc_matrix.copy())\n", "acc_rank, acc_wins = make_ranks_and_wins_table(acc_matrix.copy())\n", "cross_entropy_rank, cross_entropy_wins = make_ranks_and_wins_table(-cross_entropy_matrix.copy())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wins_vs_idx(matrix, idx):\n", "    wins_auc = np.array([[(matrix.values[:, j] < matrix.values[:, i]).sum() if i != j else 0 for i,method in enumerate(methods)] for j in [idx]])\n", "    ties_auc = np.array([[(matrix.values[:, j] == matrix.values[:, i]).sum() if i != j else 0 for i,method in enumerate(methods)] for j in [idx]])\n", "    losses_auc = np.array([[(matrix.values[:, j] > matrix.values[:, i]).sum() if i != j else 0 for i,method in enumerate(methods)] for j in [idx]])\n", "    \n", "    return wins_auc, ties_auc, losses_auc\n", "\n", "transformer_idx = np.where(roc_matrix.columns == 'transformer')[0][0]\n", "\n", "wins_roc_vs_us, ties_roc_vs_us, losses_roc_vs_us = wins_vs_idx(roc_matrix, transformer_idx)\n", "wins_acc_vs_us, ties_acc_vs_us, losses_acc_vs_us = wins_vs_idx(acc_matrix, transformer_idx)\n", "wins_ce_vs_us, ties_ce_vs_us, losses_ce_vs_us = wins_vs_idx(-cross_entropy_matrix, transformer_idx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rename(table):\n", "    return table.rename(columns=relabeler).T.rename(columns={'blood-transfusion-service-center': 'blood-transfus..'\n", "                                                                , 'jungle_chess_2pcs_raw_endgame_complete': 'jungle\\_chess..', 'bank-marketing': 'bank-market..'}).T\n", "\n", "def get_suffix(i, k):\n", "    suffix = ''\n", "    suffix = suffix+'s' if test_datasets[i][5]['samples_capped'] == True else suffix\n", "    suffix = suffix+'f' if test_datasets[i][5]['feats_capped'] == True else suffix\n", "    suffix = suffix+'c' if test_datasets[i][5]['classes_capped'] == True else suffix\n", "    suffix = '' if len(suffix) == 0 else f' [{suffix}]'\n", "    \n", "    return k + suffix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["relabeler = {'transformer': 'Tabular PFN'\n", "             , 'autogluon': 'Autogluon'\n", "             , 'autosklearn2': 'Autosklearn2'\n", "             , 'gp': 'GP (RBF)'\n", "             , 'logistic': 'Log. Regr.'\n", "             , 'knn': 'KN<PERSON>'\n", "             , 'catboost': 'Catboost'\n", "            , 'xgb': 'XGB'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table = roc_matrix.copy()\n", "#table = roc_ovr_matrix.copy()\n", "#table = acc_matrix.copy()\n", "#table = cross_entropy_matrix.copy()\n", "\n", "#table = table_acc\n", "table.index = [get_suffix(i, k) for i, k in enumerate(table.index[0:table.shape[0]])]\n", "\n", "table.loc['Wins AUC OVO'] = rocs_wins.values\n", "#table.loc['Mean AUC OVR'] = roc_ovr_matrix.mean(skipna=True)\n", "table.loc['Wins Acc.'] = acc_wins.values\n", "#table.loc['Mean Bal. Acc.'] = balanced_acc_matrix.mean()\n", "table.loc['Wins CE'] = cross_entropy_wins.values\n", "\n", "table.loc['Win/T/L AUC vs Us'] = [\"{:d}/{:d}/{:d}\".format(w, t, l) for w,t,l in zip(wins_roc_vs_us[-1, :], ties_roc_vs_us[-1, :], losses_roc_vs_us[-1, :])]\n", "table.loc['Win/T/L Acc vs Us'] = [\"{:d}/{:d}/{:d}\".format(w, t, l) for w,t,l in zip(wins_acc_vs_us[-1, :], ties_acc_vs_us[-1, :], losses_acc_vs_us[-1, :])]\n", "table.loc['Win/T/L CE vs Us'] = [\"{:d}/{:d}/{:d}\".format(w, t, l) for w,t,l in zip(wins_ce_vs_us[-1, :], ties_ce_vs_us[-1, :], losses_ce_vs_us[-1, :])]\n", "\n", "table.loc['Mean AUC OVO'] = roc_matrix.mean(skipna=True)\n", "table.loc['Mean AUC OVO Stds'] = roc_matrix_stds.mean(skipna=True)\n", "\n", "#table.loc['Mean AUC OVR'] = roc_ovr_matrix.mean(skipna=True)\n", "table.loc['Mean Acc.'] = acc_matrix.mean()\n", "table.loc['Mean Acc. Stds'] = acc_matrix_stds.mean(skipna=True)\n", "\n", "#table.loc['Mean Bal. Acc.'] = balanced_acc_matrix.mean()\n", "table.loc['Mean CE'] = cross_entropy_matrix.mean()\n", "table.loc['Mean CE Stds'] = cross_entropy_matrix_stds.mean()\n", "\n", "table.loc['M. rank AUC OVO'] = roc_rank.values\n", "#table.loc['Mean rank AUC OVR'] = roc_ovr_rank.values\n", "table.loc['Mean rank Acc.'] = acc_rank.values\n", "#table.loc['Mean rank Bal. Acc.'] = balanced_acc_rank.values\n", "table.loc['Mean rank CE'] = cross_entropy_rank.values\n", "\n", "table.loc['Mean time (s)'] = time_matrix.mean()\n", "table.loc['Mean time (s)', 'knn'] = 0.5\n", "table.loc['Mean time (s)', 'logistic'] = 60\n", "\n", "table = table[['knn', 'logistic', 'gp', 'catboost', 'xgb', 'autosklearn2', 'autogluon', 'transformer']]\n", "rename(table).round(decimals=3).style.highlight_max(axis = 1, props= 'font-weight: bold;').format(precision=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def bold_extreme_values(data, format_string=\"%.3g\", max_=True):\n", "    data = data.astype(float).round(3)\n", "    if max_:\n", "        extrema = data != data.max()\n", "    else:\n", "        extrema = data != data.min()\n", "    bolded = data.apply(lambda x : \"\\\\textbf{%s}\" % format_string % x)\n", "    formatted = data.apply(lambda x : format_string % x)\n", "    return formatted.where(extrema, bolded) \n", "\n", "def to_str(data, format_string=\"%.3g\"):\n", "    formatted = data.apply(lambda x : format_string % x)\n", "    return formatted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["keys_max = [\"Mean rank CE\", \"Mean rank Acc.\", \"Mean rank AUC OVO\", \"Mean rank AUC OVR\", \"Mean rank Bal. Acc.\", \"Mean AUC OVO\", \"Mean Acc.\"]\n", "keys_max = [\"Mean AUC OVO\", \"Mean Acc.\", \"Wins AUC OVO\", \"Wins Acc.\", \"Wins CE\"]\n", "\n", "keys_min = [\"Mean rank CE\", \"Mean rank Acc.\", \"M. rank AUC OVO\", \"Mean CE\"]\n", "\n", "table_latex = rename(table).copy()\n", "\n", "table_latex.iloc[0:30] = table_latex.iloc[0:30].apply(lambda data : bold_extreme_values(data),axis=1)\n", "table_latex.loc[[\"Mean time (s)\"]] = table_latex.loc[[\"Mean time (s)\"]].apply(lambda data : bold_extreme_values(data, format_string=\"%.4g\", max_=False), axis=1)\n", "table_latex.loc[keys_max] = table_latex.loc[keys_max].apply(lambda data : bold_extreme_values(data),axis=1)\n", "table_latex.loc[keys_min] = table_latex.loc[keys_min].apply(lambda data : bold_extreme_values(data, max_=False),axis=1)\n", "\n", "table_latex.loc[['Mean CE Stds']] = table_latex.loc[['Mean CE Stds']].apply(lambda data : to_str(data, format_string=\"%.2g\"),axis=1)\n", "table_latex.loc['Mean CE'] = table_latex.loc['Mean CE'] + '$\\pm$' + table_latex.loc['Mean CE Stds']\n", "table_latex = table_latex.drop(['Mean CE Stds'])\n", "\n", "table_latex.loc[['Mean Acc. Stds']] = table_latex.loc[['Mean Acc. Stds']].apply(lambda data : to_str(data, format_string=\"%.2g\"),axis=1)\n", "table_latex.loc['Mean Acc.'] = table_latex.loc['Mean Acc.'] + '$\\pm$' + table_latex.loc['Mean Acc. Stds']\n", "table_latex = table_latex.drop(['Mean Acc. Stds'])\n", "\n", "table_latex.loc[['Mean AUC OVO Stds']] = table_latex.loc[['Mean AUC OVO Stds']].apply(lambda data : to_str(data, format_string=\"%.2g\"),axis=1)\n", "table_latex.loc['Mean AUC OVO'] = table_latex.loc['Mean AUC OVO'] + '$\\pm$' + table_latex.loc['Mean AUC OVO Stds']\n", "table_latex = table_latex.drop(['Mean AUC OVO Stds'])\n", "\n", "table_latex\n", "#print(table_latex.to_latex(escape=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["print(table_latex.to_latex(escape=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_latex_small = table_latex.iloc[-len(keys_min+keys_max)-1-3:]\n", "table_latex_small"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(table_latex_small.to_latex(escape=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_latex = table.copy()\n", "\n", "table_latex.iloc[:-5] = table_latex.iloc[:-5].apply(lambda data : bold_extreme_values(data),axis=1)\n", "table_latex.iloc[-5:-5] = table_latex.iloc[-5:-5].apply(lambda data : bold_extreme_values(data, max_=False),axis=1)\n", "\n", "table_latex\n", "#print(table_latex.to_latex(escape=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rename(table[-7:]).round(decimals=3).style.highlight_min(axis = 1, props= 'font-weight: bold;').format(precision=3)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 4}