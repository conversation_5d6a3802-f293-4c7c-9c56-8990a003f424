"""
model_weight_fusion.py - 模型权重融合工具

该文件提供了TabPFN模型权重融合的功能，用于创建自定义的模型变体：

主要功能：
1. 模型权重融合：将不同TabPFN变体的权重进行融合
2. 权重管理：自动处理模型权重的加载和保存
3. 路径管理：统一的模型文件路径管理
4. 错误处理：完整的异常处理和验证

核心功能：
- fuse_model_weights(): 主要的权重融合函数
- 支持MSE和MuZero变体的融合
- 自动创建保存目录
- 权重兼容性检查

"""

import os
import torch
from pathlib import Path

def fuse_model_weights(original_model_path, default_model_path, save_path):
    """
    融合模型权重的函数
    Args:
        original_model_path: 原始模型权重路径 (mse或muzero)
        default_model_path: 默认模型权重路径
        save_path: 融合后模型保存路径
    
    Returns:
        str or None: 成功时返回保存路径，失败时返回None
    """
    try:
        if not Path(original_model_path).exists():
            print(f"Warning: {original_model_path} does not exist, skipping fusion")
            return None
            
        if not Path(default_model_path).exists():
            print(f"Warning: {default_model_path} does not exist, skipping fusion")
            return None
            
        print(f"开始融合模型权重: {original_model_path} -> {save_path}")
        
        # 加载原始模型和默认模型 
        model_m = torch.load(open(original_model_path, 'rb'))   
        model_d = torch.load(open(default_model_path, 'rb')) 
        
        # 提取原始模型的状态字典 
        my_stat = model_m[0]    
        new_stat = {k.replace('module.', ''): v for k, v in my_stat.items()}    
        
        # 融合权重 
        for k in model_d['state_dict'].keys():
            if k in new_stat:
                model_d['state_dict'][k] = new_stat[k]
            else:
                print(f"[!] Warning: {k} not found in new model params")
        
        # 确保保存目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存融合后的模型
        torch.save(model_d, open(save_path, 'wb'))
        print(f"Successfully fused and saved model to {save_path}")
        return save_path
        
    except Exception as e:
        print(f"Error fusing model weights for {original_model_path}: {e}")
        return None

def main():
    """
    主函数：执行 MSE 和 Muzero 两种模型的权重融合
    """
    print("=" * 60)
    print("开始 TabPFN 模型权重融合")
    print("=" * 60)
    
    
    # 定义模型路径
    mse_model_path = '/root/TabPFN-v2/test_regression/mse_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    muzero_model_path = '/root/TabPFN-v2/test_regression/muzero_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    default_model_path = "/root/LDM_test/tabpfn-v2-regressor.ckpt"
    
    # 定义保存路径
    mse_save_path = './new_model_checkpoints/mse_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    muzero_save_path = './new_model_checkpoints/muzero_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    
    # 创建保存目录
    os.makedirs('./new_model_checkpoints', exist_ok=True)
    
    # 融合结果记录
    fusion_results = {}
    
    print("\n--- 融合 MSE 模型权重 ---")
    mse_result = fuse_model_weights(mse_model_path, default_model_path, mse_save_path)
    fusion_results['mse'] = mse_result
    
    print("\n--- 融合 Muzero 模型权重 ---")
    muzero_result = fuse_model_weights(muzero_model_path, default_model_path, muzero_save_path)
    fusion_results['muzero'] = muzero_result
    
    # 输出融合结果总结
    print("\n" + "=" * 60)
    print("权重融合结果总结")
    print("=" * 60)
    
    success_count = 0
    for model_type, result_path in fusion_results.items():
        if result_path is not None:
            print(f"✅ {model_type.upper()} 模型权重融合成功: {result_path}")
            success_count += 1
        else:
            print(f"❌ {model_type.upper()} 模型权重融合失败")
    
    print(f"\n总计: {success_count}/2 个模型权重融合成功")
    
    if success_count > 0:
        print(f"\n融合后的模型权重已保存到: ./new_model_checkpoints/")
    else:
        print("\n所有模型权重融合都失败了，请检查原始权重文件路径")
    
    return fusion_results

if __name__ == "__main__":
    main()
