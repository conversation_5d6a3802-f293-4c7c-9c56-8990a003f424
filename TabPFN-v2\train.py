import os
import itertools
import argparse
import time
import datetime
import yaml
from contextlib import nullcontext


import torch
from torch import nn

import utils as utils
from transformer import TransformerModel

from utils import get_cosine_schedule_with_warmup, get_openai_lr, StoreDictKeyPair, get_weighted_single_eval_pos_sampler, get_uniform_single_eval_pos_sampler, load_model_dicts
import priors as priors
import encoders as encoders
import positional_encodings as positional_encodings
from utils import init_dist
from torch.cuda.amp import autocast, GradScaler
from torch import nn
from tqdm import tqdm 
import math
import numpy as np
import wandb  # 导入 wandb 库
import torch.distributed as dist
import networkx as nx
import pickle
import json

from model.bar_distribution import FullSupportBarDistribution
from model.config import InferenceConfig
from model.encoders import (
    InputNormalizationEncoderStep,
    LinearInputEncoderStep,
    MulticlassClassificationTargetEncoder,
    NanHandlingEncoderStep,
    RemoveDuplicateFeaturesEncoderStep,
    RemoveEmptyFeaturesEncoderStep,
    SequentialEncoder,
    VariableNumFeaturesEncoderStep,
)
from model.transformer import PerFeatureTransformer


def load_checkpoint_distributed(model, optimizer, scheduler, scaler, checkpoint_path, rank, using_dist, device):
    '''分布式恢复权重参数'''
    # print(f"========== 1 ============= load_checkpoint_distributed ===========")
    assert os.path.exists(checkpoint_path), f"No model found in {checkpoint_path}"
    checkpoint = load_model_dicts(checkpoint_path, device, replace_module_prefix=not using_dist)  # (model_state, optimizer_state, scheduler_state, scaler_state, config_sample)
    assert checkpoint, f"failed to load model: {checkpoint_path}"

    model.load_state_dict(checkpoint[0])
    optimizer.load_state_dict(checkpoint[1])
    scheduler.load_state_dict(checkpoint[2])
    scaler.load_state_dict(checkpoint[3])
    start_epoch = checkpoint[4]['epoch_in_training']        # 当前权重的epoch为epoch_in_training，恢复训练需要+1
    # print(f"Rank {rank} loaded checkpoint from {checkpoint_path}, starting from epoch {start_epoch}")


    # else:
    #     checkpoint = None
    #     start_epoch = torch.tensor(0, dtype=torch.int)

    # 同步 epoch 信息

    # print(f"========== 2 ============= load_checkpoint_distributed ===========")
    # if using_dist:
    #     start_epoch_tensor = torch.tensor(start_epoch, dtype=torch.int, device=device)
    #     print(f"========== 3 ============= load_checkpoint_distributed ===========")
    #     dist.broadcast(start_epoch_tensor, src=0)  # 让所有进程获得相同的 start_epoch
    #     print(f"========== 4 ============= load_checkpoint_distributed ===========")

    #     # 让所有进程都加载相同的模型参数

    #     print(f"========== 5 ============= load_checkpoint_distributed ===========")
    #     dist.barrier()
    #     print(f"========== 6 ============= load_checkpoint_distributed ===========")
    #     model.load_state_dict(model.state_dict())
    #     print(f"========== 7 ============= load_checkpoint_distributed ===========")

    # start_epoch = start_epoch_tensor.item()

    return start_epoch
def save_DAG(dag: nx.Graph, path: str, file_name: str) -> None:
    if not os.path.exists(path):
        os.makedirs(path)
    if '.gml' not in file_name:
        file_name += '.gml'
    path = os.path.join(path, "", file_name)
    nx.write_gml(dag, path)
    return


def save_variable(var: object,
                  filename: str,
                  path_save: str) -> None:
    # 检查文件格式后缀
    if '.pckl' not in filename:
        filename += '.pckl'

    # 检查存储路径是否存在
    if not os.path.exists(path_save):
        os.makedirs(path_save)

    # 保存文件
    f = open(path_save + filename, 'wb')
    pickle.dump(var, f)
    f.close()
    return


def read_variable(filename: str,
                  path_load: str) -> object:
    # 检查文件格式后缀
    filename = filename + '.pckl' if '.pckl' not in filename else filename

    # 读取文件
    f = open(path_load + filename, 'rb')
    var = pickle.load(f)
    f.close()
    return var



class Losses():
    gaussian = nn.GaussianNLLLoss(full=True, reduction='none')
    mse = nn.MSELoss(reduction='none')
    def ce(num_classes):
        num_classes = num_classes.shape[0] if torch.is_tensor(num_classes) else num_classes
        return nn.CrossEntropyLoss(reduction='none', weight=torch.ones(num_classes))
    bce = nn.BCEWithLogitsLoss(reduction='none')


def get_encoder(  # noqa: PLR0913
    *,
    num_features: int,
    embedding_size: int,
    remove_empty_features: bool,
    remove_duplicate_features: bool,
    nan_handling_enabled: bool,
    normalize_on_train_only: bool,
    normalize_to_ranking: bool,
    normalize_x: bool,
    remove_outliers: bool,
    normalize_by_used_features: bool,
    encoder_use_bias: bool,
) -> nn.Module:
    inputs_to_merge = {"main": {"dim": num_features}}

    encoder_steps = []
    if remove_empty_features:
        # 移除无效（常量）数据
        encoder_steps += [RemoveEmptyFeaturesEncoderStep()]         

    if remove_duplicate_features:
        # 移除重复特征（未实现）
        encoder_steps += [RemoveDuplicateFeaturesEncoderStep()]

    # NaN处理编码器
    encoder_steps += [NanHandlingEncoderStep(keep_nans=nan_handling_enabled)]

    if nan_handling_enabled:
        inputs_to_merge["nan_indicators"] = {"dim": num_features}

        encoder_steps += [
            # 通过添加零值将输入转换为固定数量的特征，不归一化处理（方差不恒定）
            VariableNumFeaturesEncoderStep(
                num_features=num_features,
                normalize_by_used_features=False,
                in_keys=["nan_indicators"],
                out_keys=["nan_indicators"],
            ),
        ]

    encoder_steps += [
        # 数据归一化
        InputNormalizationEncoderStep(
            normalize_on_train_only=normalize_on_train_only,
            normalize_to_ranking=normalize_to_ranking,
            normalize_x=normalize_x,
            remove_outliers=remove_outliers,
        ),
    ]

    encoder_steps += [
        # 通过添加零值将输入转换为固定数量的特征，归一化处理（方差恒定）
        VariableNumFeaturesEncoderStep(
            num_features=num_features,
            normalize_by_used_features=normalize_by_used_features,
        ),
    ]

    encoder_steps += [
        # 线性编码器，不使用0替换NaN值
        LinearInputEncoderStep(
            num_features=sum([i["dim"] for i in inputs_to_merge.values()]),
            emsize=embedding_size,
            bias=encoder_use_bias,
            in_keys=tuple(inputs_to_merge),
            out_keys=("output",),
        ),
    ]

    return SequentialEncoder(*encoder_steps, output_key="output")


def get_y_encoder(
    *,
    num_inputs: int,
    embedding_size: int,
    nan_handling_y_encoder: bool,
    max_num_classes: int,
) -> nn.Module:
    steps = []
    inputs_to_merge = [{"name": "main", "dim": num_inputs}]
    if nan_handling_y_encoder:
        steps += [NanHandlingEncoderStep()]
        inputs_to_merge += [{"name": "nan_indicators", "dim": num_inputs}]

    if max_num_classes >= 2:
        # 多标签类别编码
        steps += [MulticlassClassificationTargetEncoder()]

    steps += [
        LinearInputEncoderStep(
            num_features=sum([i["dim"] for i in inputs_to_merge]),  # type: ignore
            emsize=embedding_size,
            in_keys=tuple(i["name"] for i in inputs_to_merge),  # type: ignore
            out_keys=("output",),
        ),
    ]
    return SequentialEncoder(*steps, output_key="output")


    
def _preprocess_config(config: dict) -> InferenceConfig:
    '''配置文件容错处理'''
    config["task_type"]
    batch_size = config["batch_size"]
    agg_k_grads = config.get("aggregate_k_gradients")

    if agg_k_grads is None:
        if not math.log(batch_size, 2).is_integer():
            raise ValueError(f"batch_size must be pow of 2, got {config['batch_size']}")

        second_dim_tokens = config.get("num_global_att_tokens ", config["seq_len"])
        memory_factor = (
            batch_size
            * config["nlayers"]
            * config["emsize"]
            * config["seq_len"]
            * second_dim_tokens
        )
        standard_memory_factor = 16 * 12 * 512 * 1200 * 1200
        agg_k_grads = math.ceil(memory_factor / (standard_memory_factor * 1.1))
        config["aggregate_k_gradients"] = agg_k_grads

        # Make sure that batch size is power of two
        config["batch_size"] = int(
            math.pow(2, math.floor(math.log(batch_size / agg_k_grads, 2))),
        )
        config["num_steps"] = math.ceil(config["num_steps"] * agg_k_grads)

        # Make sure that batch_size_per_gp_sample is power of two
        assert math.log(config["batch_size_per_gp_sample"], 2) % 1 == 0

    config.setdefault("recompute_attn", False)
    return InferenceConfig.from_dict(config)

import socket
def get_host_info():
    hostname = socket.gethostname()
    ip_address = socket.gethostbyname(hostname)
    return f"{hostname}-{ip_address}"

def statistic_time(str, data):
    fit_time = 0
    predict_time = 0
    mlp_time = 0
    categorical_time = 0
    decision_tree_all_time = 0
    get_DAG_time = 0
    root_data_generation_time = 0
    generater_data_time = 0
    total_time = 0
    sample_XY_time = 0
    gen_type = 0
    topo_order_time = 0
    get_parent_values_time = 0
    agg_noise_time = 0

    for key, value in data.items():
        if isinstance(value, dict):
            for sub_key, sub_value in value.items():
                if "fit" in sub_key.lower() and "decesiontree" in sub_key.lower():
                    fit_time += sub_value
                elif "predict" in sub_key.lower() and "decesiontree" in sub_key.lower():
                    predict_time += sub_value
                elif "all" in sub_key.lower() and "decesiontree" in sub_key.lower():
                    decision_tree_all_time += sub_value
                elif "get_parent_values" in sub_key.lower():
                    get_parent_values_time += sub_value
                elif "agg_noise" in sub_key.lower():
                    agg_noise_time += sub_value
                elif sub_key == "MLP":
                    mlp_time += sub_value
                elif sub_key == "topo_order":
                    topo_order_time += sub_value
                elif sub_key == "Categorical":
                    categorical_time += sub_value
                elif sub_key == "gat_DAG":
                    get_DAG_time += sub_value
                elif "root_data_generation" in sub_key.lower():
                    root_data_generation_time += sub_value
                    if "normal" in sub_key.lower():
                        gen_type = 0
                    else:
                        gen_type = 1
        else:
            if key == "generater data":
                generater_data_time = value
            elif key == "total":
                total_time = value
            elif key == "sample XY":
                sample_XY_time = value

    fit_time *= 1000
    predict_time *= 1000
    mlp_time *= 1000
    categorical_time *= 1000
    decision_tree_all_time *= 1000
    get_DAG_time *= 1000
    root_data_generation_time *= 1000
    generater_data_time *= 1000
    total_time *= 1000
    sample_XY_time *= 1000
    topo_order_time *= 1000
    get_parent_values_time *= 1000
    agg_noise_time *= 1000

    # labels = [
    #     "total",
    #     "generater data",
    #     "sample XY",
    #     "get_DAG",
    #     "root_data_gen",
    #     "MLP",
    #     "Categorical",
    #     "decesionTree",
    #     "fit",
    #     "predict"
    # ]
    # # 打印标签信息
    # print(" ".join(f"{label:<15}" for label in labels))
    values = [
        total_time,
        generater_data_time,
        sample_XY_time,
        get_DAG_time,
        root_data_generation_time,
        gen_type,
        topo_order_time,
        get_parent_values_time,
        agg_noise_time,
        mlp_time,
        categorical_time,
        decision_tree_all_time,
        fit_time,
        predict_time
    ]

    # 打印值信息
    # Epoch_batch total generater_data sample_XY get_DAG root_data_gen gen_type topo_oder get_parenct agg_noise MLP Categorical decesionTree fit predict
    print(str, " ".join(f"{value:<15.6f}" for value in values))


def visualize_decision_trees(edge_datas, edge_type, save_path = "./", filename = "multiple_trees"):
    def visualize_multiple_trees(tree_array, save_path = "./", filename='multiple_trees'):
        """
        可视化多个决策树在一张图中，根节点标明树的编号。
        
        参数:
            tree_array (np.ndarray): 多棵决策树组成的数组，形状为 (N, num_nodes, 6)
            filename (str): 输出文件名（不带扩展名）
        """
        from graphviz import Digraph
        
        dot = Digraph(format='png')
        
        global_node_counter = 0  # 全局唯一节点编号，避免不同树之间冲突

        for tree_idx, node_array in enumerate(tree_array):
            with dot.subgraph(name=f'cluster_{tree_idx}') as c:
                c.attr(label=f'Tree {tree_idx}')
                c.attr(style='filled', color='lightgrey')
                
                # 使用闭包来携带局部变量
                def add_nodes(local_idx, node_offset):
                    nonlocal global_node_counter
                    node = node_array[local_idx]
                    node_id = f"node{node_offset + local_idx}"  # 唯一ID

                    if node[0] == 1:  # 叶子节点
                        c.node(node_id, f"N: {local_idx}\n{int(node[3])}",
                            shape='ellipse', style='filled', fillcolor='lightblue')
                    else:
                        label = f"N: {local_idx}\nI: {int(node[1])}\nT: {node[2]:.3f}"
                        c.node(node_id, label,
                            shape='ellipse', style='filled', fillcolor='lightyellow')

                        # 递归处理左右子节点
                        if node[4] != -1:
                            add_nodes(int(node[4]), node_offset)
                            c.edge(node_id, f"node{node_offset + int(node[4])}", label="l")
                        if node[5] != -1:
                            add_nodes(int(node[5]), node_offset)
                            c.edge(node_id, f"node{node_offset + int(node[5])}", label="g")

                # 为每棵树分配一个偏移量，确保节点 ID 不冲突
                node_offset = global_node_counter
                add_nodes(0, node_offset)
                global_node_counter += len(node_array)
        
        
        save_name = os.path.join(save_path, filename)
        dot.render(save_name, view=False, cleanup=True)
        
    for edge, info in edge_datas.items():
        if edge_type[edge] == 'tree':
            edge_name = f"{edge[0]}_{edge[1]}"
            visualize_multiple_trees(info, save_path, filename + "_DT_" + edge_name)

def visualize_DAG(dag, node_sampled, edge_type, save_path = "./", filename='dag'):
    from graphviz import Digraph
    
    os.makedirs(save_path, exist_ok=True)
    # 创建一个 Digraph 对象
    dot = Digraph(comment='Directed Acyclic Graph')

    # 设置图形属性
    dot.attr(rankdir='TB', dpi='100')

    # node->idx数组 转 node->idx字典
    node_sampled_x = {}
    node_sampled_y = {}
    for data in node_sampled['x']:
        if data[0] not in node_sampled_x:
            node_sampled_x[data[0]] = []
        node_sampled_x[data[0]].append(data[1])
    
    for data in node_sampled['y']:
        if data[0] not in node_sampled_y:
            node_sampled_y[data[0]] = []
        node_sampled_y[data[0]].append(data[1])
    
    # 添加节点到 Digraph
    for node in dag.nodes():
        fillcolor = '#FFFFFF'
        node_text = f"N: {node}"
        if node in node_sampled_x:
            fillcolor = '#FFFF00'
            node_text = node_text + "\nX:" + str(node_sampled_x[node])
        if node in node_sampled_y:
            fillcolor = '#FF8000'
            node_text = node_text + "\nY" + str(node_sampled_y[node])
        dot.node(str(node), shape='ellipse', label=node_text, style='filled', fillcolor=fillcolor)
        
    paths = []
    for x_node in node_sampled_x:
        for y_node in node_sampled_y:
            if nx.has_path(dag, x_node, y_node):
                for path in nx.all_simple_paths(dag, source=x_node, target=y_node):
                    paths.append(path)
     # 标记路径上的边
    special_edges = set()
    for path in paths:
        for i in range(len(path) - 1):
            special_edges.add((path[i], path[i + 1]))
            
    # 添加边到 Digraph
    for u, v in dag.edges():
        key = (str(u), str(v))
        edge_text = edge_type[key]
        if (u, v) in special_edges:
            dot.edge(str(u), str(v), label=edge_text, color='red')
        else:
            dot.edge(str(u), str(v), label=edge_text, color="black")

    # 渲染并保存为png
    save_name = os.path.join(save_path, filename)
    dot.render(save_name, view=False, format='png', cleanup=True)
    
    
    

def train(priordataloader_class, criterion, encoder_generator, emsize=200, nhid=200, nlayers=6, nhead=2, dropout=0.0,
          epochs=10, steps_per_epoch=100, batch_size=200, bptt=10, lr=None, weight_decay=0.0, start_epoch = 1, warmup_epochs=10, input_normalization=False,
          y_encoder_generator=None, pos_encoder_generator=None, decoder=None, extra_prior_kwargs_dict={}, scheduler=get_cosine_schedule_with_warmup,
          load_weights_from_this_state_dict=None, validation_period=10, single_eval_pos_gen=None, bptt_extra_samples=None, gpu_device='cuda:0',
          aggregate_k_gradients=1, verbose=True, style_encoder_generator=None, epoch_callback=None,
          initializer=None, initialize_with_model=None, train_mixed_precision=False, efficient_eval_masking=True, project_name='default_project',
          **model_extra_args
          ):
    import datetime
    now = datetime.datetime.now()
    time_str = now.strftime("%m%d-%H%M")
    time_mini = now.strftime("%m%d%H")

    # 调用函数获取本地唯一标识
    host_info = get_host_info()
    print("project: ", f"{project_name}_{host_info}_{time_str}")

    device = gpu_device if torch.cuda.is_available() else 'cpu:0'
    print(f'Using {device} device')
    # 分布式初始化
    using_dist, rank, device = init_dist(device)
    single_eval_pos_gen = single_eval_pos_gen if callable(single_eval_pos_gen) else lambda: single_eval_pos_gen     # 生成max_eval_pos至min_eval_pos的随机整数

    task_name = model_extra_args['task_name']
    
    # start_epoch = model_extra_args['start_epoch']
    config_info = extra_prior_kwargs_dict['config_info']
    
    if not config_info['debug'] and rank == 0:
        print("== use wandb ==")
        # init wandb
        # login wandb
        # 添加异步日志设置
        os.environ['WANDB_START_METHOD'] = 'thread'
        os.environ['WANDB_SILENT'] = 'true'             # 减少非关键日志
        os.environ['WANDB_ERROR_REPORTING'] = 'false'   # 禁用错误报告
        api_key = model_extra_args['wandb_api_key']
        wandb.login(key=api_key)
        wandb.init(
            project=f"{project_name}",  # projectname
            name=f"{task_name}_{rank}",  # 实验名称
            group=f"{task_name}",  # 分组名称
            job_type="training",  # 任务类型
            tags=["flash_attn"],  # 标签 "baseline", "flash_attn", "fp16", "fp16_flash_attn"
            config={
                "emsize": emsize,
                "nhid": nhid,
                "nlayers": nlayers,
                'layer_dropout':model_extra_args['min_num_layers_layer_dropout'] if 'min_num_layers_layer_dropout' in model_extra_args else -1,
                "nhead": nhead,
                "dropout": dropout,
                "epochs": epochs,
                "steps_per_epoch": steps_per_epoch,
                "batch_size": batch_size,
                "bptt": bptt,
                "lr": lr,
                "weight_decay": weight_decay,
                "warmup_epochs": warmup_epochs,
                "input_normalization": input_normalization,
                "aggregate_k_gradients": aggregate_k_gradients,
                "train_mixed_precision": train_mixed_precision,
                "efficient_eval_masking": efficient_eval_masking,
                "total_dataset": model_extra_args['total_dataset'],
                "GPU_num": model_extra_args['GPU_num'],
                "batch_sigle": model_extra_args['batch_sigle'],
                "update_num": model_extra_args['update_num'],
                **model_extra_args,
                **config_info
            }
        )

    # 生成bptt，赋值给seq_len
    def eval_pos_seq_len_sampler():
        single_eval_pos = single_eval_pos_gen()
        if bptt_extra_samples:
            return single_eval_pos, single_eval_pos + bptt_extra_samples
        else:
            return single_eval_pos, bptt
    dl = priordataloader_class(
        num_steps=steps_per_epoch, 
        num_workers=4,  # 根据桶数量设置工作线程数
        batch_size=batch_size,
        prefetch_factor=64,  # 添加预取因子
        persistent_workers=True,  # 保持工作进程存活
        eval_pos_seq_len_sampler=eval_pos_seq_len_sampler,
        seq_len_maximum=bptt+(bptt_extra_samples if bptt_extra_samples else 0),
        device=device,
        **extra_prior_kwargs_dict
    )
    # encoder = encoder_generator(dl.num_features, emsize)
    #style_def = dl.get_test_batch()[0][0] # the style in batch of the form ((style, x, y), target, single_eval_pos)
    style_def = None
    #print(f'Style definition of first 3 examples: {style_def[:3] if style_def is not None else None}')
    style_encoder = style_encoder_generator(style_def.shape[1], emsize) if (style_def is not None) else None
    if isinstance(criterion, nn.GaussianNLLLoss):
        n_out = 2
    elif isinstance(criterion, nn.CrossEntropyLoss):
        n_out = criterion.weight.shape[0]
    else:
        n_out = 1

    # model = TransformerModel(encoder, n_out, emsize, nhead, nhid, nlayers, dropout, style_encoder=style_encoder,
    #                          y_encoder=y_encoder_generator(1, emsize), input_normalization=input_normalization,
    #                          pos_encoder=(pos_encoder_generator or positional_encodings.NoPositionalEncoding)(emsize, bptt*2),
    #                          decoder=decoder, init_method=initializer, efficient_eval_masking=efficient_eval_masking, **model_extra_args
    #                          )
    config = torch.load(open('tabpfn-v2-classifier.ckpt', 'rb'))['config']
    config = _preprocess_config(config)

    layer_dropout = None
    if model_extra_args['min_num_layers_layer_dropout'] is not None:
        if rank == 0:
            random_hyperparameter = torch.randint(
                    low=model_extra_args['min_num_layers_layer_dropout'],
                    high=config.nlayers + 1,
                    size=(1,),
                    dtype=torch.long
                ).to(device)
        else:
            random_hyperparameter = torch.empty(1, dtype=torch.long, device=device)

        # 广播随机超参数到所有进程
        dist.broadcast(random_hyperparameter, src=0)

        # 打印每个进程中的随机超参数
        # print(f"Rank {rank}: Random hyperparameter = {random_hyperparameter.item()}")

        layer_dropout = random_hyperparameter.item() # 保留多少layers
    
    model = PerFeatureTransformer(
        seed=0,
        # Things that were explicitly passed inside `build_model()`
        encoder=get_encoder(
            num_features=config.features_per_group,
            embedding_size=config.emsize,
            remove_empty_features=config.remove_empty_features,
            remove_duplicate_features=config.remove_duplicate_features,
            nan_handling_enabled=config.nan_handling_enabled,
            normalize_on_train_only=config.normalize_on_train_only,
            normalize_to_ranking=config.normalize_to_ranking,
            normalize_x=config.normalize_x,
            remove_outliers=config.remove_outliers,
            normalize_by_used_features=config.normalize_by_used_features,
            encoder_use_bias=config.encoder_use_bias,
        ),
        y_encoder=get_y_encoder(
            num_inputs=1,
            embedding_size=config.emsize,
            nan_handling_y_encoder=config.nan_handling_y_encoder,
            max_num_classes=config.max_num_classes,
        ),
        nhead=config.nhead,
        ninp=config.emsize,
        nhid=config.emsize * config.nhid_factor,
        nlayers=config.nlayers,
        features_per_group=config.features_per_group,
        cache_trainset_representation=False,
        #
        # Based on not being present in config or otherwise, these were default values
        init_method=None,
        decoder_dict={"standard": (None, n_out)},
        use_encoder_compression_layer=False,
        #
        # These were extra things passed in through `**model_extra_args`
        # or `**extra_model_kwargs` and were present in the config
        recompute_attn=config.recompute_attn,
        recompute_layer=config.recompute_layer,
        feature_positional_embedding=config.feature_positional_embedding,
        use_separate_decoder=config.use_separate_decoder,
        #
        # These are things that had default values from config.get() but were not
        # present in any config.
        layer_norm_with_elementwise_affine=False,
        nlayers_decoder=None,
        pre_norm=False,
        #
        # These seem to map to `**layer_config` in the init of `PerFeatureTransformer`
        # Which got passed to the `PerFeatureEncoderLayer(**layer_config)`
        multiquery_item_attention=config.multiquery_item_attention,  # False
        multiquery_item_attention_for_test_set=config.multiquery_item_attention_for_test_set,  # True  # noqa: E501
        # Is either 1.0 or None in the configs, which lead to the default of 1.0 anywho
        attention_init_gain=(
            config.attention_init_gain
            if config.attention_init_gain is not None
            else 1.0
        ),
        # Is True, False in the config or not present,
        # with the default of the `PerFeatureEncoderLayer` being False,
        # which is what the value would have mapped to if the config had not present
        two_sets_of_queries=(
            config.two_sets_of_queries
            if config.two_sets_of_queries is not None
            else False
        ),
        dropout_p=dropout,
        min_num_layers_layer_dropout = layer_dropout,
    )
    model.criterion = criterion
    if load_weights_from_this_state_dict is not None:
        model.load_state_dict(load_weights_from_this_state_dict[0])
    if initialize_with_model is not None:
        model.init_from_small_model(initialize_with_model)

    print(f"Using a Transformer with {sum(p.numel() for p in model.parameters())/1000/1000:.{2}f} M parameters")

    try:
        for (k, v), (k2, v2) in zip(model.state_dict().items(), initialize_with_model.state_dict().items()):
            print(k, ((v - v2) / v).abs().mean(), v.shape)
    except Exception:
        pass

    model.to(device)
    if using_dist:
        print("Distributed training")
        check_unused_param = True if layer_dropout is not None else False
        model = torch.nn.parallel.DistributedDataParallel(model, device_ids=[rank], output_device=rank, broadcast_buffers=False, find_unused_parameters=check_unused_param)
    dl.dataset.model = model 

    # learning rate
    if lr is None:
        lr = get_openai_lr(model)
        print(f"Using OpenAI max lr of {lr}.")

    eps = 1e-5 if train_mixed_precision else 1e-8
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay, eps=eps)

    scheduler = scheduler(optimizer, warmup_epochs, epochs if epochs is not None else 100) # when training for fixed time lr schedule takes 100 steps

    if load_weights_from_this_state_dict is not None:
        optimizer.load_state_dict(load_weights_from_this_state_dict[1])
        scheduler.load_state_dict(load_weights_from_this_state_dict[2])
    scaler = None
    if(train_mixed_precision):
        scaler = GradScaler()
        if load_weights_from_this_state_dict is not None:
            scaler.load_state_dict(load_weights_from_this_state_dict[3])
    print(f"\033[32m==++== start epoch: {start_epoch}, lr: {optimizer.param_groups[0]['lr']}\033[0m")
    


    # check that everything uses up-to-date APIs
    utils.check_compatibility(dl)


    def train_epoch(cur_epoch):

        if config_info['profiler_on'] == 1 and cur_epoch == 1 :
            prof = torch.profiler.profile(
                activities=[
                    torch.profiler.ProfilerActivity.CPU,
                    torch.profiler.ProfilerActivity.CUDA
                ],
                schedule=torch.profiler.schedule(wait=1, warmup=1, active=3, repeat=1),
                on_trace_ready=torch.profiler.tensorboard_trace_handler(f"./logs/{project_name}"),
                record_shapes=True,
                profile_memory=True,
                with_stack=True,
                with_flops=True,
                with_modules=True,
                experimental_config=None
            )
            prof.start()
        else:
            prof = None

        model.train()  # Turn on the train mode
        total_loss = torch.tensor(0.).to(device)
        total_positional_losses = 0.
        total_positional_losses_recorded = 0
        nan_steps = 0
        ignore_steps = 0
        before_get_batch = time.time()
        step_times = []
        batch_times = []
        forward_times = []
        backward_times = []
        losses_times = []
        update_times = []
        assert len(dl) % aggregate_k_gradients == 0, 'Please set the number of steps per epoch s.t. `aggregate_k_gradients` divides it.'
        for batch,  all_datas in tqdm(enumerate(dl), total=len(dl)) if config_info['debug'] else enumerate(dl):
        # for batch,  all_datas in tqdm(enumerate(dl), total=len(dl)):
            # print(f"train [{batch}]")
            if prof is not None:
                prof.step()
            data, targets, single_eval_pos, mlp_dicts = all_datas
            data = tuple(e.to(device) if torch.is_tensor(e) else e for e in data)
            targets = data[2].clone()
            batch_times.append(time.time() - before_get_batch)
            # print(f"== {data[1].shape}")

            before_forward = time.time()

            # 注意这里的single_eval_pos后面都没用到，是又重新生成了
            # print('\n','------'*10)
            # print(batch, data[1].shape, targets.shape, single_eval_pos)
            # print(data[1].shape,data[2].shape, targets.shape)
            if using_dist and not (batch % aggregate_k_gradients == aggregate_k_gradients - 1):
                cm = model.no_sync()
            else:
                cm = nullcontext()
            with cm:
                # 修改为使用dl返回的single_eval_pos
                # if bptt_extra_samples is None:
                #     single_eval_pos = single_eval_pos_gen() if callable(single_eval_pos_gen) else single_eval_pos_gen
                # else:
                #     single_eval_pos = targets.shape[0] - bptt_extra_samples
                # print('->', single_eval_pos)
                with autocast(enabled=scaler is not None):
                    # If style is set to None, it should not be transferred to device
                    output = model(data, single_eval_pos=single_eval_pos)
                    forward_times.append(time.time() - before_forward)
                    before_loss = time.time()
                    if single_eval_pos is not None:
                        targets = targets[single_eval_pos:]
                    if isinstance(criterion, nn.GaussianNLLLoss):
                        assert output.shape[-1] == 2, \
                            'need to write a little bit of code to handle multiple regression targets at once'

                        mean_pred = output[..., 0]
                        var_pred = torch.nn.functional.softplus(output[..., 1]) + 1e-6
                        losses = criterion(mean_pred.flatten(), targets.to(device).flatten(), var=var_pred.flatten())
                    elif isinstance(criterion, (nn.MSELoss, nn.BCEWithLogitsLoss)):
                        losses = criterion(output.flatten(), targets.to(device).flatten())
                    elif isinstance(criterion, nn.CrossEntropyLoss):
                        # lofc = nn.CrossEntropyLoss(reduction='none')
                        # los1 = lofc(output.reshape(-1, n_out), targets.long().flatten())
                        losses = criterion(output.reshape(-1, n_out), targets.long().flatten())
                        # los2 = lofc(output.reshape(-1, n_out), targets.long().flatten())

                        # l1 = losses.mean().item()
                        # l2 = los1.mean().item()
                        # l3 = los2.mean().item()
                        # if(abs(l1 - l2) > 0.001 or abs(l1 - l3) > 0.001):
                        #     print(f"losses: {l1}, los1: {l2}, los2: {l3}  error!!!!!!!!!!")
                        # else:
                        #     # print(f"losses: {l1}, los1: {l2}, los2: {l3}")
                        #     pass


                    else:
                        losses = criterion(output, targets)
                    losses = losses.view(*output.shape[0:2])
                    loss, nan_share = utils.torch_nanmean(losses.mean(0), return_nanshare=True)
                    # 修改为使用 clone() 方法复制 loss 张量
                    losses = loss.clone().detach()
                    loss = loss / aggregate_k_gradients
                    losses_times.append(time.time()-before_loss)
                before_backward = time.time()
                if scaler: loss = scaler.scale(loss)
                loss.backward()
                backward_times.append(time.time() - before_backward)
                before_update = time.time()
                if batch % aggregate_k_gradients == aggregate_k_gradients - 1:
                    if scaler: scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), 1.)
                    try:
                        if scaler:
                            scaler.step(optimizer)
                            scaler.update()
                        else:
                            optimizer.step()
                    except:
                        print("Invalid optimization step encountered")
                    optimizer.zero_grad()

                # if not torch.isnan(loss): # 这一步也挺开销时间的
                total_loss += losses
                    # total_positional_losses += losses.mean(1).cpu().detach() if single_eval_pos is None else \
                    #     nn.functional.one_hot(torch.tensor(single_eval_pos), bptt)*\
                    #     losses[:bptt-single_eval_pos].mean().cpu().detach()

                    # total_positional_losses_recorded += torch.ones(bptt) if single_eval_pos is None else \
                    #     nn.functional.one_hot(torch.tensor(single_eval_pos), bptt)


                update_times.append(time.time() - before_update)
                nan_steps += nan_share
                ignore_steps += (targets == -100).float().mean()
                step_times.append(time.time() - before_get_batch)
                # torch.cuda.synchronize()
                before_get_batch = time.time()

            # 保存SCM信息
            if model_extra_args['save_scm'] == True:
                path_save = model_extra_args['path_save_scm']
                for fidx, sample in enumerate(mlp_dicts):
                    file_name_prefix = f'epoch_{epoch}_batch_{batch}_sample_{fidx}'
                    dags = sample['dag']
                    
                    for idx_dag in dags:
                        save_DAG(dags[idx_dag], path_save, file_name_prefix + f'_dag_{idx_dag}')
                        visualize_DAG(dags[idx_dag], sample['info']['node_sampled'][idx_dag], sample['info']['edge_type'][idx_dag], path_save, f"dag_{file_name_prefix}")
                        visualize_decision_trees(sample['info']['edge_datas'][idx_dag], sample['info']['edge_type'][idx_dag], path_save, f"dag_{file_name_prefix}")
                    save_variable(sample['info'], file_name_prefix + '_info', path_save)

            # if rank == 0:
            #     with open(f"logs/{epoch}_{batch}.json", 'w', encoding='utf-8') as f:
            #         json.dump(mlp_dicts[0]['time_info'], f, ensure_ascii=False, indent=4)
            #     statistic_time(f"{epoch}_{batch}", mlp_dicts[0]['time_info'])

        if prof is not None:
            prof.stop()
        step_time = np.mean(step_times)
        batch_time = np.mean(batch_times)
        forward_time = np.mean(forward_times)
        backward_time = np.mean(backward_times)
        losses_time = np.mean(losses_times)
        update_time = np.mean(update_times)
        times_log = {'step_time':step_time, 'batch_time':batch_time, 'forward_time':forward_time,
                     'backward_time':backward_time, 'losses_time':losses_time, 'update_time':update_time}

        return total_loss.cpu().item() / len(dl), nan_steps.cpu().item()/(batch+1), ignore_steps.cpu().item()/(batch+1), times_log

    total_loss = float('inf')
    total_positional_losses = float('inf')
    train_history_info = []

    epochs = epochs if epochs is not None else itertools.count(1)
    epoch = start_epoch
    last_auc = 0
    try:
        cur_epoch = 0
        while epoch <= epochs:
        # for epoch in (range(start_epoch, epochs + 1) if epochs is not None else itertools.count(1)):
            # TODO mock model evaluation
            # if epoch_callback is not None and rank == 0:
            #     epoch_callback(model, optimizer, scheduler, scaler, epoch)
            # end mock
            
            epoch_start_time = time.time()
            cur_epoch = cur_epoch + 1
            total_loss, nan_share, ignore_share, times_log = \
                train_epoch(cur_epoch)
            step_time, batch_time, forward_time, backward_time, losses_time, update_time = times_log
            if hasattr(dl, 'validate') and epoch % validation_period == 0:
                with torch.no_grad():
                    val_score = dl.validate(model)
            else:
                val_score = None

            if verbose:
                print('-' * 89)
                print(
                    f'| end of epoch {epoch:3d} | epoch time: {(time.time() - epoch_start_time):5.2f}s | mean loss {total_loss:5.2f} | '
                    + (f'val score {val_score}' if val_score is not None else '')+f"| curr_lr: {optimizer.param_groups[0]['lr']:.8f}")
                # time_string = [f'{k}: {v:.3f}s' for k, v in times_log.items()]
                # time_string = ' | '.join(time_string)
                # print(time_string)
                print('-' * 89)

            scheduler.step()

            # stepping with wallclock time based scheduler
            save_same = None
            auc = None
            if epoch_callback is not None and rank == 0:
                ext_info = None
                save_same, auc = epoch_callback(model, optimizer, scheduler, scaler, epoch, ext_info)
                if auc:
                    last_auc = auc
            
            
            # 记录指标到 wandb
            if not config_info['debug'] and rank==0:
                wandb.log({
                    "epoch": epoch,
                    "mean_loss": total_loss,
                    "val_score": val_score if val_score is not None else 0,
                    "step_time": times_log['step_time'],
                    "batch_time": times_log['batch_time'],
                    "nan_share": nan_share,
                    "ignore_share": ignore_share,
                    'curr_lr':optimizer.param_groups[0]['lr'],
                    'auc': last_auc
                })


            # mock
            # total_loss *= 0.2
            # if len(train_history_info) > 3:
            #     total_loss = 1.5
            #     print("==++== start mock boom")
            # else:
            #     print(f"==++== normal mock, len: {len(train_history_info)}")

            if save_same:
                train_history_info.append({
                    "epoch": epoch,
                    "mean_loss": total_loss,
                    "val_score": val_score if val_score is not None else 0,
                    "auc": auc,
                    "save_name": save_same
                })
            if epoch > warmup_epochs and total_loss > 1 and len(train_history_info) > 0:
                # 查找最近的正常loss的checkpoint
                last_infos = train_history_info[-50:]
                last_infos_valid = [info for info in last_infos if info['mean_loss'] < 1]

                print(f"== epoch {epoch} loss boom! try to resume from last 10 epochs ==")
                print(f"last 10 epochs info: {last_infos_valid}")
                if len(last_infos_valid) > 0:
                    mean_loss_sum = sum(info['mean_loss'] for info in last_infos_valid)
                    mean_loss_avg = mean_loss_sum / len(last_infos_valid)

                    result_elements = [element for element in last_infos_valid if element["mean_loss"] <= mean_loss_avg and element["mean_loss"] < 1]

                    if len(result_elements) > 0:
                        checkpoint_name = result_elements[-1]["save_name"]
                        epoch = load_checkpoint_distributed(model=model, optimizer=optimizer, scheduler=scheduler, scaler=scaler, checkpoint_path=checkpoint_name, rank=rank, using_dist=using_dist, device=device)
                        print(f"== resume frome checkpoint: {checkpoint_name}, new epoch: {epoch} ==")

            epoch += 1
    except KeyboardInterrupt:
        pass

    if not config_info['debug'] and rank==0:
        # 结束 wandb 运行
        wandb.finish()

    if rank == 0: # trivially true for non-parallel training
        if isinstance(model, torch.nn.parallel.DistributedDataParallel):
            model = model.module
            dl = None
        return total_loss, total_positional_losses, model.to('cpu'), dl

    

def _parse_args(config_parser, parser):
    # Do we have a config file to parse?
    args_config, remaining = config_parser.parse_known_args()
    if args_config.config:
        with open(args_config.config, 'r') as f:
            cfg = yaml.safe_load(f)
            parser.set_defaults(**cfg)

    # The main arg parser parses the rest of the args, the usual
    # defaults will have been overridden if config file specified.
    args = parser.parse_args(remaining)

    # Cache the args as a text string to save them in the output dir later
    args_text = yaml.safe_dump(args.__dict__, default_flow_style=False)
    return args, args_text


if __name__ == '__main__':
    config_parser = argparse.ArgumentParser(description='Only used as a first parser for the config file path.')
    config_parser.add_argument('--config')
    parser = argparse.ArgumentParser()
    parser.add_argument('prior')
    parser.add_argument('--loss_function', default='barnll')
    # Optional Arg's for `--loss_function barnll`
    parser.add_argument('--min_y', type=float, help='barnll can only model y in strict ranges, this is the minimum y can take.')
    parser.add_argument('--max_y', type=float, help='barnll can only model y in strict ranges, this is the maximum y can take.')
    parser.add_argument('--num_buckets', default=100, type=int)
    #parser.add_argument('--num_features', default=None, type=int, help='Specify depending on the prior.')
    parser.add_argument("--extra_prior_kwargs_dict", default={}, dest="extra_prior_kwargs_dict", action=StoreDictKeyPair, nargs="+", metavar="KEY=VAL", help='Specify depending on the prior.')
    parser.add_argument('--encoder', default='linear', type=str, help='Specify depending on the prior.')
    parser.add_argument('--y_encoder', default='linear', type=str, help='Specify depending on the prior. You should specify this if you do not fuse x and y.')
    parser.add_argument('--pos_encoder', default='none', type=str, help='Specify depending on the prior.')
    parser.add_argument('--bptt', default=10, type=int)
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--warmup_epochs', default=50, type=int)
    parser.add_argument('--validation_period', default=10, type=int)
    parser.add_argument('--permutation_invariant_max_eval_pos', default=None, type=int, help='Set this to an int to ')
    parser.add_argument('--permutation_invariant_sampling', default='weighted', help="Only relevant if --permutation_invariant_max_eval_pos is set.")
    parser.add_argument('--train_mixed_precision', action='store_true')

    # these can likely be mostly left at defaults
    parser.add_argument('--emsize', default=512, type=int) # sometimes even larger is better e.g. 1024
    parser.add_argument('--nlayers', default=6, type=int)
    parser.add_argument('--nhid', default=None, type=int) # 2*emsize is the default
    parser.add_argument('--nhead', default=4, type=int) # nhead = emsize / 64 in the original paper
    parser.add_argument('--dropout', default=.0, type=float)
    parser.add_argument('--steps_per_epoch', default=10, type=int)
    parser.add_argument('--batch_size', default=1000, type=int)
    parser.add_argument('--lr', '--learning_rate', default=.001, type=float) # try also .0003, .0001, go lower with lower batch size

    args, _ = _parse_args(config_parser, parser)

    if args.nhid is None:
        args.nhid = 2*args.emsize

    prior = args.__dict__.pop('prior')

    if prior == 'gp':
        prior = priors.fast_gp.DataLoader
    elif prior == 'ridge':
        prior = priors.ridge.DataLoader
    elif prior == 'stroke':
        prior = priors.stroke.DataLoader
    elif prior == 'mix_gp':
        prior = priors.fast_gp_mix.DataLoader
    else:
        raise NotImplementedError(f'Prior == {prior}.')

    loss_function = args.__dict__.pop('loss_function')

    criterion = nn.GaussianNLLLoss(reduction='none', full=True)
    classificiation_criterion = nn.CrossEntropyLoss(reduction='none')
    num_buckets = args.__dict__.pop('num_buckets')
    max_y = args.__dict__.pop('max_y')
    min_y = args.__dict__.pop('min_y')
    # criterion = nn.MSELoss(reduction='none')

    if loss_function == 'ce':
        criterion = nn.CrossEntropyLoss(reduction='none')
    elif loss_function == 'gaussnll':
        criterion = nn.GaussianNLLLoss(reduction='none', full=True)
    elif loss_function == 'mse':
        criterion = nn.MSELoss(reduction='none')
    else:
        raise NotImplementedError(f'loss_function == {loss_function}.')



    encoder = args.__dict__.pop('encoder')
    y_encoder = args.__dict__.pop('y_encoder')

    def get_encoder_generator(encoder):
        if encoder == 'linear':
            encoder_generator = encoders.Linear
        elif encoder == 'mlp':
            encoder_generator = encoders.MLP
        elif encoder == 'positional':
            encoder_generator = encoders.Positional
        else:
            raise NotImplementedError(f'A {encoder} encoder is not valid.')
        return encoder_generator

    encoder_generator = get_encoder_generator(encoder)
    y_encoder_generator = get_encoder_generator(y_encoder)

    pos_encoder = args.__dict__.pop('pos_encoder')

    if pos_encoder == 'none':
        pos_encoder_generator = None
    elif pos_encoder == 'sinus':
        pos_encoder_generator = positional_encodings.PositionalEncoding
    elif pos_encoder == 'learned':
        pos_encoder_generator = positional_encodings.LearnedPositionalEncoding
    elif pos_encoder == 'paired_scrambled_learned':
        pos_encoder_generator = positional_encodings.PairedScrambledPositionalEncodings
    else:
        raise NotImplementedError(f'pos_encoer == {pos_encoder} is not valid.')

    permutation_invariant_max_eval_pos = args.__dict__.pop('permutation_invariant_max_eval_pos')
    permutation_invariant_sampling = args.__dict__.pop('permutation_invariant_sampling')
    if permutation_invariant_max_eval_pos is not None:
        if permutation_invariant_sampling == 'weighted':
            get_sampler = get_weighted_single_eval_pos_sampler
        elif permutation_invariant_sampling == 'uniform':
            get_sampler = get_uniform_single_eval_pos_sampler
        else:
            raise ValueError()
        args.__dict__['single_eval_pos_gen'] = get_sampler(permutation_invariant_max_eval_pos)


    print("ARGS for `train`:", args.__dict__)

    train(prior, criterion, encoder_generator,
          y_encoder_generator=y_encoder_generator, pos_encoder_generator=pos_encoder_generator,
          **args.__dict__)

