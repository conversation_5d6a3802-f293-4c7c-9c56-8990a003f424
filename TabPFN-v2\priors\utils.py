import random

import torch

from utils import set_locals_in_self, normalize_by_used_features_f
from .prior import PriorDataLoader
from torch import nn
from torch.utils.data import Dataset, DataLoader
import numpy as np
import scipy.stats as stats
import math
from typing import *
import types

import random


class PriorDataset(Dataset):
    def __init__(self, get_batch_method, **get_batch_kwargs):
        self.get_batch_method = get_batch_method
        self.get_batch_kwargs = get_batch_kwargs
        self.model = None  # 将被外部赋值
        

    def gbm(self, *args, eval_pos_seq_len_sampler, **kwargs):
        kwargs['single_eval_pos'], kwargs['seq_len'] = eval_pos_seq_len_sampler()
        # Scales the batch size dynamically with the power of 'dynamic_batch_size'.
        # A transformer with quadratic memory usage in the seq len would need a power of 2 to keep memory constant.
        # if 'dynamic_batch_size' in kwargs and kwargs['dynamic_batch_size'] > 0 and kwargs['dynamic_batch_size']:
        #     kwargs['batch_size'] = kwargs['batch_size'] * math.floor(math.pow(kwargs['seq_len_maximum'], kwargs['dynamic_batch_size']) / math.pow(kwargs['seq_len'], kwargs['dynamic_batch_size']))
        kwargs['batch_size'] = 1 # 仅用于接口兼容，实际上每次生成1条数据，然后用DataLoader控制batch size ? 也许这里一次生成多个batch会比较快？
        batch = self.get_batch_method(*args, **kwargs)
        if len(batch) == 4:
            x, y, target_y, style = batch
            dicts = None
        elif len(batch) == 3:
            x, y, target_y = batch
            style = None
            dicts = None
        else:
            x, y, target_y, style, dicts = batch
            if dicts:
                try:
                    dicts = dicts[0][0][0]
                except:
                    dicts = None
        return (style, x, y), target_y, kwargs['single_eval_pos'], dicts

    def __len__(self):
        return self.get_batch_kwargs['num_steps']
    
    def __getitem__(self, idx):
        return self.gbm(**self.get_batch_kwargs)


def get_batch_to_dataloader(get_batch_method_):
    num_features_list = [20, 40, 60,   80,   100,  120,  160, 180, 200, 260, 300]
    # seq_len_list      = [8000, 4600, 3000, 2300, 1850, 1550, 1150, 1024, 920, 740]
    # seq_len_list      = [7200, 4140, 2700, 2070, 1665, 1395, 1035, 921, 828, 666]
    seq_len_list      = [6400, 3600, 2400, 1840, 1480, 1240, 920, 819, 736, 592, 540]
    feature_dict = dict(zip(num_features_list, seq_len_list))
    
    def get_max_seq_len(num_features:int, batch_size:int=2, memory:int=24):
        # 直接匹配的情况
        if num_features in feature_dict:
            return feature_dict[num_features]
        
        # 线性查找第一个大于目标值的元素
        i = 0
        while i < len(num_features_list) and num_features_list[i] <= num_features:
            i += 1
        
        # 处理边界情况
        if i == 0:
            return seq_len_list[0]  # 小于最小值
        if i == len(num_features_list):
            return seq_len_list[-1]  # 大于最大值
        
        # 获取相邻值进行插值
        upper_num, upper_sl = num_features_list[i-1], seq_len_list[i-1]
        lower_num, lower_sl = num_features_list[i], seq_len_list[i]
        ratio = (num_features - upper_num) / (lower_num - upper_num)
        base_len = upper_sl + (lower_sl - upper_sl) * ratio
        max_len = int(base_len * 2 / batch_size * memory / 24)
        
        return max_len
    
    def collate_fn(batch):
        # 保持原有数据格式 (style, x, y), target_y, single_eval_pos
        # print(len(batch))
        # print(type(batch[0]), type(batch[1]))
        # b1 = batch[0]
        # print(b1[0][1].shape, b1[0][2].shape, b1[1].shape, b1[2])
        # b1 = batch[1]
        # print(b1[0][1].shape, b1[0][2].shape, b1[1].shape, b1[2])
        # styles = batch[:, 0, 0]
        # xs = batch[:, 0, 1]
        # ys = batch[:, 0, 2].
        
        # targets = batch[:, 1]
        # single_eval_pos = batch[:, 2]
        
        init_kwargs = collate_fn.init_kwargs  # 访问闭包变量

        # styles = [item[0][0] for item in batch]
        xs = [item[0][1] for item in batch]
        ys = [item[0][2] for item in batch]
        targets = [item[1] for item in batch]
        dicts = []
        for item in batch:
            if item[3] is not None:
                if isinstance(item[3], tuple):
                    dicts.append(item[3][0])
                elif isinstance(item[3], dict):
                    dicts.append(item[3])
            else:
                dicts.append(None)
        
        # single_eval_pos = [item[2] for item in batch]
        # (styles, xs, ys), targets = zip(*batch)
        # single_eval_pos = random.choice(range(len(ys[0])))  # 保持原有抽样逻辑
        # xs = torch.stack(xs).squeeze(2).transpose(0, 1)#.to(torch.float16)#.contiguous()
        # ys = torch.stack(ys).squeeze(2).transpose(0, 1)#.to(torch.float16)#.contiguous()
        # targets = torch.stack(targets).squeeze(2).transpose(0, 1)#.contiguous()
        
        # 同batch的数据扩展至最大feature_num值
        feature_num = [item.shape[2] for item in xs]
        max_feature_num = max(feature_num)
        min_feature_num = min(feature_num)
        
        def get_fill_data(shape, device, fill_type):
            if fill_type == 'zero':
                return torch.zeros(shape).to(device)
            elif fill_type == 'gaussian_noise':
                return torch.normal(0, 2, shape).to(device)
            else:
                raise ValueError('Invalid fill_type')
        
        new_feature_num = max_feature_num
        if init_kwargs['hyperparameters']['padding_type'] == 'min_num':
            new_feature_num = min_feature_num
        elif init_kwargs['hyperparameters']['padding_type'] =='max_num':
            new_feature_num = max_feature_num
        elif init_kwargs['hyperparameters']['padding_type'] == 'random':
            new_feature_num = random.randint(min_feature_num, max_feature_num)
        
        single_eval_pos = batch[0][2]
        if(init_kwargs['hyperparameters']['enable_auto_seq_len'] and new_feature_num > 10) or init_kwargs['hyperparameters']['bptt_use_uniform']:
            max_seq_len = get_max_seq_len(new_feature_num, len(batch))
            if init_kwargs['hyperparameters']['bptt_use_uniform']:
                cur_seq_len = random.randint(init_kwargs['hyperparameters']['bptt_min'], init_kwargs['hyperparameters']['bptt'])
                new_seq_len = min(max_seq_len, cur_seq_len)
            else:
                new_seq_len = max_seq_len
            if(xs[0].shape[0] > new_seq_len):
                for i in range(len(xs)):
                    xs[i] = xs[i][:new_seq_len, :, :]
                    ys[i] = ys[i][:new_seq_len, :]
                    targets[i] = targets[i][:new_seq_len, :]
                    # print(f"==++== new_feature_num: {new_feature_num}, 超过最大seq_len, 裁剪数据至： {max_seq_len}, new shape: x: {xs[i].shape}, y: {ys[i].shape}, target: {targets[i].shape}")
                
                max_pos = int(new_seq_len * init_kwargs['hyperparameters']['max_eval_pos_factor'])
                min_pos = int(new_seq_len * init_kwargs['hyperparameters']['min_eval_pos_factor'])
                single_eval_pos = random.randint(min_pos, max_pos)
        
        for i in range(len(xs)):
            if xs[i].shape[2] < new_feature_num:
                # xs[i] = torch.cat([xs[i], torch.zeros((xs[i].shape[0], xs[i].shape[1], max_feature_num - xs[i].shape[2])).to(xs[i].device)], dim=2)
                if init_kwargs['hyperparameters']['normalize_by_used_features'] and init_kwargs['hyperparameters']['padding_fill_type'] == 'zero':
                    xs[i] = normalize_by_used_features_f(xs[i], num_features_used=xs[i].shape[2], num_features=new_feature_num, normalize_with_sqrt=init_kwargs['hyperparameters'].get('normalize_with_sqrt', False))
                xs[i] = torch.cat([xs[i], get_fill_data((xs[i].shape[0], xs[i].shape[1], new_feature_num - xs[i].shape[2]), xs[i].device, init_kwargs['hyperparameters']['padding_fill_type'])], dim=2)
            elif xs[i].shape[2] > new_feature_num:
                xs[i] = xs[i][:, :, :new_feature_num]
        
        
        
        # 随机选择一个single_eval_pos，并做train的y值是否覆盖valid的y值的判断，train.py使用返回的single_eval_pos做训练
        # 校验single_eval_pos是否在合理范围内（sqe_len修改后，可能导致越界问题）,否则重新生成
        
            
        if init_kwargs['hyperparameters']['check_is_compatible']:
            for i in range(len(ys)):
                for b in range(ys[i].shape[1]):
                    # 计算data的均值和标准差
                    # 确保训练集和验证集包含相同类别
                    # 计算n_sigma倍的标准差
                    is_compatible, N = False, 0
                    # 计算上下限
                    while not is_compatible and N < 10:
                        targets_in_train = torch.unique(ys[i][:single_eval_pos, b], sorted=True)
                        # 创建一个掩码，用于标记data中不在上下限范围内的值
                        targets_in_eval = torch.unique(ys[i][single_eval_pos:, b], sorted=True)
                        is_compatible = len(targets_in_train) == len(targets_in_eval) and (
                                    targets_in_train == targets_in_eval).all() and len(targets_in_train) > 1

                        if not is_compatible:
                            randperm = torch.randperm(xs[i].shape[0])
                            xs[i][:, b], ys[i][:, b] = xs[i][randperm, b], ys[i][randperm, b]
                        N = N + 1
                    if not is_compatible:
                        # todo check that it really does this and how many together
                        ys[i][:, b] = -100 # Relies on CE having `ignore_index` set to -100 (default)
        if init_kwargs['hyperparameters']['normalize_labels']:
            # 标签标准化
            for i in range(len(ys)):
                #assert self.h['output_multiclass_ordered_p'] == 0., "normalize_labels destroys ordering of labels anyways."
                for b in range(ys[i].shape[1]):
                    valid_labels = ys[i][:,b] != -100
                    if init_kwargs['hyperparameters'].get('normalize_ignore_label_too', False):     # 是否将忽略的标签也参与标签序列化
                        valid_labels[:] = True
                    # 将标签转换为排序索引
                    ys[i][valid_labels, b] = (ys[i][valid_labels, b] > ys[i][valid_labels, b].unique().unsqueeze(1)).sum(axis=0).unsqueeze(0).float()

                    if ys[i][valid_labels, b].numel() != 0 and init_kwargs['hyperparameters'].get('rotate_normalized_labels', True):
                        # 随机循环偏移标签
                        num_classes_float = (ys[i][valid_labels, b].max() + 1).cpu()
                        num_classes = num_classes_float.int().item()
                        assert num_classes == num_classes_float.item()
                        random_shift = torch.randint(0, num_classes, (1,), device=ys[i].device)
                        ys[i][valid_labels, b] = (ys[i][valid_labels, b] + random_shift) % num_classes
        # 1、同batch的数据扩展至最大feature_num值  Done
        # 2、随机选择一个single_eval_pos，并做train的y值是否覆盖valid的y值的判断，train.py使用返回的single_eval_pos做训练  Done
        # 3、校验train的y值是否覆盖valid的y值，若不覆盖对y的对应标签赋-100  Done
        # 4、在3的基础上，进行标签循环偏移标签处理  Done
        
        xs = torch.stack(xs).squeeze(2).transpose(0, 1)#.to(torch.float16)#.contiguous()
        ys = torch.stack(ys).squeeze(2).transpose(0, 1)#.to(torch.float16)#.contiguous()
        targets = torch.stack(targets).squeeze(2).transpose(0, 1)#.contiguous()

        # print(torch.stack(xs).shape)
        # return (None, torch.stack(xs), torch.stack(ys)), torch.stack(targets), single_eval_pos
        return (None, xs, ys), ys, single_eval_pos, dicts
    
    class PriorDataLoader(DataLoader):
        def __init__(self, **kwargs):
            collate_fn.init_kwargs = kwargs  # 注入初始化参数
            dataset = PriorDataset(get_batch_method_, **kwargs)
            super().__init__(
                dataset,
                batch_size=kwargs['batch_size'],  # Dataset处理批量生成
                collate_fn=collate_fn,
                num_workers=kwargs['num_workers']
            )
            # self.device = kwargs['device']
            
    return PriorDataLoader

        # num_steps=steps_per_epoch,
        # batch_size=batch_size,
        # eval_pos_seq_len_sampler=eval_pos_seq_len_sampler,
        # seq_len_maximum=bptt+(bptt_extra_samples if bptt_extra_samples else 0),
        # device=device,
        # **extra_prior_kwargs_dict
    
# def get_batch_to_dataloader(get_batch_method_):
#     class DL(DataLoader):
#         get_batch_method = get_batch_method_

#         # Caution, you might need to set self.num_features manually if it is not part of the args.
#         def __init__(self, num_steps, **get_batch_kwargs):
#             set_locals_in_self(locals())

#             # The stuff outside the or is set as class attribute before instantiation.
#             self.num_features = get_batch_kwargs.get('num_features') or self.num_features
#             self.epoch_count = 0
#             #print('DataLoader.__dict__', self.__dict__)

#         @staticmethod
#         def gbm(*args, eval_pos_seq_len_sampler, **kwargs):
#             kwargs['single_eval_pos'], kwargs['seq_len'] = eval_pos_seq_len_sampler()
#             # Scales the batch size dynamically with the power of 'dynamic_batch_size'.
#             # A transformer with quadratic memory usage in the seq len would need a power of 2 to keep memory constant.
#             if 'dynamic_batch_size' in kwargs and kwargs['dynamic_batch_size'] > 0 and kwargs['dynamic_batch_size']:
#                 kwargs['batch_size'] = kwargs['batch_size'] * math.floor(math.pow(kwargs['seq_len_maximum'], kwargs['dynamic_batch_size']) / math.pow(kwargs['seq_len'], kwargs['dynamic_batch_size']))
#             batch = get_batch_method_(*args, **kwargs)
#             x, y, target_y, style = batch if len(batch) == 4 else (batch[0], batch[1], batch[2], None)
#             return (style, x, y), target_y, kwargs['single_eval_pos']

#         def __len__(self):
#             return self.num_steps

#         def get_test_batch(self): # does not increase epoch_count
#             return self.gbm(**self.get_batch_kwargs, epoch=self.epoch_count, model=self.model if hasattr(self, 'model') else None)

#         def __iter__(self):
#             assert hasattr(self, 'model'), "Please assign model with `dl.model = ...` before training."
#             self.epoch_count += 1
#             return iter(self.gbm(**self.get_batch_kwargs, epoch=self.epoch_count - 1, model=self.model) for _ in range(self.num_steps))

#     return DL


def plot_features(data, targets, fig=None, categorical=True):
    import seaborn as sns
    import matplotlib.pyplot as plt
    import matplotlib.gridspec as gridspec
    if torch.is_tensor(data):
        data = data.detach().cpu().numpy()
        targets = targets.detach().cpu().numpy()
    #data = np.concatenate([data, np.expand_dims(targets, -1)], -1)
    #df = pd.DataFrame(data, columns=list(range(0, data.shape[1])))
    #g = sns.pairplot(df, hue=data.shape[1]-1, palette="Set2", diag_kind="kde", height=2.5)
    #plt.legend([], [], frameon=False)
    #g._legend.remove()
    #g = sns.PairGrid(df, hue=data.shape[1]-1)
    #g.map_diag(sns.histplot)
    #g.map_offdiag(sns.scatterplot)
    #g._legend.remove()

    fig2 = fig if fig else plt.figure(figsize=(8, 8))
    spec2 = gridspec.GridSpec(ncols=data.shape[1], nrows=data.shape[1], figure=fig2)
    for d in range(0, data.shape[1]):
        for d2 in range(0, data.shape[1]):
            if d > d2:
                continue
            sub_ax = fig2.add_subplot(spec2[d, d2])
            sub_ax.set_xticks([])
            sub_ax.set_yticks([])
            if d == d2:
                if categorical:
                    sns.kdeplot(data[:, d],hue=targets[:],ax=sub_ax,legend=False, palette="deep")
                else:
                    sns.kdeplot(data[:, d], ax=sub_ax, legend=False)
                sub_ax.set(ylabel=None)
            else:
                if categorical:
                    sns.scatterplot(x=data[:, d], y=data[:, d2],
                           hue=targets[:],legend=False, palette="deep")
                else:
                    sns.scatterplot(x=data[:, d], y=data[:, d2],
                                    hue=targets[:], legend=False)
                #plt.scatter(data[:, d], data[:, d2],
                #               c=targets[:])
            #sub_ax.get_xaxis().set_ticks([])
            #sub_ax.get_yaxis().set_ticks([])
    plt.subplots_adjust(wspace=0.05, hspace=0.05)
    fig2.show()


def plot_prior(prior):
    import matplotlib.pyplot as plt
    s = np.array([prior() for _ in range(0, 1000)])
    count, bins, ignored = plt.hist(s, 50, density=True)
    print(s.min())
    plt.show()

trunc_norm_sampler_f = lambda mu, sigma, size=1 : lambda: stats.truncnorm((0 - mu) / sigma, (1000000 - mu) / sigma, loc=mu, scale=sigma).rvs(1)[0]
beta_sampler_f = lambda a, b, size=1 : lambda : np.random.beta(a, b)
gamma_sampler_f = lambda a, b, size=1 : lambda : np.random.gamma(a, b)
uniform_sampler_f = lambda a, b, size=1 : lambda : np.random.uniform(a, b)
uniform_int_sampler_f = lambda a, b, size=1 : lambda : round(np.random.uniform(a, b))
normal_sampler_f = lambda mu, std: lambda : np.random.normal(mu, std)
randint_sampler = lambda low, high, size=1: lambda: np.random.randint(low, high)


def log_uniform_sampling(low: float, 
                         high: float, 
                         size: Union[Tuple, int]=1
                         ) -> types.FunctionType:
    
    def sampling(low, high, size) -> float:
        """
        生成 log-uniform 分布的样本
        Args:
            low (float): 采样下限（正数）
            high (float): 采样上限（正数）
            size (int/tuple): 输出张量的形状
        Returns:
            torch.Tensor: 采样结果
        """
        # 生成均匀分布样本并转换到对数区间
        uniform_samples = np.random.rand(size) * (high - low) + low
        # 对结果取指数，得到 log-uniform 分布
        samples = np.exp(uniform_samples)
        return samples
    
    return lambda : sampling(low, high, size)


beta_sampler_range = lambda a,b : lambda min, max: np.random.beta(a, b) * (max - min) + min

def get_threshold_func_beta(a,b):
    return np.random.beta(0.95, 8) * (b - a) + a

def zipf_sampler_f(a, b, c):
    x = np.arange(b, c)
    weights = x ** (-a)
    weights /= weights.sum()
    return lambda : stats.rv_discrete(name='bounded_zipf', values=(x, weights)).rvs(1)
scaled_beta_sampler_f = lambda a, b, scale, minimum : lambda : minimum + round(beta_sampler_f(a, b)() * (scale - minimum))

def order_by_y(x, y):
    order = torch.argsort(y if random.randint(0, 1) else -y, dim=0)[:, 0, 0]
    order = order.reshape(2, -1).transpose(0, 1).reshape(-1)#.reshape(seq_len)
    x = x[order]  # .reshape(2, -1).transpose(0, 1).reshape(-1).flip([0]).reshape(seq_len, 1, -1)
    y = y[order]  # .reshape(2, -1).transpose(0, 1).reshape(-1).reshape(seq_len, 1, -1)

    return x, y

def randomize_classes(x, num_classes):
    classes = torch.arange(0, num_classes, device=x.device)
    random_classes = torch.randperm(num_classes, device=x.device).type(x.type())
    x = ((x.unsqueeze(-1) == classes) * random_classes).sum(-1)
    return x


class CategoricalActivation(nn.Module):
    def __init__(self, categorical_p=0.1, ordered_p=0.7
                 , keep_activation_size=False
                 , num_classes_sampler=zipf_sampler_f(0.8, 1, 10)):
        self.categorical_p = categorical_p
        self.ordered_p = ordered_p
        self.keep_activation_size = keep_activation_size
        self.num_classes_sampler = num_classes_sampler

        super().__init__()

    def forward(self, x):
        # x shape: T, B, H
        # 将激活值压缩到(-1,1)范围
        x = nn.Softsign()(x)
        # 生成类别数量，这里固定未20
        num_classes = self.num_classes_sampler()
        hid_strength = torch.abs(x).mean(0).unsqueeze(0) if self.keep_activation_size else None

        # 对选中的特征进行离散化（概率由categorical_p控制）
        categorical_classes = torch.rand((x.shape[1], x.shape[2])) < self.categorical_p
        # 为每个特征生成分类边界
        class_boundaries = torch.zeros((num_classes - 1, x.shape[1], x.shape[2]), device=x.device, dtype=x.dtype)
        # Sample a different index for each hidden dimension, but shared for all batches
        for b in range(x.shape[1]):
            for h in range(x.shape[2]):
                # # 随机选择分割点
                ind = torch.randint(0, x.shape[0], (num_classes - 1,))
                class_boundaries[:, b, h] = x[ind, b, h]        # 记录分割值

        # 将连续值转换为离散分类（通过边界比较）
        for b in range(x.shape[1]):
            x_rel = x[:, b, categorical_classes[b]]
            boundaries_rel = class_boundaries[:, b, categorical_classes[b]].unsqueeze(1)
            # 计算每个值超过的边界数，并中心化处理
            x[:, b, categorical_classes[b]] = (x_rel > boundaries_rel).sum(dim=0).float() - num_classes / 2

        # 对部分分类进行顺序随机化（概率由ordered_p控制）
        ordered_classes = torch.rand((x.shape[1],x.shape[2])) < self.ordered_p
        ordered_classes = torch.logical_and(ordered_classes, categorical_classes)
        x[:, ordered_classes] = randomize_classes(x[:, ordered_classes], num_classes)

        x = x * hid_strength if self.keep_activation_size else x

        return x
